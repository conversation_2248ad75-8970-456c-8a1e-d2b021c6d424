package dataAll._player.time
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   
   public class TimeSave
   {
      
      public static var pro_arr:Array = [];
      
      public static var doubleArr:Array = ["doubleExpTime","doubleArmsDropTime","doubleEquipDropTime","doubleMaterialsDropTime"];
      
      public static var doubleCnArr:Array = ["双倍经验时间","双倍武器掉率时间","双倍装备掉率时间","双倍材料掉率时间"];
      
      public static const weekStartTime:StringDate = new StringDate("2021-8-2 00:00:00");
      
      public static const week6Time:StringDate = new StringDate("2022-10-22 00:00:00");
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var prevReadTime:String = "";
      
      public var nowReadTime:String = "";
      
      public function TimeSave()
      {
         super();
         this.doubleExpTime = 0;
         this.doubleArmsDropTime = 0;
         this.doubleEquipDropTime = 0;
         this.doubleMaterialsDropTime = 0;
         this.weekIndex = 0;
      }
      
      public function get doubleExpTime() : Number
      {
         return this.CF.getAttribute("doubleExpTime");
      }
      
      public function set doubleExpTime(v0:Number) : void
      {
         this.CF.setAttribute("doubleExpTime",v0);
      }
      
      public function get doubleArmsDropTime() : Number
      {
         return this.CF.getAttribute("doubleArmsDropTime");
      }
      
      public function set doubleArmsDropTime(v0:Number) : void
      {
         this.CF.setAttribute("doubleArmsDropTime",v0);
      }
      
      public function get doubleEquipDropTime() : Number
      {
         return this.CF.getAttribute("doubleEquipDropTime");
      }
      
      public function set doubleEquipDropTime(v0:Number) : void
      {
         this.CF.setAttribute("doubleEquipDropTime",v0);
      }
      
      public function get doubleMaterialsDropTime() : Number
      {
         return this.CF.getAttribute("doubleMaterialsDropTime");
      }
      
      public function set doubleMaterialsDropTime(v0:Number) : void
      {
         this.CF.setAttribute("doubleMaterialsDropTime",v0);
      }
      
      public function get weekIndex() : Number
      {
         return this.CF.getAttribute("weekIndex");
      }
      
      public function set weekIndex(v0:Number) : void
      {
         this.CF.setAttribute("weekIndex",v0);
      }
      
      public function get week6() : Number
      {
         return this.CF.getAttribute("week6");
      }
      
      public function set week6(v0:Number) : void
      {
         this.CF.setAttribute("week6",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function getNowReadData() : StringDate
      {
         var d0:StringDate = new StringDate();
         d0.inData_byStr(this.nowReadTime);
         return d0;
      }
      
      public function getPrevReadData() : StringDate
      {
         var d0:StringDate = new StringDate();
         d0.inData_byStr(this.prevReadTime);
         return d0;
      }
      
      public function getDayGap() : int
      {
         var now0:StringDate = this.getNowReadData();
         var prev0:StringDate = this.getPrevReadData();
         return prev0.compareDate(now0);
      }
      
      public function clearAllTime() : void
      {
         this.doubleExpTime = 0;
         this.doubleArmsDropTime = 0;
         this.doubleEquipDropTime = 0;
         this.doubleMaterialsDropTime = 0;
      }
   }
}

