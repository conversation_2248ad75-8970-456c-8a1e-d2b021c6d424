package dataAll.drop.define
{
   public class DropColorDefineGroup
   {
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var body:BodyColorDefine = new BodyColorDefine();
      
      public var arms:ArmsColorDefine = new ArmsColorDefine();
      
      public var equip:EquipColorDefine = new EquipColorDefine();
      
      public function DropColorDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         this.name = xml0.@name;
         this.cnName = xml0.@cnName;
         this.body.inData_byXML(xml0.body[0]);
         this.arms.inData_byXML(xml0.arms[0]);
         this.equip.inData_byXML(xml0.equip[0]);
      }
      
      public function copy(name0:String, cn0:String) : DropColorDefineGroup
      {
         var d0:DropColorDefineGroup = new DropColorDefineGroup();
         d0.name = name0;
         d0.cnName = cn0;
         d0.body = this.body;
         d0.arms = this.arms;
         d0.equip = this.equip;
         return d0;
      }
   }
}

