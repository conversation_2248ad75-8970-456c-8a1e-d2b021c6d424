package dataAll.body.attack
{
   import gameAll.scene.weather.WeatherData;
   
   public class ElementHurt
   {
      
      public static const no:String = "";
      
      public static const fire:String = "fire";
      
      public static const electric:String = "electric";
      
      public static const frozen:String = "frozen";
      
      public static const poison:String = "poison";
      
      public static const arr:Array = [fire,electric,frozen,poison];
      
      public static const uiArr:Array = [frozen,electric,fire,poison];
      
      private static var weatherObj:Object = {
         "fire":1,
         "electric":1,
         "frozen":1,
         "poison":1
      };
      
      private static var gemNameArr:Array = [];
      
      private static var defineObj:Object = {};
      
      public function ElementHurt()
      {
         super();
      }
      
      public static function defineInit() : void
      {
         var ename0:String = null;
         var d0:ElementHurtDefine = null;
         d0 = new ElementHurtDefine();
         d0.name = fire;
         d0.cnName = "火焰";
         d0.breakShell = ElementShell.compound;
         d0.defenceShell = ElementShell.metal;
         d0.gatherColor = "redness";
         defineObj[d0.name] = d0;
         d0 = new ElementHurtDefine();
         d0.name = electric;
         d0.cnName = "电磁";
         d0.breakShell = ElementShell.metal;
         d0.defenceShell = ElementShell.normal;
         d0.gatherColor = "purpleness";
         defineObj[d0.name] = d0;
         d0 = new ElementHurtDefine();
         d0.name = frozen;
         d0.cnName = "冷冻";
         d0.breakShell = ElementShell.variation;
         d0.defenceShell = ElementShell.compound;
         d0.gatherColor = "blove";
         defineObj[d0.name] = d0;
         d0 = new ElementHurtDefine();
         d0.name = poison;
         d0.cnName = "生化";
         d0.breakShell = ElementShell.normal;
         d0.defenceShell = ElementShell.variation;
         d0.gatherColor = "green";
         defineObj[d0.name] = d0;
         for each(ename0 in arr)
         {
            gemNameArr.push(ename0 + "Gem");
         }
      }
      
      public static function isElementGem(name0:String) : Boolean
      {
         return gemNameArr.indexOf(name0) >= 0;
      }
      
      public static function getCn(name0:String) : String
      {
         var d0:ElementHurtDefine = getDefine(name0);
         if(Boolean(d0))
         {
            return d0.cnName;
         }
         return "";
      }
      
      public static function getCnArr(nameArr0:Array) : Array
      {
         var name0:String = null;
         var cnArr0:Array = [];
         for each(name0 in nameArr0)
         {
            cnArr0.push(getCn(name0));
         }
         return cnArr0;
      }
      
      public static function getDefine(name0:String) : ElementHurtDefine
      {
         return defineObj[name0];
      }
      
      public static function getBreakShell(name0:String) : String
      {
         var d0:ElementHurtDefine = getDefine(name0);
         if(Boolean(d0))
         {
            return d0.breakShell;
         }
         return ElementShell.other;
      }
      
      public static function getHurtByShell(shell0:String) : ElementHurtDefine
      {
         var name0:String = null;
         var d0:ElementHurtDefine = null;
         for each(name0 in arr)
         {
            d0 = getDefine(name0);
            if(d0.breakShell == shell0)
            {
               return d0;
            }
         }
         return null;
      }
      
      public static function countHurtMul(element0:String, shell0:String, eleArr0:Array = null) : Number
      {
         var ele0:String = null;
         var v2:Number = NaN;
         var v0:Number = getHurtMul(element0,shell0);
         if(Boolean(eleArr0))
         {
            for each(ele0 in eleArr0)
            {
               v2 = getHurtMul(ele0,shell0);
               if(v2 > v0)
               {
                  v0 = v2;
               }
            }
         }
         return v0;
      }
      
      private static function getHurtMul(element0:String, shell0:String) : Number
      {
         var w0:Number = NaN;
         var v0:Number = 0;
         var d0:ElementHurtDefine = defineObj[element0];
         if(d0.breakShell == shell0)
         {
            v0 = 1;
         }
         else if(d0.defenceShell == shell0)
         {
            v0 = 0;
         }
         else
         {
            v0 = 0.3;
         }
         if(v0 > 0)
         {
            if(element0 != "")
            {
               w0 = Number(weatherObj[element0]);
               v0 *= w0;
            }
         }
         return v0;
      }
      
      public static function initWeatherState() : void
      {
         var n:* = undefined;
         for(n in weatherObj)
         {
            weatherObj[n] = 1;
         }
      }
      
      public static function inWeatherData(w0:WeatherData) : void
      {
         var n:* = undefined;
         for(n in weatherObj)
         {
            weatherObj[n] *= 1 + w0.getHurtEle(n);
         }
      }
   }
}

