package dataAll.gift.dailySign
{
   public class DailyGiftDefineGroup
   {
      
      public var arr:Array = [];
      
      public var obj:Object = {};
      
      public function DailyGiftDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var barXML0:XML = null;
         var d0:DailyGiftDefine = null;
         var xml_list0:XMLList = xml0.bar;
         for(i in xml_list0)
         {
            barXML0 = xml_list0[i];
            d0 = new DailyGiftDefine();
            d0.inData_byXML(barXML0);
            this.arr.push(d0);
            this.obj[d0.mustNum] = d0;
         }
      }
      
      public function getByMustNum(num0:int) : DailyGiftDefine
      {
         return this.obj[num0];
      }
      
      public function getNameArr() : Array
      {
         var n:* = undefined;
         var d0:DailyGiftDefine = null;
         var arr0:Array = [];
         for(n in this.arr)
         {
            d0 = this.arr[n];
            arr0.push(d0.getName());
         }
         return arr0;
      }
      
      public function getCnNameArr() : Array
      {
         var n:* = undefined;
         var d0:DailyGiftDefine = null;
         var arr0:Array = [];
         for(n in this.arr)
         {
            d0 = this.arr[n];
            arr0.push(d0.cnName);
         }
         return arr0;
      }
   }
}

