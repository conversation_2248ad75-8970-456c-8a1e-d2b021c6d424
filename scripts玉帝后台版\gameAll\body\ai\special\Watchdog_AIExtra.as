package gameAll.body.ai.special
{
   import dataAll.level.LevelDiffGetting;
   import gameAll.body.IO_NormalBody;
   
   public class Watchdog_AIExtra extends AIExtra
   {
      
      private static var teleportStart:int = 8;
      
      private static var teleportEnd:int = 77;
      
      private static var moveStart:int = 11;
      
      private static var moveEnd:int = 21;
      
      private static var laserFirst:int = moveEnd + 1;
      
      private static var laserStart:int = 45;
      
      private static var laserEnd:int = 65;
      
      private static var laserOver:int = 72;
      
      private static var laserMaxT:Number = 2.5;
      
      private var laser_t:Number = 0;
      
      private var teleportY:Number = 0;
      
      private var headCanHitB:Boolean = false;
      
      public var laserLoopB:Boolean = false;
      
      public function Watchdog_AIExtra()
      {
         super();
      }
      
      public function getHeadCanHitB() : Boolean
      {
         return this.headCanHitB;
      }
      
      private function playerLaserAttack() : void
      {
         var f0:int = _img.nowMc.currentFrame;
         if(f0 == laserOver)
         {
            if(this.laserLoopB)
            {
               _img.gotoPlayFrame(moveStart);
            }
         }
         this.headCanHitB = false;
         if(f0 >= laserStart && f0 <= laserEnd)
         {
            this.headCanHitB = true;
         }
         if(f0 == teleportStart - 1)
         {
            this.teleportY = Gaming.sceneGroup.map.anyShap.getMinY(_mot.x,_mot.y - 30);
         }
         else if(f0 >= teleportStart && f0 <= teleportEnd)
         {
            _mot.y = this.teleportY - 300;
         }
         else if(f0 == teleportEnd)
         {
            _mot.y = this.teleportY;
         }
         if(this.laserLoopB)
         {
            if(Gaming.LG.isWinB())
            {
               this.stopLaser();
            }
         }
      }
      
      private function laserAttack() : void
      {
         var followB0:Boolean = false;
         var f0:int = _img.nowMc.currentFrame;
         this.headCanHitB = false;
         _ai.attackAI.canChooseAHDB = false;
         if(f0 >= moveStart && f0 <= moveEnd)
         {
            followB0 = this.followTarget(3);
            if(followB0)
            {
               _img.gotoPlayFrame(laserFirst);
            }
            else if(f0 == moveEnd)
            {
               _img.gotoPlayFrame(moveStart);
            }
            this.laser_t = 0;
         }
         if(f0 >= laserStart && f0 <= laserEnd)
         {
            this.headCanHitB = true;
            this.laser_t += 1 / 30;
            if(this.laser_t >= laserMaxT)
            {
               _img.gotoPlayFrame(laserEnd + 1);
            }
            else if(f0 == laserEnd)
            {
               _img.gotoPlayFrame(laserStart);
            }
            this.followTarget(this.getSpeedMul());
         }
         if(f0 == laserOver)
         {
            if(this.laserLoopB)
            {
               _img.gotoPlayFrame(moveStart);
            }
         }
         if(f0 == teleportStart - 1)
         {
            this.teleportY = Gaming.sceneGroup.map.anyShap.getMinY(_mot.x,_mot.y - 30);
         }
         else if(f0 >= teleportStart && f0 <= teleportEnd)
         {
            _mot.y = this.teleportY - 300;
         }
         else if(f0 == teleportEnd)
         {
            _mot.y = this.teleportY;
         }
      }
      
      private function getSpeedMul() : Number
      {
         var lifePer0:Number = _dat.getLifePer();
         var v0:Number = 1;
         if(lifePer0 >= 0.4)
         {
            v0 = 1;
         }
         else if(lifePer0 >= 0.2)
         {
            v0 = 1.3;
         }
         else
         {
            v0 = 1.5;
         }
         if(LevelDiffGetting.getDiff() >= 6)
         {
            v0 += 0.4;
         }
         return v0;
      }
      
      public function stopLaser() : void
      {
         var f0:int = 0;
         if(_img.nowLabel == "laserAttack")
         {
            f0 = _img.nowMc.currentFrame;
            if(f0 < laserOver)
            {
               _img.gotoPlayFrame(laserOver);
               this.laserLoopB = false;
            }
         }
      }
      
      private function followTarget(speedMul0:Number = 1) : Boolean
      {
         var tx0:Number = NaN;
         var x0:Number = NaN;
         var t0:IO_NormalBody = _ai.attackAI.targetBody;
         if(Boolean(t0))
         {
            _mot.out_maxStateMul = speedMul0;
            tx0 = Number(t0.getMot().x);
            x0 = _mot.x;
            if(x0 >= tx0 - 30 && x0 <= tx0 + 30)
            {
               _act.toStop();
               return true;
            }
            if(x0 > tx0)
            {
               _act.moveToLeft();
            }
            else
            {
               _act.moveToRight();
            }
         }
         return false;
      }
      
      override public function FTimer() : void
      {
         super.FTimer();
         _mot.out_maxStateMul = 1;
         _ai.attackAI.canChooseAHDB = true;
         if(_img.nowLabel == "laserAttack")
         {
            if(_dat.playerCtrlB)
            {
               this.playerLaserAttack();
            }
            else
            {
               this.laserAttack();
            }
         }
      }
   }
}

