package dataAll.things.creator
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ObjectMethod;
   import com.sounto.utils.StringMethod;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.things.define.ThingsDefine;
   
   public class ThingsSemltCreator
   {
      
      public function ThingsSemltCreator()
      {
         super();
      }
      
      public static function getThingsMax() : int
      {
         return 6;
      }
      
      public static function getThingsMin() : int
      {
         return 2;
      }
      
      public static function getMinMaxError(len0:int) : String
      {
         var inMax0:int = getThingsMax();
         var inMin0:int = getThingsMin();
         if(len0 < inMin0)
         {
            return "至少要放入" + inMin0 + "种物品";
         }
         if(len0 > inMax0)
         {
            return "最多可放入" + inMax0 + "种物品";
         }
         return "";
      }
      
      public static function getSingle(g0:GiftAddDefineGroup, num0:int) : GiftAddDefineGroup
      {
         var newG0:GiftAddDefineGroup = null;
         if(num0 < 1)
         {
            return null;
         }
         if(num0 == 1)
         {
            newG0 = g0;
         }
         else
         {
            newG0 = g0.clone();
            newG0.addNumMul(1 / num0,false,true);
         }
         return newG0;
      }
      
      public static function smeltNum(g0:GiftAddDefineGroup, num0:int) : GiftAddDefineGroup
      {
         var d0:GiftAddDefine = null;
         var newG0:GiftAddDefineGroup = getSingle(g0,num0);
         var back0:GiftAddDefineGroup = new GiftAddDefineGroup();
         for(var i:int = 0; i < num0; i++)
         {
            d0 = smelt(newG0);
            back0.mergeOne(d0);
         }
         return back0;
      }
      
      private static function smelt(g0:GiftAddDefineGroup, backObj0:Object = null) : GiftAddDefine
      {
         var gd0:GiftAddDefine = null;
         var td0:ThingsDefine = null;
         var ranName0:String = null;
         var ranD0:ThingsDefine = null;
         var num0:int = 0;
         var giftD0:GiftAddDefine = null;
         var sumPrice1:Number = 0;
         var tdObj0:Object = {};
         for each(gd0 in g0.arr)
         {
            td0 = Gaming.defineGroup.things.getDefine(gd0.name);
            if(td0.smeltD.havePriceB)
            {
               sumPrice1 += td0.smeltD.price * gd0.num;
            }
            tdObj0[td0.name] = td0;
         }
         sumPrice1 = Math.round(sumPrice1 / 2);
         if(sumPrice1 > 200)
         {
            sumPrice1 = 200;
         }
         var gradeObj0:Object = getGradeObj(tdObj0);
         var tdarr0:Array = findThingsNameArr(gradeObj0,sumPrice1,tdObj0);
         if(tdarr0.length > 0)
         {
            if(Boolean(backObj0))
            {
               inBackObj(backObj0,tdarr0,g0);
            }
            ranName0 = ArrayMethod.getRandomOne(tdarr0);
            ranD0 = Gaming.defineGroup.things.getDefine(ranName0);
            num0 = getSmeltNum(g0,ranName0);
            if(num0 > 0)
            {
               giftD0 = new GiftAddDefine();
               giftD0.inData_byStr("things;" + ranD0.name + ";" + num0);
               giftD0.tipB = tdarr0.length >= 2;
               return giftD0;
            }
            return null;
         }
         return null;
      }
      
      private static function inBackObj(backObj0:Object, tdarr0:Array, g0:GiftAddDefineGroup) : void
      {
         var things0:String = null;
         var num0:int = 0;
         for each(things0 in tdarr0)
         {
            num0 = getSmeltNum(g0,things0);
            backObj0[things0] = num0;
         }
      }
      
      private static function getSmeltNum(g0:GiftAddDefineGroup, ranName0:String) : int
      {
         var max0:Number = NaN;
         var ranD0:ThingsDefine = Gaming.defineGroup.things.getDefine(ranName0);
         var sumPrice2:Number = countSumPrice(g0,ranD0.smeltD.type);
         var num0:int = sumPrice2 / ranD0.smeltD.price;
         if(num0 > 0)
         {
            max0 = ranD0.smeltD.maxNum;
            if(max0 > 0 && num0 > max0)
            {
               num0 = max0;
            }
            return num0;
         }
         return 0;
      }
      
      private static function countSumPrice(g0:GiftAddDefineGroup, setType0:String) : Number
      {
         var gd0:GiftAddDefine = null;
         var td0:ThingsDefine = null;
         var sumPrice1:Number = 0;
         for each(gd0 in g0.arr)
         {
            td0 = Gaming.defineGroup.things.getDefine(gd0.name);
            if(td0.smeltD.havePriceB)
            {
               sumPrice1 += td0.smeltD.getPrice(setType0) * gd0.num;
            }
         }
         sumPrice1 = Math.round(sumPrice1 / 2);
         if(sumPrice1 > 200)
         {
            sumPrice1 = 200;
         }
         return sumPrice1;
      }
      
      private static function getGradeObj(tdObj0:Object) : Object
      {
         var td0:ThingsDefine = null;
         var type0:String = null;
         var lv0:int = 0;
         var arr0:Array = null;
         var obj0:Object = {};
         for each(td0 in tdObj0)
         {
            type0 = td0.smeltD.type;
            if(type0 != "")
            {
               lv0 = td0.smeltD.grade;
               arr0 = obj0[lv0];
               if(!arr0)
               {
                  arr0 = [];
                  obj0[lv0] = arr0;
               }
               if(arr0.indexOf(type0) == -1)
               {
                  arr0.push(type0);
               }
            }
         }
         return obj0;
      }
      
      private static function findThingsNameArr(gradeObj0:Object, sumPrice1:Number, removeThingsObj0:Object) : Array
      {
         var garr0:Array = null;
         var type0:String = null;
         var tdarr0:Array = null;
         var allThingsArr0:Array = [];
         for(var i:int = 5; i >= 0; i--)
         {
            garr0 = gradeObj0[i];
            if(Boolean(garr0))
            {
               for each(type0 in garr0)
               {
                  tdarr0 = getGradeThingsNameArr(type0,i,sumPrice1,removeThingsObj0);
                  allThingsArr0 = allThingsArr0.concat(tdarr0);
               }
               if(allThingsArr0.length > 0)
               {
                  break;
               }
            }
         }
         return allThingsArr0;
      }
      
      private static function getGradeThingsNameArr(type0:String, grade0:int, sumPrice1:Number, removeThingsObj0:Object) : Array
      {
         var marr0:Array = null;
         var d0:ThingsDefine = null;
         var name0:String = null;
         var obj0:Object = Gaming.defineGroup.things.smeltObj;
         var arr0:Array = obj0[type0];
         var tarr0:Array = [];
         if(Boolean(arr0))
         {
            marr0 = [];
            for each(d0 in arr0)
            {
               if(d0.smeltD.grade == grade0)
               {
                  if(d0.smeltD.price <= sumPrice1)
                  {
                     marr0.push(d0.name);
                  }
               }
            }
            if(marr0.length > 0)
            {
               for each(name0 in marr0)
               {
                  d0 = Gaming.defineGroup.things.getDefine(name0);
                  if(!removeThingsObj0[name0])
                  {
                     tarr0.push(name0);
                  }
               }
               if(tarr0.length == 0)
               {
                  tarr0 = marr0;
               }
            }
         }
         return tarr0;
      }
      
      public static function getSmeltResultStr(g0:GiftAddDefineGroup, snum0:int) : String
      {
         var s0:String = null;
         var num0:int = 0;
         var MAX_NUM:int = 0;
         var n:* = undefined;
         var name0:String = null;
         var v0:int = 0;
         var td0:ThingsDefine = null;
         var newG0:GiftAddDefineGroup = getSingle(g0,snum0);
         var backObj0:Object = {};
         var d0:GiftAddDefine = smelt(newG0,backObj0);
         var objNum0:int = ObjectMethod.getObjElementNum(backObj0);
         if(objNum0 > 1)
         {
            s0 = "";
            num0 = 0;
            MAX_NUM = 99;
            for(n in backObj0)
            {
               name0 = n as String;
               v0 = int(backObj0[name0]);
               num0++;
               if(num0 <= MAX_NUM)
               {
                  td0 = Gaming.defineGroup.things.getDefine(name0);
                  if(s0 != "")
                  {
                     s0 += "、";
                  }
                  s0 += td0.cnName + "*" + ComMethod.color(v0 + "","#00FFFF");
               }
               else
               {
                  if(num0 != MAX_NUM + 1)
                  {
                     break;
                  }
                  s0 += "等";
               }
            }
            return ComMethod.color("熔炼1次可能出：","#FFFFFF") + s0;
         }
         if(Boolean(d0))
         {
            return ComMethod.color("熔炼1次必然出：","#FFFFFF") + d0.getCnName() + "*" + ComMethod.color(d0.num + "","#00FFFF");
         }
         return "";
      }
      
      public static function test() : void
      {
         var n:* = undefined;
         var arr0:Array = null;
         var d0:ThingsDefine = null;
         var s0:String = "";
         var obj0:Object = Gaming.defineGroup.things.smeltObj;
         for(n in obj0)
         {
            s0 += "\n【" + n + "】=====================";
            arr0 = obj0[n];
            for each(d0 in arr0)
            {
               s0 += "\n" + d0.toTrace();
            }
         }
         trace(s0);
      }
      
      public static function getThingsStrByPrice(setPrice0:Number) : String
      {
         var n:* = undefined;
         var arr0:Array = null;
         var d0:ThingsDefine = null;
         var price0:Number = NaN;
         var num0:int = 0;
         var s0:String = "";
         var obj0:Object = Gaming.defineGroup.things.smeltObj;
         for(n in obj0)
         {
            s0 += "\n【" + n + "】============";
            arr0 = obj0[n];
            for each(d0 in arr0)
            {
               price0 = d0.smeltD.price;
               if(price0 > 0 && d0.smeltD.grade >= 0)
               {
                  num0 = Math.round(setPrice0 / price0);
                  if(num0 > 0)
                  {
                     s0 += "\n" + d0.cnName + "x" + num0;
                  }
               }
            }
         }
         return s0;
      }
      
      public static function getPlanTitle(g0:GiftAddDefineGroup) : String
      {
         var s0:String = null;
         var num0:int = 0;
         var MAX_CHAR:int = 0;
         var MAX_NUM:int = 0;
         var name0:* = undefined;
         var td0:ThingsDefine = null;
         var backObj0:Object = {};
         var d0:GiftAddDefine = smelt(g0,backObj0);
         if(ObjectMethod.getObjElementNum(backObj0) > 0)
         {
            s0 = "";
            num0 = 0;
            MAX_CHAR = 2;
            MAX_NUM = 3;
            for(name0 in backObj0)
            {
               num0++;
               if(num0 <= MAX_NUM)
               {
                  td0 = Gaming.defineGroup.things.getDefine(name0 as String);
                  s0 += td0.cnName.substr(0,MAX_CHAR);
               }
               else
               {
                  if(num0 != MAX_NUM + 1)
                  {
                     break;
                  }
                  s0 += "等";
               }
            }
            return s0;
         }
         return d0.getCnName() + "x" + d0.num;
      }
      
      public static function giftToPlan(g0:GiftAddDefineGroup, num0:int) : String
      {
         var sarr0:Array = null;
         var d0:GiftAddDefine = null;
         var newG0:GiftAddDefineGroup = getSingle(g0,num0);
         if(Boolean(newG0))
         {
            sarr0 = [];
            for each(d0 in newG0.arr)
            {
               sarr0.push(d0.getCnName());
            }
            return StringMethod.concatStringArr(sarr0,999);
         }
         return "";
      }
      
      public static function planToGift(str0:String) : *
      {
         var s0:String = null;
         var td0:ThingsDefine = null;
         var d0:GiftAddDefine = null;
         var len0:int = 0;
         var inMax0:int = 0;
         var inMin0:int = 0;
         var f0:String = "、";
         var c0:String = "*";
         var farr0:Array = str0.split(f0);
         var error0:String = "";
         var gg0:GiftAddDefineGroup = new GiftAddDefineGroup();
         for each(s0 in farr0)
         {
            s0 = TextWay.toHanSpace(s0);
            td0 = Gaming.defineGroup.things.getDefineByCnName(s0);
            if(!td0 || td0.isPartsB())
            {
               error0 = "找不到物品“" + s0 + "”";
               break;
            }
            if(td0.isSmeltMaterialB() == false)
            {
               error0 = "“" + s0 + "”不是熔炼材料";
            }
            else
            {
               d0 = new GiftAddDefine();
               d0.inData_byStr("things;" + td0.name + ";1");
               gg0.mergeOne(d0);
            }
         }
         if(error0 == "")
         {
            for each(d0 in gg0.arr)
            {
               td0 = Gaming.defineGroup.things.getDefine(d0.name);
               d0.num = td0.smeltD.getMinMust();
            }
         }
         if(error0 == "")
         {
            len0 = int(gg0.arr.length);
            inMax0 = getThingsMax();
            inMin0 = getThingsMin();
            if(len0 < inMin0)
            {
               error0 = "最少要有" + inMin0 + "种物品。";
            }
            else if(len0 > inMax0)
            {
               error0 = "不能超过" + inMax0 + "种物品。";
            }
         }
         if(error0 == "")
         {
            return gg0;
         }
         return error0;
      }
   }
}

