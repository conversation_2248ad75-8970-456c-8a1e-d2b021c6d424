package dataAll._player.count
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   
   public class HeadCountProSave
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var timeStr:String = "";
      
      public var todayAddB:Boolean = false;
      
      public function HeadCountProSave()
      {
         super();
         this.value = 0;
      }
      
      public function get value() : Number
      {
         return this.CF.getAttribute("value");
      }
      
      public function set value(v0:Number) : void
      {
         this.CF.setAttribute("value",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function newDayCtrl(nowTimeStr0:String) : void
      {
         this.todayAddB = false;
         var c0:int = StringDate.compareDateByStr(this.timeStr,nowTimeStr0);
         if(c0 > 1)
         {
            this.value = 0;
         }
      }
      
      public function add(timeStr0:String) : void
      {
         if(!this.todayAddB)
         {
            this.value += 1;
            this.timeStr = timeStr0;
            this.todayAddB = true;
         }
      }
   }
}

