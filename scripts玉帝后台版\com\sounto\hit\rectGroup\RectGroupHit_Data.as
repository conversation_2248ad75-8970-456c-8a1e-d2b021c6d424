package com.sounto.hit.rectGroup
{
   import com.sounto.hit.LineHit;
   import com.sounto.hit.anyShape.AnyShapePoint;
   import com.sounto.hit.anyShape.AnyShapeRect;
   import com.sounto.motion.DofHitType;
   import com.sounto.motion.Motion_DOFData;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public class RectGroupHit_Data
   {
      
      public var haveDataB:Boolean = false;
      
      private var rectArr:Array = [];
      
      private var line_p:Point = new Point();
      
      private var xp0:AnyShapePoint = new AnyShapePoint();
      
      private var yp0:AnyShapePoint = new AnyShapePoint();
      
      public function RectGroupHit_Data()
      {
         super();
      }
      
      public function inData_bySp(sp0:Sprite) : void
      {
         var mc0:DisplayObject = null;
         this.clear();
         var arr0:Array = this.rectArr;
         for(var i:int = 0; i < sp0.numChildren; i++)
         {
            mc0 = sp0.getChildAt(i);
            arr0.push(new Rectangle(mc0.x,mc0.y,mc0.width,mc0.height));
         }
         if(arr0.length > 0)
         {
            this.haveDataB = true;
         }
      }
      
      public function clear() : void
      {
         this.haveDataB = false;
         this.rectArr.length = 0;
      }
      
      public function hit(x0:int, y0:int) : Boolean
      {
         var rect1:Rectangle = null;
         var bb0:Boolean = false;
         var arr0:Array = this.rectArr;
         var arr_len0:int = int(arr0.length);
         for(var n:int = 0; n < arr_len0; n++)
         {
            rect1 = arr0[n];
            bb0 = x0 >= rect1.x && x0 <= rect1.width + rect1.x && (y0 >= rect1.y && y0 <= rect1.height + rect1.y);
            if(bb0)
            {
               return true;
            }
         }
         return false;
      }
      
      public function hitLine(x0:Number, y0:Number, x1:Number, y1:Number, penetrationGap:int = 0) : Point
      {
         var rect1:Rectangle = null;
         var bb0:Boolean = false;
         var p0:Point = null;
         var len2:Number = NaN;
         var len0:Number = NaN;
         var arr0:Array = this.rectArr;
         this.line_p.x = x1;
         this.line_p.y = y1;
         if(x0 == x1 && y0 == y1)
         {
            return this.line_p;
         }
         var minLen2:Number = 1000000000;
         var hitB:Boolean = false;
         var arr_len0:int = int(arr0.length);
         for(var n:int = 0; n < arr_len0; n++)
         {
            rect1 = arr0[n];
            bb0 = x0 >= rect1.x && x0 <= rect1.width + rect1.x && (y0 >= rect1.y && y0 <= rect1.height + rect1.y);
            if(bb0)
            {
               this.line_p.x = x0;
               this.line_p.y = y0;
               return this.line_p;
            }
            p0 = LineHit.hit_LineRect(x0,y0,x1,y1,rect1.x,rect1.y,rect1.x + rect1.width,rect1.y + rect1.height);
            if(Boolean(p0))
            {
               len2 = (x0 - p0.x) * (x0 - p0.x) + (y0 - p0.y) * (y0 - p0.y);
               if(len2 < minLen2)
               {
                  this.line_p.x = p0.x;
                  this.line_p.y = p0.y;
                  minLen2 = len2;
               }
               hitB = true;
            }
         }
         if(hitB)
         {
            if(penetrationGap > 0)
            {
               if(x0 == x1)
               {
                  if(y0 < y1)
                  {
                     this.line_p.y += penetrationGap;
                     if(this.line_p.y > y1)
                     {
                        this.line_p.y = y1;
                     }
                  }
                  else
                  {
                     this.line_p.y -= penetrationGap;
                     if(this.line_p.y < y1)
                     {
                        this.line_p.y = y1;
                     }
                  }
               }
               else if(y0 == y1)
               {
                  if(x0 < x1)
                  {
                     this.line_p.x += penetrationGap;
                     if(this.line_p.x > x1)
                     {
                        this.line_p.x = x1;
                     }
                  }
                  else
                  {
                     this.line_p.x -= penetrationGap;
                     if(this.line_p.x < x1)
                     {
                        this.line_p.x = x1;
                     }
                  }
               }
               else
               {
                  len0 = Math.sqrt((x0 - x1) * (x0 - x1) + (y0 - y1) * (y0 - y1));
                  this.line_p.x = penetrationGap / len0 * (x1 - x0) + this.line_p.x;
                  this.line_p.y = penetrationGap / len0 * (y1 - y0) + this.line_p.y;
                  if(Math.abs(this.line_p.x - x0) > Math.abs(x1 - x0) || !this.hit(this.line_p.x,this.line_p.y))
                  {
                     this.line_p.x = x1;
                     this.line_p.y = y1;
                  }
               }
            }
            return this.line_p;
         }
         return null;
      }
      
      public function getSlopeVerticalRa(x0:Number, y0:Number) : Number
      {
         var rect1:Rectangle = null;
         var ctop:Number = NaN;
         var cdown:Number = NaN;
         var cleft:Number = NaN;
         var cRight:Number = NaN;
         var min:Number = NaN;
         var minNum:int = 0;
         var arr0:Array = this.rectArr;
         var arr_len0:int = int(arr0.length);
         for(var n:int = 0; n < arr_len0; n++)
         {
            rect1 = arr0[n];
            if(x0 >= rect1.x && x0 <= rect1.x + rect1.width && y0 >= rect1.y && y0 <= rect1.y + rect1.height)
            {
               ctop = Math.abs(y0 - rect1.y);
               cdown = Math.abs(y0 - rect1.y - rect1.height);
               cleft = Math.abs(x0 - rect1.x);
               cRight = Math.abs(x0 - rect1.x - rect1.width);
               min = 1000000;
               minNum = -1;
               if(cRight < min)
               {
                  min = cRight;
                  minNum = 0;
               }
               if(cdown < min)
               {
                  min = cdown;
                  minNum = 1;
               }
               if(cleft < min)
               {
                  min = cleft;
                  minNum = 2;
               }
               if(ctop < min)
               {
                  min = ctop;
                  minNum = 3;
               }
               return Math.PI / 2 * minNum;
            }
         }
         return -1000;
      }
      
      public function hitAll(rect0:AnyShapeRect, dof0:Motion_DOFData) : void
      {
         var rect1:Rectangle = null;
         var JJ:Rectangle = null;
         var cy:int = 0;
         var cx:int = 0;
         var arr0:Array = this.rectArr;
         var arr_len0:int = int(arr0.length);
         for(var n:int = 0; n < arr_len0; n++)
         {
            rect1 = arr0[n];
            if(this._hit(rect0.x,rect0.y,rect0.width,rect0.height,rect1.x,rect1.y,rect1.width,rect1.height))
            {
               JJ = rect1.intersection(rect0);
               if(JJ.width > JJ.height / 2)
               {
                  cy = rect0.y + rect0.height / 2 - (rect1.y + rect1.height / 2);
                  if(cy <= 0)
                  {
                     if(dof0.down == 0)
                     {
                        dof0.down = 1;
                        dof0.down_back = JJ.height - 1;
                        dof0.left_out = rect1.x - rect0.x;
                        dof0.right_out = rect0.x + rect0.width - (rect1.x + rect1.width);
                        dof0.hitType = DofHitType.ground;
                     }
                  }
                  else if(dof0.up == 0)
                  {
                     dof0.up = 1;
                     dof0.up_back = JJ.height - 1;
                  }
               }
               else
               {
                  cx = rect0.x + rect0.width / 2 - (rect1.x + rect1.width / 2);
                  if(cx < 0)
                  {
                     if(dof0.right == 0)
                     {
                        dof0.right = 1;
                        dof0.right_back = -JJ.width + 1;
                     }
                  }
                  else if(dof0.left == 0)
                  {
                     dof0.left = 1;
                     dof0.left_back = JJ.width - 1;
                  }
               }
            }
         }
      }
      
      public function hitPlatform(rect0:Rectangle, dof0:Motion_DOFData, vy0:Number) : void
      {
         var rect1:Rectangle = null;
         var c_y:Number = NaN;
         var arr0:Array = this.rectArr;
         var arr_len0:int = int(arr0.length);
         for(var n:int = 0; n < arr_len0; n++)
         {
            rect1 = arr0[n];
            if(rect0.x < rect1.x + rect1.width && rect0.x + rect0.width > rect1.x)
            {
               c_y = rect0.y + rect0.height - rect1.y;
               if(c_y > 0)
               {
                  if(c_y <= 5 || c_y <= vy0 + 10)
                  {
                     dof0.down = 1;
                     dof0.down_back = -c_y + 1;
                     dof0.left_out = rect1.x - rect0.x;
                     dof0.right_out = rect0.x + rect0.width - (rect1.x + rect1.width);
                     dof0.hitType = DofHitType.platform;
                  }
               }
            }
         }
      }
      
      private function _hit(x1:Number, y1:Number, w1:Number, h1:Number, x2:Number, y2:Number, w2:Number, h2:Number) : Boolean
      {
         var bb:Boolean = false;
         var lx:* = x1 - (x2 + w2);
         var rx:* = x2 - (x1 + w1);
         var uy:* = y1 - (y2 + h2);
         var dy:* = y2 - (y1 + h1);
         if(lx < 0 && rx < 0 && (uy < 0 && dy < 0))
         {
            bb = true;
         }
         return bb;
      }
   }
}

