package dataAll._app.login
{
   import com.sounto.cf.CodeCF;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._app.edit.card.BossCardSaveGroup;
   import dataAll._app.top.player.PlayerTopUploadData;
   import dataAll._app.union.UnionSave;
   import dataAll._app.wilder.WilderData;
   import dataAll._player.PlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.creator.ArmsEvoCtrl;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.device.DeviceData;
   import dataAll.equip.device.DeviceDataCreator;
   import dataAll.equip.device.DeviceDefine;
   import dataAll.equip.weapon.WeaponData;
   import dataAll.equip.weapon.WeaponDataCreator;
   import dataAll.equip.weapon.WeaponDefine;
   
   public class LoginUpload
   {
      
      public function LoginUpload()
      {
         super();
      }
      
      private static function get PD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      private static function bossCard7() : Object
      {
         var g6:Number = NaN;
         var da0:PlayerTopUploadData = null;
         var sg0:BossCardSaveGroup = PD.bossCard.getSaveG();
         var g7:Number = sg0.g7;
         g6 = sg0.g6;
         if(g7 > 0 || g6 > 0)
         {
            da0 = new PlayerTopUploadData();
            da0.rId = 2316;
            da0.score = g7 * 100 + g6;
            da0.extra = "七：" + g7 + "   六：" + g6 + "   普通：" + sg0.num + "  高级：" + sg0.hNum + "    " + PD.vip.def.getName2();
            return da0;
         }
         return null;
      }
      
      private static function sprintSword_shuttleDevicer() : Object
      {
         var da0:EquipData = null;
         var xuwu0:Number = NaN;
         var xuwuS0:WilderData = null;
         var tda0:PlayerTopUploadData = null;
         var wda0:WeaponData = null;
         var dda0:DeviceData = null;
         var arr0:Array = PD.getEquipDataArr(true,true,true,false,true);
         var weaponNum0:Number = 0;
         var deviceNum0:Number = 0;
         for each(da0 in arr0)
         {
            wda0 = da0 as WeaponData;
            if(Boolean(wda0))
            {
               weaponNum0 += getWeaponNum(wda0);
            }
            dda0 = da0 as DeviceData;
            if(Boolean(dda0))
            {
               deviceNum0 += getDeviceNum(dda0);
            }
         }
         xuwu0 = 0;
         xuwuS0 = PD.wilder.getData("VirtualScorpion");
         if(Boolean(xuwuS0))
         {
            xuwu0 = xuwuS0.save.all;
         }
         var kbuy0:Number = PD.getSave().pay.getNum("2787");
         var knum0:Number = Math.ceil(xuwu0 * 0.15) + kbuy0;
         var kmore0:Number = weaponNum0 - knum0;
         var all0:Number = weaponNum0 * 10 + deviceNum0;
         if(all0 > 2000000000)
         {
            all0 = 2000000000;
         }
         var s0:String = "k:" + kmore0 + "  ，凯撒：" + weaponNum0 + "，镭射：" + deviceNum0 + "，虚晶蝎：" + xuwu0 + "，" + PD.vip.def.getName2();
         tda0 = new PlayerTopUploadData();
         tda0.rId = 2298;
         tda0.score = all0;
         tda0.extra = s0;
         return tda0;
      }
      
      private static function getWeaponNum(da0:WeaponData) : Number
      {
         var v0:Number = NaN;
         var nameArr0:Array = ["sprintSword"];
         var d0:WeaponDefine = da0.getWeaponSave().getWeaponDefine();
         if(nameArr0.indexOf(d0.baseLabel) >= 0)
         {
            v0 = WeaponDataCreator.getUpgradeAll(d0.lv,d0);
            return v0 * da0.getNowNum();
         }
         return 0;
      }
      
      private static function getDeviceNum(da0:DeviceData) : Number
      {
         var v0:Number = NaN;
         var nameArr0:Array = ["shuttleDevicer"];
         var d0:DeviceDefine = da0.getDeviceSave().getDeviceDefine();
         if(nameArr0.indexOf(d0.baseLabel) >= 0)
         {
            v0 = DeviceDataCreator.getUpgradeAll(d0.lv,d0);
            return v0 * da0.getNowNum();
         }
         return 0;
      }
      
      public function getUploadArr() : Array
      {
         var extra0:String = this.getExtra();
         var arr0:Array = [];
         if(PD.post.save.nowPost == "platinumMonthCard")
         {
            arr0.push(this.postDay(extra0));
         }
         this.addArrIfHave(arr0,sprintSword_shuttleDevicer());
         this.addArrIfHave(arr0,this.battleWinTime());
         this.addArrIfHave(arr0,bossCard7());
         return arr0;
      }
      
      public function getUploadArr2() : Array
      {
         var extra0:String = this.getExtra();
         var arr0:Array = [];
         if(PD.main.uploadZB())
         {
            arr0.push(this.getZuoBi());
         }
         arr0.push(this.getMainTask(extra0));
         arr0.push(this.demStone());
         arr0.push(this.getDarkgold(extra0));
         this.addArrIfHave(arr0,this.undeadBird());
         return arr0;
      }
      
      private function addArrIfHave(arr0:Array, bo0:Object) : void
      {
         if(Boolean(bo0))
         {
            arr0.push(bo0);
         }
      }
      
      private function demStone() : Object
      {
         var ada0:ArmsData = null;
         var thingsNum0:Number = NaN;
         var s0:String = null;
         var da0:PlayerTopUploadData = null;
         var ad0:ArmsRangeDefine = null;
         var d0:ArmsDefine = null;
         var evoLv0:int = 0;
         var num0:Number = NaN;
         var ball0:Number = NaN;
         var arr0:Array = PD.getArmsDataArr(true,true,true);
         var nameArr0:Array = ["rifleHornet","shotgunSkunk","sniperCicada","pistolFox"];
         var stoneNum0:Number = 0;
         var ballNum0:Number = 0;
         for each(ada0 in arr0)
         {
            ad0 = ada0.save.getArmsRangeDefine();
            d0 = ad0.def;
            if(nameArr0.indexOf(d0.name) >= 0)
            {
               evoLv0 = ada0.save.evoLv + d0.evoMustFirstLv;
               if(evoLv0 >= 14)
               {
                  num0 = (evoLv0 - 14 + 1) * 285;
                  ball0 = (evoLv0 - 14 + 1) * 300;
                  stoneNum0 += num0;
                  ballNum0 += ball0;
               }
            }
         }
         thingsNum0 = PD.thingsBag.getThingsNum("demStone");
         ballNum0 += PD.thingsBag.getThingsNum("demBall");
         s0 = "物品：" + thingsNum0 + "，武器所需：" + stoneNum0 + "，掉落：" + PD.worldMap.saveGroup.getDemStoneAll() + "，万能球：" + ballNum0 + "，" + PD.vip.def.getName2();
         da0 = new PlayerTopUploadData();
         da0.rId = 2107;
         da0.score = stoneNum0 + thingsNum0;
         da0.extra = s0;
         return da0;
      }
      
      public function guideUN() : Object
      {
         var log0:LoginData4399 = null;
         var uid0:String = null;
         var new0:String = null;
         var da0:PlayerTopUploadData = null;
         var un0:String = PD.getSave().guide.un;
         if(un0 != "")
         {
            log0 = Gaming.api.save.s4399.getLogObjNull();
            if(Boolean(log0))
            {
               uid0 = log0.uid;
               if(uid0 != "")
               {
                  new0 = CodeCF.uidToCode(uid0);
                  if(un0 != new0)
                  {
                     da0 = new PlayerTopUploadData();
                     da0.rId = 2100;
                     da0.score = Math.ceil(PD.getDps() / 10000);
                     NumberMethod.limitRange(da0.score,1,2000000000);
                     da0.extra = "now:" + uid0 + " * " + new0 + "，save:" + PD.getSave().guide.un2 + " * " + un0 + "，" + PD.vip.def.getName2();
                     return da0;
                  }
               }
            }
         }
         return null;
      }
      
      public function guideUI() : Object
      {
         var da0:PlayerTopUploadData = null;
         var new0:String = Gaming.PG.loginData.getNew();
         var ui0:String = PD.getSave().guide.getObj2() as String;
         if(new0 != ui0)
         {
            da0 = new PlayerTopUploadData();
            da0.rId = 2098;
            da0.score = Math.ceil(PD.getDps() / 10000);
            NumberMethod.limitRange(da0.score,1,2000000000);
            da0.extra = "md5:" + new0 + "，save:" + ui0 + "，" + PD.vip.def.getName2() + "";
            return da0;
         }
         return null;
      }
      
      public function guideU() : Object
      {
         var da0:PlayerTopUploadData = null;
         var new0:String = Gaming.PG.u;
         var ui0:String = PD.getSave().guide.uu as String;
         if(new0 != ui0)
         {
            da0 = new PlayerTopUploadData();
            da0.rId = 2096;
            da0.score = Math.ceil(PD.getDps() / 10000);
            NumberMethod.limitRange(da0.score,1,2000000000);
            da0.extra = "now:" + Gaming.PG.u2 + " * " + new0 + "，save:" + PD.getSave().guide.uu2 + " * " + ui0 + "，login:" + Gaming.PG.loginData.uid + "，" + PD.vip.def.getName2();
            return da0;
         }
         return null;
      }
      
      public function battleWinTime() : Object
      {
         var score0:int = 0;
         var da0:PlayerTopUploadData = null;
         var s0:UnionSave = PD.union.save;
         var levelTime0:Number = s0.lt;
         if(levelTime0 > 0)
         {
            score0 = 100 - levelTime0;
            if(score0 > 0)
            {
               da0 = new PlayerTopUploadData();
               da0.rId = 2300;
               da0.score = score0;
               da0.extra = "time：" + levelTime0 + "，" + s0.mp;
               return da0;
            }
         }
         return null;
      }
      
      private function postDay(extra0:String) : Object
      {
         var da0:PlayerTopUploadData = new PlayerTopUploadData();
         da0.rId = 2037;
         da0.score = PD.post.save.getSurplusDay(PD.time.getReadTime());
         return da0;
      }
      
      private function getZuoBi() : Object
      {
         var lv0:int = PD.task.getMainTaskMaxLevel();
         var da0:PlayerTopUploadData = new PlayerTopUploadData();
         da0.rId = 2032;
         da0.score = PD.getDps();
         da0.extra = "lv*" + PD.level + "  life*" + PD.base.getMaxLife();
         return da0;
      }
      
      private function getMainTask(extra0:String) : Object
      {
         var lv0:int = PD.task.getMainTaskMaxLevel();
         var da0:PlayerTopUploadData = new PlayerTopUploadData();
         da0.rId = 1996;
         da0.score = int(Math.random() * 1000000 + 1);
         da0.extra = "map*" + lv0 + "  lv*" + PD.level + extra0;
         return da0;
      }
      
      private function getArmsChip(extra0:String) : Object
      {
         var ada0:ArmsData = null;
         var da0:PlayerTopUploadData = null;
         var ad0:ArmsRangeDefine = null;
         var d0:ArmsDefine = null;
         var evoLv0:int = 0;
         var chipNum0:Number = NaN;
         var s0:String = null;
         var arr0:Array = PD.getArmsDataArr(true,true,true);
         var nameArr0:Array = ["rifleHornet","shotgunSkunk","sniperCicada","pistolFox","redFire","meltFlamer"];
         var strArr0:Array = [];
         for each(ada0 in arr0)
         {
            ad0 = ada0.save.getArmsRangeDefine();
            d0 = ad0.def;
            if(nameArr0.indexOf(d0.name) >= 0)
            {
               evoLv0 = ada0.save.evoLv + d0.evoMustFirstLv;
               chipNum0 = ArmsEvoCtrl.getAllChipMustNum(evoLv0);
               chipNum0 += PD.thingsBag.getThingsNum(d0.name);
               s0 = d0.armsType + "*" + chipNum0;
               strArr0.push(s0);
            }
         }
         da0 = new PlayerTopUploadData();
         da0.rId = 1997;
         da0.score = int(Math.random() * 1000000 + 1);
         da0.extra = StringMethod.concatStringArr(strArr0,999,"  ");
         return da0;
      }
      
      private function getSpecialParts(extra0:String) : Object
      {
         var partsNum0:Number = PD.zuobiPaner.getSpecialPartsNum();
         var da0:PlayerTopUploadData = new PlayerTopUploadData();
         da0.rId = 1994;
         da0.score = partsNum0;
         da0.extra = extra0;
         return da0;
      }
      
      private function getParts90(extra0:String) : Object
      {
         var partsNum0:Number = PD.zuobiPaner.getPartsNum90Gather(false);
         var da0:PlayerTopUploadData = new PlayerTopUploadData();
         da0.rId = 2036;
         da0.score = partsNum0;
         da0.extra = extra0;
         return da0;
      }
      
      private function getDarkgold(extra0:String) : Object
      {
         var partsNum0:Number = PD.zuobiPaner.getSpecialPartsNum();
         var da0:PlayerTopUploadData = new PlayerTopUploadData();
         var darkGoldNum0:Number = this.getDarkgoldNum();
         var stoneNum0:Number = PD.thingsBag.getThingsNum("wisdomGem");
         var allNum0:Number = stoneNum0 + darkGoldNum0 * 300;
         var wilderNum0:Number = PD.wilder.getWilderAllNum("PoliceZombie");
         var e0:String = "darkgold*" + darkGoldNum0 + "  gem*" + stoneNum0 + "  wilder*" + wilderNum0;
         e0 += "  " + extra0;
         da0.rId = 1995;
         da0.score = allNum0;
         da0.extra = e0;
         return da0;
      }
      
      private function getDarkgoldNum() : Number
      {
         var da0:EquipData = null;
         var num0:Number = 0;
         var garr0:Array = PD.getEquipDataArr(true,true,true,false,true);
         for each(da0 in garr0)
         {
            if(da0.getColor() == EquipColor.DARKGOLD)
            {
               num0++;
            }
         }
         return num0;
      }
      
      private function getExtra() : String
      {
         var s0:String = PD.vip.def.getName2();
         return s0 + ("    levelNum*" + PD.worldMap.countAllLevelNum());
      }
      
      private function undeadBird() : Object
      {
         var da0:PlayerTopUploadData = null;
         var e0:EquipData = PD.findEquipDataByName("undeadBird",true);
         if(Boolean(e0))
         {
            da0 = new PlayerTopUploadData();
            da0.rId = 2068;
            da0.score = PD.getDps();
            da0.extra = "lv*" + PD.level + "  life*" + PD.base.getMaxLife();
            return da0;
         }
         return null;
      }
   }
}

