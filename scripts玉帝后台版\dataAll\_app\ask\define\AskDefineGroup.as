package dataAll._app.ask.define
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.equip.suit.SuitCtreator;
   import dataAll.skill.define.SkillDefine;
   
   public class AskDefineGroup
   {
      
      public var fatherArrObj:Object = {};
      
      public var fatherNameArr:Array = [];
      
      public var propsArr:Array = [];
      
      private var allNum:int = 0;
      
      public function AskDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var thingsXML0:* = undefined;
         var fatherName0:String = null;
         var index0:int = 0;
         var n:* = undefined;
         var d0:AskDefine = null;
         var fatherXML0:XMLList = xml0.father;
         for(i in fatherXML0)
         {
            thingsXML0 = fatherXML0[i].ask;
            fatherName0 = fatherXML0[i].@name;
            index0 = 0;
            for(n in thingsXML0)
            {
               d0 = new AskDefine();
               d0.inData_byXML(thingsXML0[n],fatherName0);
               this.addInFatherArr(d0,fatherName0);
               d0.index = index0;
               index0++;
               this.dealOtherMap(d0);
               this.dealEnemySuperSkill(d0);
            }
         }
      }
      
      public function initPropsData() : void
      {
         var d0:AskPropsDefine = null;
         d0 = new AskPropsDefine();
         d0.name = "clearError";
         d0.cnName = "照妖镜";
         d0.info = "去掉1个错误答案。";
         d0.fleshMouseTip();
         this.propsArr.push(d0);
         d0 = new AskPropsDefine();
         d0.name = "double";
         d0.cnName = "双倍积分";
         d0.info = "当前题目答对，则获得积分双倍。";
         d0.vipAddB = false;
         d0.num = 4;
         d0.fleshMouseTip();
         this.propsArr.push(d0);
      }
      
      private function dealOtherMap(d0:AskDefine) : void
      {
         var errorArr0:Array = null;
         var arr0:Array = null;
         var mapD0:WorldMapDefine = null;
         if(d0.errorArr[0] == "otherMap")
         {
            errorArr0 = [];
            arr0 = Gaming.defineGroup.worldMap.arr;
            for each(mapD0 in arr0)
            {
               if(d0.correctArr.indexOf(mapD0.cnName) == -1)
               {
                  errorArr0.push(mapD0.cnName);
               }
            }
            d0.errorArr = errorArr0;
         }
      }
      
      private function dealEnemySuperSkill(d0:AskDefine) : void
      {
         var arr0:Array = null;
         var xx0:int = 0;
         if(d0.correctArr[0] == "otherEnemySuperSkill")
         {
            arr0 = Gaming.defineGroup.skill.getSkillCnNameArr_byFather("enemySuper");
            d0.correctArr = ComMethod.deductArr(arr0,d0.errorArr);
         }
         if(d0.correctArr.length == 0)
         {
            xx0 = 0;
         }
      }
      
      private function addInFatherArr(d0:AskDefine, father0:String) : void
      {
         if(!this.fatherArrObj.hasOwnProperty(father0))
         {
            this.fatherArrObj[father0] = [];
            this.fatherNameArr.push(father0);
         }
         this.fatherArrObj[father0].push(d0);
         ++this.allNum;
      }
      
      public function getArr(father0:String) : Array
      {
         return this.fatherArrObj[father0];
      }
      
      public function initOther() : void
      {
         this.inOneSkillFather("heroSkill");
      }
      
      private function inOneSkillFather(f0:String) : void
      {
         var effectObj0:Object = Gaming.defineGroup.skill.getAskEffectArrObj(f0);
         this.inYesSkillEffect(f0,effectObj0);
         this.inNoSkillEffect(f0,effectObj0);
      }
      
      private function inSkillHeroCd(defineObj0:Object) : void
      {
         var arr0:Array = null;
         var skillD0:SkillDefine = null;
         var f0:String = null;
         var d0:AskDefine = null;
         var fatherCnName0:String = null;
         var index0:int = 0;
         for each(arr0 in defineObj0)
         {
            skillD0 = arr0[int(Math.random() * (arr0.length - 3) + 3)];
            if(skillD0 is SkillDefine)
            {
               if(skillD0.isActiveB() && skillD0.father == "heroSkill")
               {
                  f0 = skillD0.father;
                  d0 = new AskDefine();
                  index0++;
                  fatherCnName0 = Gaming.defineGroup.skill.getFatherCnName(f0);
                  d0.father = f0 + "_cd";
                  d0.index = index0;
                  d0.inSkillCd(skillD0,fatherCnName0);
                  this.addInFatherArr(d0,f0);
               }
            }
         }
      }
      
      private function inYesSkillEffect(f0:String, effectObj0:Object) : void
      {
         var n:* = undefined;
         var effect0:String = null;
         var fatherCnName0:String = null;
         var d0:AskDefine = null;
         var index0:int = 0;
         for(n in effectObj0)
         {
            effect0 = n;
            index0++;
            fatherCnName0 = Gaming.defineGroup.skill.getFatherCnName(f0);
            d0 = new AskDefine();
            d0.father = f0 + "_yes";
            d0.index = index0;
            d0.inSkillEffect(effect0,fatherCnName0,true);
            d0.correctArr = effectObj0[n];
            d0.errorArr = Gaming.defineGroup.skill.getNoAskEffectByFather(f0,effect0);
            this.addInFatherArr(d0,f0);
         }
      }
      
      private function inNoSkillEffect(f0:String, effectObj0:Object) : void
      {
         var n:* = undefined;
         var effect0:String = null;
         var skillNameArr0:Array = null;
         var fatherCnName0:String = null;
         var d0:AskDefine = null;
         var index0:int = 0;
         for(n in effectObj0)
         {
            effect0 = n;
            index0++;
            skillNameArr0 = effectObj0[n];
            if(skillNameArr0.length >= 2)
            {
               fatherCnName0 = Gaming.defineGroup.skill.getFatherCnName(f0);
               d0 = new AskDefine();
               d0.father = f0 + "_no";
               d0.index = index0;
               d0.inSkillEffect(effect0,fatherCnName0,false);
               d0.correctArr = Gaming.defineGroup.skill.getNoAskEffectByFather(f0,effect0);
               d0.errorArr = skillNameArr0;
               this.addInFatherArr(d0,f0);
            }
         }
      }
      
      private function inSuit() : void
      {
         this.inColorSuit("blue");
         this.inColorSuit("purple");
         this.inColorSuit("orange");
         this.inColorSuit("red");
      }
      
      private function inColorSuit(color0:String) : void
      {
         var f0:EquipFatherDefine = null;
         var correctArr0:Array = null;
         var allArr0:Array = null;
         var errorArr0:Array = null;
         var d0:AskDefine = null;
         var obj0:Object = Gaming.defineGroup.equip.fatherObj;
         var index0:int = 0;
         var num0:int = SuitCtreator.getProNumBy(color0,4);
         var colorCnName0:String = EquipColor.getCn(color0);
         var colorColor0:String = EquipColor.htmlColor(color0);
         for each(f0 in obj0)
         {
            if(f0.cnName != "猎魔" && f0.suitArray.length > 0)
            {
               correctArr0 = f0.getProCnNameArr();
               correctArr0.splice(num0,99);
               allArr0 = Gaming.defineGroup.suitProperty.getCnNameArr();
               errorArr0 = ComMethod.deductArr(allArr0,correctArr0);
               d0 = new AskDefine();
               d0.father = "suit_" + color0;
               index0++;
               d0.index = index0;
               d0.title = ComMethod.color("“" + colorCnName0 + "色" + f0.cnName + "套装”",colorColor0) + "包含有以下哪个属性？";
               d0.correctArr = correctArr0;
               d0.errorArr = errorArr0;
               this.addInFatherArr(d0,"suit");
            }
         }
      }
      
      public function getRandomDefine() : AskDefine
      {
         var father0:String = this.fatherNameArr[int(Math.random() * this.fatherNameArr.length)];
         var arr0:Array = this.getArr(father0);
         return arr0[int(Math.random() * arr0.length)];
      }
      
      public function showText() : String
      {
         var d0:AskDefine = null;
         var str0:String = "";
         var n0:int = 0;
         var arr0:Array = this.fatherArrObj["life"];
         for each(d0 in arr0)
         {
            n0++;
            str0 += "\n" + n0 + "、" + d0.showText();
         }
         return str0;
      }
   }
}

