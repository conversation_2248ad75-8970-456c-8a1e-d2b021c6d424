package dataAll.test
{
   import dataAll._base.FirstNormalDefineGroup;
   
   public class CheatingDefineGroup extends FirstNormalDefineGroup
   {
      
      private var upperObj:Object = {};
      
      public function CheatingDefineGroup()
      {
         super();
         defineClass = CheatingDefine;
      }
      
      override protected function addDefine(d0:Object, father0:String, fatherCn0:String = "") : void
      {
         super.addDefine(d0,father0,fatherCn0);
         var name0:String = d0.name;
         this.upperObj[name0.toLowerCase()] = d0;
      }
      
      public function getDefine(name0:String) : CheatingDefine
      {
         return obj[name0];
      }
      
      public function getDefineByUpper(upperName0:String) : CheatingDefine
      {
         return this.upperObj[upperName0];
      }
   }
}

