package dataAll._app.love.define
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.skill.define.SkillDefine;
   
   public class LoveLevelDefine
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var name:String = "";
      
      public var skillNameArr:Array = [];
      
      public var appLabel:String = "";
      
      public var gift:GiftAddDefineGroup = new GiftAddDefineGroup();
      
      public function LoveLevelDefine()
      {
         super();
         this.lv = 0;
         this.must = 0;
         this.max = -1;
      }
      
      public function get lv() : Number
      {
         return this.CF.getAttribute("lv");
      }
      
      public function set lv(v0:Number) : void
      {
         this.CF.setAttribute("lv",v0);
      }
      
      public function get must() : Number
      {
         return this.CF.getAttribute("must");
      }
      
      public function set must(v0:Number) : void
      {
         this.CF.setAttribute("must",v0);
      }
      
      public function get max() : Number
      {
         return this.CF.getAttribute("max");
      }
      
      public function set max(v0:Number) : void
      {
         this.CF.setAttribute("max",v0);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         ClassProperty.inData_byXML(this,xml0,pro_arr);
         this.gift.inAllData_byXML(xml0.gift,false);
         this.name = "lv" + this.lv;
      }
      
      public function getTalkName(defAll0:LoveRoleAll) : String
      {
         var lv2:int = 0;
         var lv0:int = this.lv;
         var newLv0:int = 1;
         var rangeArr0:Array = defAll0.loveTalkLvRangeArr;
         for(var i:int = rangeArr0.length - 1; i >= 0; i--)
         {
            lv2 = int(rangeArr0[i]);
            if(lv0 >= lv2)
            {
               newLv0 = lv2;
               break;
            }
         }
         return "lv_" + newLv0;
      }
      
      public function getMustRange() : String
      {
         var last0:String = this.max + "";
         if(this.max == -1)
         {
            last0 = "最大值";
         }
         return this.must + " ~ " + last0;
      }
      
      public function getInfo(openB0:Boolean) : String
      {
         var str0:String = null;
         var firstStr0:String = null;
         var s0:String = "";
         var arr0:Array = this.getUISkillInfoArr(openB0).concat([this.getAppLabelInfo()]);
         var n0:int = 0;
         for each(str0 in arr0)
         {
            if(str0 != "")
            {
               firstStr0 = openB0 ? ComMethod.color("√ ","#00FF00") : n0 + 1 + "、";
               s0 += (n0 == 0 ? "" : "\n") + firstStr0 + str0;
               n0++;
            }
         }
         if(s0 == "")
         {
            s0 = "无";
         }
         return s0;
      }
      
      public function getUISkillInfoArr(openB0:Boolean) : Array
      {
         var name0:String = null;
         var d0:SkillDefine = null;
         var str0:String = null;
         var arr0:Array = [];
         for each(name0 in this.skillNameArr)
         {
            d0 = Gaming.defineGroup.skill.getDefine(name0);
            str0 = "";
            if(d0.wantDescripB)
            {
               str0 = "“" + d0.cnName + "”：" + d0.getDescription();
               str0 = (d0.valueString == "" ? "" : "主角技能") + str0;
            }
            else
            {
               str0 = d0.getDescription(true);
            }
            arr0.push(str0);
         }
         return arr0;
      }
      
      public function getAppLabelInfo() : String
      {
         var giftD0:GiftAddDefine = null;
         if(this.appLabel == "girlWeapon")
         {
            return "解锁小樱副手功能。";
         }
         if(this.appLabel == "otherWeapon")
         {
            return "解锁其他队友副手功能。";
         }
         if(this.gift.arr.length > 0)
         {
            giftD0 = this.gift.arr[0];
            return "每日可获得" + this.appLabel + "“" + giftD0.getCnName() + "”" + giftD0.num + "个。";
         }
         return "";
      }
   }
}

