package com.sounto.motion
{
   import com.sounto.math.Maths;
   
   public class Coor<PERSON>otion extends MainMotion
   {
      
      public var FPS:int = 30;
      
      public var mx:Number = 0;
      
      public var my:Number = 0;
      
      public var ax:Number = 0;
      
      public var ay:Number = 0;
      
      public var vxmax:Number = 40;
      
      public var vymax:Number = 40;
      
      public function CoorMotion()
      {
         super();
      }
      
      override public function setX(value:Number) : void
      {
         x = value;
         this.mx = value;
      }
      
      override public function setY(value:Number) : void
      {
         y = value;
         this.my = value;
      }
      
      public function getGapX() : Number
      {
         return Math.abs(this.mx - x);
      }
      
      public function getGapY() : Number
      {
         return Math.abs(this.my - y);
      }
      
      public function getGap() : Number
      {
         return Maths.Long(this.mx - x,this.my - y);
      }
   }
}

