package dataAll.equip.device
{
   import dataAll._player.PlayerData;
   import dataAll.must.define.MustDefine;
   import dataAll.things.define.ThingsDefine;
   
   public class DeviceDataCreator
   {
      
      public function DeviceDataCreator()
      {
         super();
      }
      
      public static function getSave(name0:String, num0:int) : DeviceSave
      {
         var d0:DeviceDefine = Gaming.defineGroup.device.getDefine(name0);
         if(!(d0 is DeviceDefine))
         {
            INIT.showError("找不到定义DeviceDefine：" + name0);
         }
         return getSaveByDefine(d0,num0);
      }
      
      public static function getSaveByDefine(d0:DeviceDefine, num0:int) : DeviceSave
      {
         var s0:DeviceSave = new DeviceSave();
         s0.inDataByDefine(d0);
         s0.nowNum = num0;
         return s0;
      }
      
      public static function getTempData(d0:DeviceDefine, pd0:PlayerData) : DeviceData
      {
         var s0:DeviceSave = getSaveByDefine(d0,1);
         var da0:DeviceData = new DeviceData();
         da0.inData_bySave(s0,pd0,null);
         return da0;
      }
      
      public static function getUpgradeMust(da0:DeviceData, lv0:int) : MustDefine
      {
         var deviceDefine0:DeviceDefine = da0.deviceDefine;
         var d0:MustDefine = getUpgradeThingsMust(deviceDefine0,lv0,da0.placeType);
         d0.lv = 70;
         return d0;
      }
      
      public static function getUpgradeThingsMust(deviceDefine0:DeviceDefine, lv0:int, placeType0:String) : MustDefine
      {
         if(deviceDefine0.baseLabel == "timeCapsule")
         {
            return getMust_timeCapsule(deviceDefine0,lv0,placeType0);
         }
         var d0:MustDefine = new MustDefine();
         var num0:int = getUpgradeNum(lv0,deviceDefine0.mustMul);
         var thingArr0:Array = [deviceDefine0.getUpgradeMustName() + ";" + num0];
         if(lv0 == 7)
         {
            thingArr0.push("dinosaurEgg_1;1");
            thingArr0.push("electricDevicer_1;10");
         }
         else if(lv0 >= 8)
         {
            thingArr0.push("dinosaurEgg_1;1");
            thingArr0.push("knightsMedal_1;1");
         }
         if(deviceDefine0.baseLabel == "hookDevice")
         {
            if(lv0 == 2)
            {
               thingArr0 = ["hookDevice_1;" + 2,"skeletonMedal_1;2"];
            }
         }
         d0.inThingsDataByArrAndMeger(thingArr0,MustDefine.NUM_EQUIP);
         d0.coin = num0 * 1000;
         return d0;
      }
      
      private static function getMust_timeCapsule(deviceDefine0:DeviceDefine, lv0:int, placeType0:String) : MustDefine
      {
         var d0:MustDefine = new MustDefine();
         var td0:ThingsDefine = deviceDefine0.getComposeThingsDefine();
         var num0:int = 500;
         if(lv0 == 2)
         {
            num0 = 80;
         }
         if(lv0 == 3)
         {
            num0 = 120;
         }
         if(lv0 == 4)
         {
            num0 = 200;
         }
         if(lv0 == 5)
         {
            num0 = 300;
         }
         if(lv0 == 6)
         {
            num0 = 400;
         }
         d0.inThingsDataByArrAndMeger([td0.name + ";" + num0]);
         return d0;
      }
      
      private static function getUpgradeNum(lv0:int, mul0:Number = 1) : int
      {
         var num0:int = (lv0 - 1) * 4;
         if(lv0 >= 5)
         {
            num0 = (lv0 - 1) * 20;
         }
         return int(Math.ceil(num0 * mul0));
      }
      
      public static function getUpgradeAll(lv0:int, deviceDefine0:DeviceDefine) : int
      {
         var v0:Number = 1;
         for(var i:int = 2; i <= lv0; i++)
         {
            v0 += getUpgradeNum(i,deviceDefine0.mustMul);
         }
         return v0;
      }
   }
}

