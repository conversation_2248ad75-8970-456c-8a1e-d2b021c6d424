package gameAll.body.ai.extraTask
{
   import dataAll.body.attack.HurtData;
   import gameAll.body.IO_NormalBody;
   import gameAll.skill.SkillEffectData;
   
   public class ExtraTaskAI_FightKing extends ExtraTaskAI
   {
      
      private var knifeBoomNum:int = 0;
      
      public function ExtraTaskAI_FightKing()
      {
         super();
      }
      
      override protected function lifePerPan() : void
      {
         super.lifePerPan();
         var per0:Number = _dat.getLifePer();
         if(per0 < (9 - this.knifeBoomNum) / 10 && BB.getDie() == 0)
         {
            ++this.knifeBoomNum;
            BB.getSkillCtrl().doSkillImmediately("knifeBoom_FightKing");
         }
      }
      
      override public function bodyAddBySkill(se0:SkillEffectData, b0:IO_NormalBody) : void
      {
         if(se0.define.name == "summonedPo_extra")
         {
            b0.getData().setNewMaxLife(_dat.maxLife * se0.define.obj["lifeMul"]);
            b0.getData().setDpsFactor(_dat.dpsFactor * se0.define.obj["dpsMul"]);
         }
      }
      
      override public function summonedDie(b0:IO_NormalBody, b1:IO_NormalBody, h0:HurtData) : void
      {
         var per0:Number = NaN;
         if(b1 is IO_NormalBody)
         {
            if(b0.getData().camp != b1.getData().camp)
            {
               if(b0.getData().define.name == _def.name)
               {
                  per0 = _dat.getLifePer();
                  if(per0 > 0.1)
                  {
                     _dat.setLifePer(per0 - 0.05);
                     Gaming.EG.special.addGreenBlood("greenBlood",_mot.x,_mot.y - 50,-Math.PI / 2,30,10,3);
                  }
               }
            }
         }
      }
   }
}

