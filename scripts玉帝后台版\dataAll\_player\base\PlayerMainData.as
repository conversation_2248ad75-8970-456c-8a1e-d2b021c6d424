package dataAll._player.base
{
   import com.adobe.crypto.MD5;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.login.LoginData4399;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._data.DefineGroup;
   import dataAll._player.PlayerData;
   import dataAll._player.realName.RealNameAgent;
   import dataAll._player.role.RoleName;
   import dataAll.level.define.LevelDefine;
   import dataAll.must.define.MustDefine;
   import gameAll.level.data.LevelData;
   
   public class PlayerMainData
   {
      
      private var playerData:PlayerData = null;
      
      public var save:PlayerMainSave = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public function PlayerMainData()
      {
         super();
         this.money = 0;
         this.totalRecharged = 0;
      }
      
      public static function getDayLotteryShowMax() : int
      {
         return 40;
      }
      
      public static function getBossSumMax() : int
      {
         return 7;
      }
      
      public static function getDemBossSumMax() : int
      {
         return 1;
      }
      
      public function set money(v0:Number) : void
      {
         this.CF.setAttribute("money",v0);
      }
      
      public function get money() : Number
      {
         return Number(this.CF.getAttribute("money"));
      }
      
      public function set totalRecharged(v0:Number) : void
      {
         this.CF.setAttribute("totalRecharged",v0);
      }
      
      public function get totalRecharged() : Number
      {
         return Number(this.CF.getAttribute("totalRecharged"));
      }
      
      public function getErrorStr() : String
      {
         var s0:String = "";
         var owe0:Number = this.save.getOweNuclearStone();
         if(owe0 > 0)
         {
            s0 += "在34.6版本中，由于系统bug多返还了核能石，现在需要回收，但你的核能石不足，缺少" + ComMethod.yellow(owe0) + "个，请补齐，直到该提示消失；或者找客服回档到7月16号更新前的版本。否则氩石的掉落将受影响。";
         }
         return s0;
      }
      
      public function setPlayerData(pd0:PlayerData) : void
      {
         this.playerData = pd0;
      }
      
      public function inData_bySave(s0:PlayerMainSave) : void
      {
         this.save = s0;
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.save.newDayCtrl();
      }
      
      public function newWeek() : void
      {
         this.save.newWeek();
      }
      
      public function getSwapRole() : String
      {
         var role0:String = null;
         if(this.save.before == "" || this.save.before == this.save.role)
         {
            role0 = "";
            if(this.save.role == RoleName.Striker)
            {
               role0 = RoleName.WenJie;
            }
            else
            {
               role0 = RoleName.Striker;
            }
            return role0;
         }
         return "";
      }
      
      public function overLevel(nowWorldMapDefine0:WorldMapDefine, levelDat0:LevelData, levelDef0:LevelDefine, model0:String) : void
      {
      }
      
      public function addBulletNumArr(arr0:Array) : void
      {
         ArrayMethod.addNoRepeatArrInArr(this.save.weekArmsArr,arr0);
      }
      
      public function fleshMaxDps(dps0:Number) : void
      {
         if(this.save.maxDp < dps0)
         {
            this.save.maxDp = dps0;
         }
      }
      
      public function addCoin(v0:Number) : void
      {
         var v2:Number = this.save.coin;
         v2 += v0;
         if(v2 < 0)
         {
            v2 = 0;
         }
         this.save.coin = v2;
         if(v0 > 0)
         {
            this.playerData.base.nowCountSave.coin += v0;
         }
      }
      
      public function useCoin(v0:Number) : void
      {
         if(v0 >= 0)
         {
            this.addCoin(-v0);
         }
      }
      
      public function addScore(v0:Number) : void
      {
         var v2:Number = this.save.score;
         v2 += v0;
         if(v2 < 0)
         {
            v2 = 0;
         }
         this.save.score = v2;
      }
      
      public function addAnniCoin(v0:Number) : void
      {
         var v2:Number = this.save.anniCoin;
         v2 += v0;
         if(v2 < 0)
         {
            v2 = 0;
         }
         this.save.anniCoin = v2;
      }
      
      public function addTenCoin(v0:Number) : void
      {
         var v2:Number = this.save.tenCoin;
         v2 += v0;
         if(v2 < 0)
         {
            v2 = 0;
         }
         this.save.tenCoin = v2;
      }
      
      public function addPartsCoin(v0:Number) : void
      {
         var v2:Number = this.save.partsC;
         v2 += v0;
         if(v2 < 0)
         {
            v2 = 0;
         }
         this.save.partsC = v2;
      }
      
      public function addPumpkin(v0:Number) : void
      {
         var v2:Number = this.save.getActivePrice();
         v2 += v0;
         if(v2 < 0)
         {
            v2 = 0;
         }
         this.save.setActivePrice(v2);
      }
      
      public function getPumpkin() : Number
      {
         return this.save.getActivePrice();
      }
      
      public function addDaySweeping(v0:Number) : void
      {
         var v2:Number = this.save.daySweeping;
         v2 += v0;
         if(v2 < 0)
         {
            v2 = 0;
         }
         this.save.daySweeping = v2;
      }
      
      public function setPass(pass0:String) : void
      {
         if(pass0 == "")
         {
            this.save.pass = "";
         }
         else
         {
            this.save.pass = MD5.hash(pass0);
         }
      }
      
      public function panPass(pass0:String) : Boolean
      {
         var md50:String = MD5.hash(pass0);
         if(md50 == this.save.pass)
         {
            return true;
         }
         return false;
      }
      
      public function havePass() : Boolean
      {
         return this.save.pass != "";
      }
      
      public function createMd5(s0:LoginData4399, isLocalB0:Boolean) : void
      {
         if(this.save.uidMd5 == "" && Boolean(s0.save))
         {
            this.save.uidMd5 = MD5.hash(s0.uid + "_" + s0.save.index);
         }
      }
      
      public function panMd5(s0:LoginData4399) : String
      {
         var str0:String = MD5.hash(s0.uid + "_" + s0.save.index);
         if(this.save.uidMd5 == "" || str0 == this.save.uidMd5)
         {
            return "";
         }
         return "存档uid与实际uid不符";
      }
      
      public function addZuobi(reason0:String) : void
      {
         if(this.save.zuobiReason.indexOf(reason0) == -1)
         {
            this.save.zuobiReason += "," + reason0;
         }
         this.save.isZuobiB = true;
      }
      
      public function zuobiPan() : String
      {
         return "";
      }
      
      public function uploadZB() : Boolean
      {
         var nameArr0:Array = null;
         var name0:String = null;
         var s0:String = this.save.zuobiReason;
         if(s0 == "" || !this.save.isZuobiB)
         {
            return false;
         }
         nameArr0 = DefineGroup.zuobiNameArr;
         for each(name0 in nameArr0)
         {
            s0 = s0.replace(name0,"");
         }
         if(s0.indexOf(",") == -1)
         {
            return false;
         }
         return true;
      }
      
      public function inRealNameAgent(a0:RealNameAgent) : void
      {
         if(a0.haveCertB())
         {
            this.save.s18 = 2;
         }
      }
      
      public function realNameApiEvent() : void
      {
         this.save.s18 = 2;
      }
      
      public function openRealNameUI() : void
      {
         if(this.save.s18 == 0)
         {
            this.save.s18 = 1;
         }
      }
      
      public function getNameChangeTip() : String
      {
         var must_d0:MustDefine = Gaming.defineGroup.must.changeName;
         var str0:String = "修改一次昵称，需要：\n" + must_d0.getText();
         var s2:String = "";
         if(this.save.changeNameB)
         {
            s2 = "下周一你才能再次修改昵称。";
         }
         else
         {
            s2 = "一周只能修改一次昵称(包括人物昵称、尸宠昵称)。";
         }
         return s2 + "\n\n" + str0;
      }
      
      public function setAi(bb0:Boolean) : void
      {
         if(bb0)
         {
            this.save.ai = 1;
         }
         else
         {
            this.save.ai = 0;
         }
      }
      
      public function getAi() : Boolean
      {
         if(this.save.ai == 1)
         {
            return true;
         }
         return false;
      }
      
      public function swapAi() : Boolean
      {
         this.setAi(!this.getAi());
         return this.getAi();
      }
      
      private function haveBookId(id0:String) : Boolean
      {
         return this.save.bookObj.hasOwnProperty(id0);
      }
      
      public function canBookGetB(id0:String) : Boolean
      {
         return this.getBookGetNum(id0) < 1;
      }
      
      public function getBookGetNum(id0:String) : int
      {
         if(this.haveBookId(id0))
         {
            return this.save.bookObj[id0];
         }
         return -1;
      }
      
      public function getBookIdEvent(id0:String) : void
      {
         if(this.save.bookObj.hasOwnProperty(id0))
         {
            ++this.save.bookObj[id0];
         }
         else
         {
            this.save.bookObj[id0] = 0;
         }
      }
      
      public function bossSumCardUseEvent(demB0:Boolean, num0:int) : void
      {
         if(demB0)
         {
            this.save.dembs += num0;
         }
         else
         {
            this.save.bs += num0;
         }
      }
      
      public function haveArmsSkinB(name0:String) : Boolean
      {
         return this.save.armsSkin.indexOf(name0) >= 0;
      }
      
      public function addArmsSkin(name0:String) : Boolean
      {
         return ArrayMethod.addNoRepeatInArr(this.save.armsSkin,name0);
      }
   }
}

