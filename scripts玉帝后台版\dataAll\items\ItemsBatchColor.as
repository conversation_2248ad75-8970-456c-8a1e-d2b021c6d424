package dataAll.items
{
   import dataAll._app.setting.SettingSave;
   import dataAll.arms.save.ArmsSave;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.save.EquipSave;
   import dataAll.items.save.ItemsSave;
   
   public class ItemsBatchColor extends EquipColor
   {
      
      public static const rare:String = "rare";
      
      public static const auto:String = "auto";
      
      public static const normalArr:Array = [WHITE,GREEN,BLUE,PURPLE,ORANGE,RED,BLACK];
      
      public static const allArr:Array = normalArr.concat([rare,auto]);
      
      public static const rareTypeArr:Array = [ItemsDataGroup.TYPE_EQUIP,ItemsDataGroup.TYPE_ARMS];
      
      public static const autoTypeArr:Array = [ItemsDataGroup.TYPE_EQUIP,ItemsDataGroup.TYPE_ARMS,ItemsDataGroup.TYPE_GENE];
      
      public function ItemsBatchColor()
      {
         super();
      }
      
      public static function getColorCn(color0:String, dataType0:String) : String
      {
         if(color0 == rare)
         {
            if(dataType0 == ItemsDataGroup.TYPE_EQUIP)
            {
               return "保留红色鬼爵、仲裁者";
            }
            if(dataType0 == ItemsDataGroup.TYPE_ARMS)
            {
               return "保留指定属性武器";
            }
            return "";
         }
         if(color0 == auto)
         {
            if(dataType0 == ItemsDataGroup.TYPE_ARMS)
            {
               return "掉落的武器自动拆解";
            }
            if(dataType0 == ItemsDataGroup.TYPE_EQUIP)
            {
               return "掉落的装备自动拆解";
            }
            if(dataType0 == ItemsDataGroup.TYPE_GENE)
            {
               return "掉落的基因体自动分解";
            }
            return "";
         }
         return EquipColor.getColorCn(color0);
      }
      
      public static function getBtnTip(color0:String, dataType0:String) : String
      {
         if(color0 == rare)
         {
            if(dataType0 == ItemsDataGroup.TYPE_ARMS)
            {
               return "点击进入设置界面。";
            }
         }
         else if(color0 == auto)
         {
            return "联邦国务卿、联邦总统的特权。";
         }
         return "";
      }
      
      public static function getRareChooseLabel(dataType0:String) : String
      {
         if(rareIsPointerB(dataType0))
         {
            return "pointer";
         }
         return "choose";
      }
      
      public static function rareIsPointerB(dataType0:String) : Boolean
      {
         if(dataType0 == ItemsDataGroup.TYPE_ARMS)
         {
            return true;
         }
         return false;
      }
      
      public static function dataPanColorArr(s0:ItemsSave, colorArr0:Array) : Boolean
      {
         var bb0:Boolean = false;
         var equipSave0:EquipSave = null;
         var noRareB0:Boolean = colorArr0.indexOf(rare) >= 0;
         if(noRareB0)
         {
            if(s0 is ArmsSave)
            {
               if(settingSave.armsFilterPan(s0 as ArmsSave))
               {
                  return false;
               }
            }
         }
         var color0:String = s0.color;
         if(colorArr0.indexOf(color0) >= 0)
         {
            bb0 = true;
            if(color0 == EquipColor.RED)
            {
               if(noRareB0)
               {
                  equipSave0 = s0 as EquipSave;
                  if(Boolean(equipSave0))
                  {
                     if(equipSave0.getDefine().isDescomposeRareB())
                     {
                        return false;
                     }
                  }
               }
            }
            return true;
         }
         return false;
      }
      
      private static function get settingSave() : SettingSave
      {
         return Gaming.PG.save.setting;
      }
   }
}

