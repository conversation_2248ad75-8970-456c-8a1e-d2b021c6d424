package dataAll._data
{
   import com.adobe.crypto.MD5;
   import com.common.data.Base64;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.achieve.define.AchieveConditionDefine;
   import dataAll._app.achieve.define.AchieveDefine;
   import dataAll._app.achieve.define.AchieveDefineGroup;
   import dataAll._app.achieve.define.AchieveFatherDefine;
   import dataAll._app.achieve.define.MedelProDefine;
   import dataAll._app.active.define.ActiveDefineGroup;
   import dataAll._app.active.define.ActiveGiftDefine;
   import dataAll._app.active.define.ActiveTaskDefine;
   import dataAll._app.ask.define.AskDefineGroup;
   import dataAll._app.blackMarket.define.BlackMarketDefineGroup;
   import dataAll._app.blackMarket.define.BlackMarketWayDefine;
   import dataAll._app.book.BookDefineGroup;
   import dataAll._app.city.define.CityBodyDefineGroup;
   import dataAll._app.edit._def.EditProDefineGroup;
   import dataAll._app.edit.arms.ArmsEditMethod;
   import dataAll._app.edit.boss.BossEditSkill;
   import dataAll._app.edit.card.BCardPKCreator;
   import dataAll._app.edit.card.BossCardCreator;
   import dataAll._app.food.FoodDefineGroup;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.goods.define.GoodsDefineGroup;
   import dataAll._app.goods.define.GoodsFatherDefine;
   import dataAll._app.head.define.HeadConditionDefine;
   import dataAll._app.head.define.HeadDefine;
   import dataAll._app.head.define.HeadDefineGroup;
   import dataAll._app.head.define.HeadHonorDefine;
   import dataAll._app.love.define.LoveDefineAll;
   import dataAll._app.love.define.LoveLevelDefine;
   import dataAll._app.love.define.LoveTalkDefine;
   import dataAll._app.outfit.define.OutfitDefine;
   import dataAll._app.outfit.define.OutfitDefineGroup;
   import dataAll._app.outfit.define.OutfitMustDefine;
   import dataAll._app.partner.define.PartnerDefineGroup;
   import dataAll._app.parts.PartsCreator;
   import dataAll._app.parts.define.PartsPropertyDefine;
   import dataAll._app.parts.define.PartsPropertyDefineGroup;
   import dataAll._app.parts.define.PartsPropertyRangeDefine;
   import dataAll._app.peak.PeakProDefineGroup;
   import dataAll._app.post.define.PostDefine;
   import dataAll._app.post.define.PostDefineGroup;
   import dataAll._app.setting.key.KeyActionDefine;
   import dataAll._app.setting.key.KeyActionDefineGroup;
   import dataAll._app.space.achieve.SpaceAchieveCtrl;
   import dataAll._app.space.craft.CraftDefineGroup;
   import dataAll._app.task.define.TaskConditionDefine;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._app.task.define.TaskDefineGroup;
   import dataAll._app.task.define.TaskDropDefine;
   import dataAll._app.task.define.TaskFatherDefine;
   import dataAll._app.task.define.TaskLimitDefine;
   import dataAll._app.task.define.TaskOpenDefine;
   import dataAll._app.top.define.TopBarAll;
   import dataAll._app.top.define.TopBarDefine;
   import dataAll._app.top.define.TopBarGatherDefine;
   import dataAll._app.top.extra.ArenaTopExtra;
   import dataAll._app.top.extra.ArmsTopExtra;
   import dataAll._app.top.extra.BossEditTopExtra;
   import dataAll._app.top.extra.DpsTopExtra;
   import dataAll._app.top.extra.UnionBattleExtra;
   import dataAll._app.tower.TowerDefineCtrl;
   import dataAll._app.tower.UnendDataCtrl;
   import dataAll._app.union.battle.UnionBattleMapDefine;
   import dataAll._app.union.building.define.UnionBuildingDefine;
   import dataAll._app.union.building.define.UnionCookingDefine;
   import dataAll._app.union.building.define.UnionGeologyDefine;
   import dataAll._app.union.building.define.UnionGeologyThingsDefine;
   import dataAll._app.union.building.define.UnionSendTaskDefine;
   import dataAll._app.union.building.define.UnionWatchmenDefine;
   import dataAll._app.union.define.MilitaryDefine;
   import dataAll._app.union.define.UnionDefineGroup;
   import dataAll._app.union.info.UnionInfoGroup;
   import dataAll._app.union.task.UnionTaskDefine;
   import dataAll._app.vip.define.VipDefineGroup;
   import dataAll._app.vip.define.VipLevelDefine;
   import dataAll._app.wilder.define.WilderDefine;
   import dataAll._app.wilder.define.WilderDefineGroup;
   import dataAll._app.wilder.define.WilderDropDefine;
   import dataAll._app.wilder.define.WilderFatherDefine;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._app.worldMap.define.WorldMapDefineGroup;
   import dataAll._app.worldMap.define.WorldMapLevelDefine;
   import dataAll._data.xml.LoveXml;
   import dataAll._player.count.define.CountDefineGroup;
   import dataAll._player.state.define.PlayerStateDefine;
   import dataAll._player.state.define.PlayerStateDefineGroup;
   import dataAll.arms.bookGet.ArmsBookGetter;
   import dataAll.arms.creator.ArmsDataCreator;
   import dataAll.arms.creator.ArmsSpecialAndSkill;
   import dataAll.arms.define.ArmsChargerDefine;
   import dataAll.arms.define.ArmsChargerDefineGroup;
   import dataAll.arms.define.ArmsCrossbowDefine;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsRecordDefine;
   import dataAll.arms.define.ArmsTipDefine;
   import dataAll.arms.save.ArmsSave;
   import dataAll.body.attack.BodyAttackDefine;
   import dataAll.body.attack.ElementHurt;
   import dataAll.body.attack.ElementShell;
   import dataAll.body.attack.HurtData;
   import dataAll.body.define.BodyDefineGroup;
   import dataAll.body.define.BodyDropDefine;
   import dataAll.body.define.BodyEditDefine;
   import dataAll.body.define.BodyMoreDefine;
   import dataAll.body.define.BodyMotionDefine;
   import dataAll.body.define.BodySystemType;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.body.extra.BodyExtraDefine;
   import dataAll.body.extra.BodyExtraDefineGroup;
   import dataAll.bullet.BulletBindingDefine;
   import dataAll.bullet.BulletBoomDefine;
   import dataAll.bullet.BulletBounceDefine;
   import dataAll.bullet.BulletCritDefine;
   import dataAll.bullet.BulletDefine;
   import dataAll.bullet.BulletDefineGroup;
   import dataAll.bullet.BulletFollowDefine;
   import dataAll.bullet.BulletLineDefine;
   import dataAll.bullet.BulletSpeedDefine;
   import dataAll.bullet.position.BulletPositionDefine;
   import dataAll.drop.define.DropColorAll;
   import dataAll.drop.define.DropItemsDefine;
   import dataAll.drop.define.DropItemsDefineGroup;
   import dataAll.drop.week.WeekDropDefine;
   import dataAll.equip.EquipPropertyData;
   import dataAll.equip.creator.EquipDataCreator;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   import dataAll.equip.creator.EquipSkillCreator;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipDefineGroup;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.device.DeviceDefine;
   import dataAll.equip.device.DeviceDefineGroup;
   import dataAll.equip.jewelry.JewelryDefine;
   import dataAll.equip.jewelry.JewelryDefineGroup;
   import dataAll.equip.shield.ShieldDefine;
   import dataAll.equip.shield.ShieldDefineGroup;
   import dataAll.equip.vehicle.ShootVehicleDefine;
   import dataAll.equip.vehicle.VehicleBulletDefine;
   import dataAll.equip.vehicle.VehicleDefine;
   import dataAll.equip.vehicle.VehicleDefineGroup;
   import dataAll.equip.weapon.WeaponDefine;
   import dataAll.equip.weapon.WeaponDefineGroup;
   import dataAll.gift.dailySign.DailyGiftDefine;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftDefineGroup;
   import dataAll.gift.giftHome.GiftHomeDefine;
   import dataAll.gift.giftHome.GiftHomeLevelDefine;
   import dataAll.gift.zhongQiu.DicePackDefine;
   import dataAll.image.ImageUrlDefine;
   import dataAll.image.ImageUrlGroup;
   import dataAll.image.SoundDefineGroup;
   import dataAll.items.define.IO_ItemsDefine;
   import dataAll.level.define.LevelDefine;
   import dataAll.level.define.LevelDefineGroup;
   import dataAll.level.define._fixed.LevelDefineFixedDefine;
   import dataAll.level.define.drop.LevelDropDefine;
   import dataAll.level.define.endless.EndlessGradeDefine;
   import dataAll.level.define.info.LevelInfoDefine;
   import dataAll.level.define.unit.OneUnitDropDefine;
   import dataAll.level.define.unit.OneUnitOrderDefine;
   import dataAll.level.define.unit.UnitOrderDefine;
   import dataAll.level.modeDiy.ModeDiyDefineGroup;
   import dataAll.must.define.MustDefine;
   import dataAll.must.define.MustDefineGroup;
   import dataAll.pet.PetCount;
   import dataAll.pet.gene.creator.GeneDataCreator;
   import dataAll.pet.gene.define.GeneDefine;
   import dataAll.pet.gene.define.GeneDefineGroup;
   import dataAll.pro.NormalPropertyArrayDefineGroup;
   import dataAll.pro.PropertyArrayDefine;
   import dataAll.pro.PropertyArrayDefineGroup;
   import dataAll.pro.dataList.DataListDefineGroup;
   import dataAll.scene.SceneDefineGroup;
   import dataAll.scene.WeatherDefine;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.skill.define.SkillDefine;
   import dataAll.skill.define.SkillDefineGroup;
   import dataAll.skill.define.SkillTargetDefine;
   import dataAll.skill.define.add.SkillAddDefine;
   import dataAll.skill.define.otherObj.SkillBulletObj;
   import dataAll.skill.define.otherObj.SkillSummoneObj;
   import dataAll.test.CheatingDefine;
   import dataAll.test.CheatingDefineGroup;
   import dataAll.test.ZuoBiPaner;
   import dataAll.things.define.ThingsComposeDefine;
   import dataAll.things.define.ThingsDefine;
   import dataAll.things.define.ThingsDefineGroup;
   import dataAll.things.define.ThingsEffectDefine;
   import dataAll.things.define.ThingsLoveDefine;
   import dataAll.ui.choose.ChooseOneAll;
   import dataAll.ui.say.SayListDefineGroup;
   import flash.utils.getTimer;
   
   public class DefineGroup
   {
      
      public static const zuobiNameArr:Array = ["partsProperty,","suitProperty,","blackMarket,","dropColor,","achieve,","skill,","head,","修改了钓鱼数值,"];
      
      private var xmlOut:Object = null;
      
      private var nowGoodMd5Sounto:String = "";
      
      private var dataCtrl:DataCtrl = new DataCtrl();
      
      public var staticCtrl:StaticCtrl = new StaticCtrl();
      
      private var loveXml:LoveXml = new LoveXml();
      
      public var normal:NormalPropertyArrayDefineGroup = new NormalPropertyArrayDefineGroup();
      
      public var modelDiy:ModeDiyDefineGroup = new ModeDiyDefineGroup();
      
      public var imageUrl:ImageUrlGroup = new ImageUrlGroup();
      
      public var sound:SoundDefineGroup = new SoundDefineGroup();
      
      public var body:BodyDefineGroup = new BodyDefineGroup();
      
      public var bullet:BulletDefineGroup = new BulletDefineGroup();
      
      public var armsCharger:ArmsChargerDefineGroup = new ArmsChargerDefineGroup();
      
      public var scene:SceneDefineGroup = new SceneDefineGroup();
      
      public var skill:SkillDefineGroup = new SkillDefineGroup();
      
      public var things:ThingsDefineGroup = new ThingsDefineGroup();
      
      public var gene:GeneDefineGroup = new GeneDefineGroup();
      
      public var geneCreator:GeneDataCreator = new GeneDataCreator();
      
      public var partsProperty:PartsPropertyDefineGroup = new PartsPropertyDefineGroup();
      
      public var equip:EquipDefineGroup = new EquipDefineGroup();
      
      public var suitProperty:PropertyArrayDefineGroup = new PropertyArrayDefineGroup();
      
      public var vehicle:VehicleDefineGroup = new VehicleDefineGroup();
      
      public var device:DeviceDefineGroup = new DeviceDefineGroup();
      
      public var weapon:WeaponDefineGroup = new WeaponDefineGroup();
      
      public var jewelry:JewelryDefineGroup = new JewelryDefineGroup();
      
      public var shield:ShieldDefineGroup = new ShieldDefineGroup();
      
      public var dropItems:DropItemsDefineGroup = new DropItemsDefineGroup();
      
      public var dropColor:DropColorAll = new DropColorAll();
      
      public var level:LevelDefineGroup = new LevelDefineGroup();
      
      public var worldMap:WorldMapDefineGroup = new WorldMapDefineGroup();
      
      public var must:MustDefineGroup = new MustDefineGroup();
      
      public var say:SayListDefineGroup = new SayListDefineGroup();
      
      public var task:TaskDefineGroup = new TaskDefineGroup();
      
      public var goods:GoodsDefineGroup = new GoodsDefineGroup();
      
      public var top:TopBarAll = new TopBarAll();
      
      public var gift:GiftDefineGroup = new GiftDefineGroup();
      
      public var giftHome:GiftHomeDefine = new GiftHomeDefine();
      
      public var active:ActiveDefineGroup = new ActiveDefineGroup();
      
      public var vip:VipDefineGroup = new VipDefineGroup();
      
      public var blackMarket:BlackMarketDefineGroup = new BlackMarketDefineGroup();
      
      public var achieve:AchieveDefineGroup = new AchieveDefineGroup();
      
      public var ask:AskDefineGroup = new AskDefineGroup();
      
      public var count:CountDefineGroup = new CountDefineGroup();
      
      public var keyAction:KeyActionDefineGroup = new KeyActionDefineGroup();
      
      public var union:UnionDefineGroup = new UnionDefineGroup();
      
      public var head:HeadDefineGroup = new HeadDefineGroup();
      
      public var outfit:OutfitDefineGroup = new OutfitDefineGroup();
      
      public var love:LoveDefineAll = new LoveDefineAll();
      
      public var post:PostDefineGroup = new PostDefineGroup();
      
      public var wilder:WilderDefineGroup = new WilderDefineGroup();
      
      public var cheating:CheatingDefineGroup = new CheatingDefineGroup();
      
      public var playerState:PlayerStateDefineGroup = new PlayerStateDefineGroup();
      
      public var peakPro:PeakProDefineGroup = new PeakProDefineGroup();
      
      public var partner:PartnerDefineGroup = new PartnerDefineGroup();
      
      public var dataList:DataListDefineGroup = new DataListDefineGroup();
      
      public var cityBody:CityBodyDefineGroup = new CityBodyDefineGroup();
      
      public var book:BookDefineGroup = new BookDefineGroup();
      
      public var food:FoodDefineGroup = new FoodDefineGroup();
      
      public var craft:CraftDefineGroup = new CraftDefineGroup();
      
      public var editPro:EditProDefineGroup = new EditProDefineGroup();
      
      public var tower:TowerDefineCtrl = new TowerDefineCtrl();
      
      public var unend:UnendDataCtrl = new UnendDataCtrl();
      
      public var choose:ChooseOneAll = new ChooseOneAll();
      
      public var unionInfoGroup:UnionInfoGroup = new UnionInfoGroup();
      
      public var equipCreator:EquipDataCreator = new EquipDataCreator();
      
      public var armsCreator:ArmsDataCreator = new ArmsDataCreator();
      
      public var partsCreator:PartsCreator = new PartsCreator();
      
      private var partsPropertyBase64:String = "";
      
      private var suitPropertyBase64:String = "";
      
      private var blackMarketBase64:String = "";
      
      private var dropColorBase64:String = "";
      
      private var numberArr:Array = [];
      
      private var numberBase64:String = "";
      
      public function DefineGroup()
      {
         super();
         ElementHurt.defineInit();
         ElementShell.defineInit();
      }
      
      public function setXmlOut(out0:Object) : void
      {
         this.xmlOut = out0;
      }
      
      public function outLoginEvent() : void
      {
         this.bullet.outLoginEvent();
      }
      
      public function init() : *
      {
         PetCount.init();
         EquipType.init();
         DicePackDefine.staticInit();
         WeatherDefine.staticInit();
         this.tower.init();
         var tt0:Number = getTimer();
         var out0:Object = this.xmlOut;
         this.imageUrl.inData_byXML(out0.imageUrl);
         this.imageUrl.inData_byXML(out0.imageUrlNew);
         this.dataList.inData_byXML(out0.unendEnemy);
         this.unend.proG.inData_byXML(out0.unendPro);
         this.dataList.inData_byXML(out0.spaceData);
         this.dataList.inData_byXML(out0.peakExp);
         this.dataList.inData_byXML(out0.cityDressMust);
         this.dataList.inData_byXML(out0.blackMarketPrice);
         this.dataList.inData_byXML(out0.petDispatchData);
         this.dataList.inData_byXML(out0.itemsUpgrade);
         this.dataList.inData_byXML(out0.bossEditSkillList);
         this.dataList.inData_byXML(out0.bossMatchList);
         this.dataList.inData_byXML(out0.bossEditCnList);
         this.dataList.inData_byXML(out0.bcardRareSkillList);
         this.dataList.inData_byXML(out0.bcardSkillList);
         this.cityBody.inData_byXML(out0.cityBody);
         this.cityBody.inData_byXML(out0.cityHouse);
         this.cityBody.inData_byXML(out0.cityDecora);
         this.cityBody.inData_byXML(out0.cityUnit);
         this.food.raw.inData_byXML(out0.foodRaw);
         this.food.book.inData_byXML(out0.foodBook);
         this.skill.inData_byXML(out0.foodRaw);
         this.normal.inData_byXML(out0["normal"]);
         this.scene.inData_byXML(out0["scene"]);
         this.body.inData_byXML(out0["hero"],BodySystemType.DEFINE_HERO);
         this.inBodyXml(out0.XiaoHu,BodySystemType.DEFINE_HERO);
         this.inBodyXml(out0.CrossBone,BodySystemType.DEFINE_HERO);
         this.body.inData_byXML(out0["enemy"],BodySystemType.DEFINE_NORMAL);
         this.body.inData_byXML(out0.wilderEnemy,BodySystemType.DEFINE_NORMAL);
         this.body.inData_byXML(out0.ninetyEnemy,BodySystemType.DEFINE_NORMAL);
         this.body.inData_byXML(out0.petBody,BodySystemType.DEFINE_NORMAL);
         this.body.inData_byXML(out0.carBody,BodySystemType.DEFINE_NORMAL);
         this.scene.inData_byXML(out0.scene95);
         this.scene.inData_byXML(out0.scene100);
         this.scene.inData_byXML(out0.sceneSpace);
         this.skill.inData_byXML(out0.sceneSkill);
         this.inBodyXml(out0.KingRabbit);
         this.inBodyXml(out0.PoisonDemon);
         this.inBodyXml(out0.BlackLaer);
         this.inBodyXml(out0.Warrior);
         this.inBodyXml(out0.Madman,BodySystemType.DEFINE_HERO);
         this.inBodyXml(out0.FalconBoss,BodySystemType.DEFINE_HERO);
         this.inBodyXml(out0.CheetahCar);
         this.inBodyXml(out0.PipeZombie);
         this.inBodyXml(out0.PhantomX);
         this.inBodyXml(out0.PhantomZ);
         this.inBodyXml(out0.HookWitch);
         this.inBodyXml(out0.OfficeZombie);
         this.inBodyXml(out0.DrawZombie);
         this.inBodyXml(out0.FireDragon);
         this.inBodyXml(out0.SkeletalMage);
         this.inBodyXml(out0.YouthWolf);
         this.inBodyXml(out0.PoliceZombie);
         this.inBodyXml(out0.Watchdog);
         this.inBodyXml(out0.LaborZombie);
         this.inBodyXml(out0.GasDefenseZombie);
         this.inBodyXml(out0.MeatyZombie);
         this.inBodyXml(out0.DiggingBeast);
         this.inBodyXml(out0.PetBoomSkullS);
         this.inBodyXml(out0.PetIronChiefS);
         this.inBodyXml(out0.PetLaerS);
         this.inBodyXml(out0.BoomSkullS);
         this.inBodyXml(out0.BallLightning);
         this.inBodyXml(out0.IronChiefS);
         this.inBodyXml(out0.LastdayTank);
         this.inBodyXml(out0.TransportZombie);
         this.inBodyXml(out0.FoggyZombie);
         this.inBodyXml(out0.WarriorSec);
         this.inBodyXml(out0.IronDog);
         this.inBodyXml(out0.GreatSageShadow);
         this.inBodyXml(out0.Madboss,BodySystemType.DEFINE_HERO);
         this.inBodyXml(out0.Wanda,BodySystemType.DEFINE_HERO);
         this.inBodyXml(out0.LaoZhang,BodySystemType.DEFINE_HERO);
         this.inBodyXml(out0.Madfire);
         this.inBodyXml(out0.madSpreadBody);
         this.inBodyXml(out0.mother_body);
         this.inBodyXml(out0.bwallBody);
         this.inBodyXml(out0.ArgonShip);
         this.inBodyXml(out0.MinersZombie);
         this.inBodyXml(out0.HealerZombie);
         this.inBodyXml(out0.ClawsZombie);
         this.inBodyXml(out0.DoubleZombie);
         this.inBodyXml(out0.Madgod);
         this.inBodyXml(out0.WatchdogSmall);
         this.inBodyXml(out0.WatchdogAir);
         this.inBodyXml(out0.YingUAV);
         this.inBodyXml(out0.WangLuo,BodySystemType.DEFINE_HERO);
         this.inBodyXml(out0.PythonEmploy,BodySystemType.DEFINE_HERO);
         this.inBodyXml(out0.PythonArmor,BodySystemType.DEFINE_HERO);
         this.inBodyXml(out0.PythonStriker,BodySystemType.DEFINE_HERO);
         this.inBodyXml(out0.PythonSniper,BodySystemType.DEFINE_HERO);
         this.inBodyXml(out0.Domino,BodySystemType.DEFINE_HERO);
         this.inBodyXml(out0.PythonZombie,BodySystemType.DEFINE_HERO);
         this.inBodyXml(out0._01_BaseballZombie);
         this.inBodyXml(out0._02_LoggersZombie);
         this.armsCharger.inData_byXML(out0["armsCharger"]);
         this.armsCharger.skin.inData_byXML(out0.armsSkin);
         this.armsCharger.skin.inData_byXML(out0.weaponSkin);
         this.bullet.inData_byXML(out0["bullet"]);
         this.bullet.inArmsRangeData_byXML(out0.pistol);
         this.bullet.inArmsRangeData_byXML(out0.rifle);
         this.bullet.inArmsRangeData_byXML(out0.sniper);
         this.bullet.inArmsRangeData_byXML(out0.shotgun);
         this.inArmsXml(out0.shotgunSkunk);
         this.bullet.inArmsRangeData_byXML(out0.rocket);
         this.bullet.inArmsRangeData_byXML(out0.crossbow);
         this.bullet.inArmsRangeData_byXML(out0.flamer);
         this.inArmsXml(out0.laser1);
         this.bullet.inData_byXML(out0.enemyBullet);
         this.bullet.inData_byXML(out0.demonBullet);
         this.bullet.inData_byXML(out0.wilderEnemyBullet);
         this.bullet.inData_byXML(out0.ninetyBullet);
         this.bullet.armsName.inData_byXML(out0.armsName);
         this.inArmsXml(out0.pistolFox,true);
         this.bullet.inArmsRangeData_byXML(out0.ghostAxe);
         this.inArmsXml(out0.redFire,true);
         this.inArmsXml(out0.rocketCate);
         this.inArmsXml(out0.meltFlamer);
         this.bullet.inArmsRangeData_byXML(out0.waterFlamer);
         this.bullet.inArmsRangeData_byXML(out0.yearPig);
         this.bullet.inArmsRangeData_byXML(out0.yearDog);
         this.bullet.inArmsRangeData_byXML(out0.yearSnake);
         this.bullet.inArmsRangeData_byXML(out0.yearChicken);
         this.bullet.inArmsRangeData_byXML(out0.yearHourse);
         this.bullet.inArmsRangeData_byXML(out0.yearMouse);
         this.bullet.inArmsRangeData_byXML(out0.yearDragon);
         this.bullet.inArmsRangeData_byXML(out0.lightCone);
         this.bullet.inArmsRangeData_byXML(out0.yearMonkey);
         this.bullet.inArmsRangeData_byXML(out0.yearRabbit);
         this.bullet.inArmsRangeData_byXML(out0.penGun);
         this.bullet.inArmsRangeData_byXML(out0.dreamDie);
         this.bullet.inArmsRangeData_byXML(out0.yearCattle);
         this.bullet.inArmsRangeData_byXML(out0.yearTiger);
         this.bullet.inArmsRangeData_byXML(out0.greedySnake);
         this.bullet.inArmsRangeData_byXML(out0.flySnake);
         this.bullet.inArmsRangeData_byXML(out0.pianoGun);
         this.bullet.inArmsRangeData_byXML(out0.yearSheep);
         this.bullet.inArmsRangeData_byXML(out0.skyArch);
         this.bullet.inArmsRangeData_byXML(out0.arcHow);
         this.bullet.inArmsRangeData_byXML(out0.falconArms);
         this.bullet.inArmsRangeData_byXML(out0.sawEmitter);
         this.inArmsXml(out0.iceCone);
         this.inArmsXml(out0.extremeRocket);
         this.inArmsXml(out0.extremeLaser,true);
         this.inArmsXml(out0.extremeLightning);
         this.inArmsXml(out0.barrenAwn);
         this.inArmsXml(out0.closureGun);
         this.inArmsXml(out0.falconGun);
         this.inArmsXml(out0.extremeGun);
         this.inArmsXml(out0.starTrails);
         this.inArmsXml(out0.roamRocket);
         this.inArmsXml(out0.highlighter,true);
         this.inArmsXml(out0.consVirgo);
         this.inArmsXml(out0.consLibra);
         this.inArmsXml(out0.consLeo);
         this.inArmsXml(out0.bowlGun);
         this.inArmsXml(out0.rainGun);
         this.skill.inData_byXML(out0.redFire);
         this.skill.inData_byXML(out0.rocketCate);
         this.skill.inData_byXML(out0.meltFlamer);
         this.skill.inData_byXML(out0.waterFlamer);
         this.skill.inData_byXML(out0.yearPig);
         this.skill.inData_byXML(out0.yearDog);
         this.skill.inData_byXML(out0.yearSnake);
         this.skill.inData_byXML(out0.yearChicken);
         this.skill.inData_byXML(out0.yearHourse);
         this.skill.inData_byXML(out0.yearMouse);
         this.skill.inData_byXML(out0.yearDragon);
         this.skill.inData_byXML(out0.lightCone);
         this.skill.inData_byXML(out0.yearMonkey);
         this.skill.inData_byXML(out0.yearRabbit);
         this.skill.inData_byXML(out0.penGun);
         this.skill.inData_byXML(out0.yearCattle);
         this.skill.inData_byXML(out0.yearTiger);
         this.skill.inData_byXML(out0.greedySnake);
         this.skill.inData_byXML(out0.pianoGun);
         this.skill.inData_byXML(out0.yearSheep);
         this.skill.inData_byXML(out0.skyArch);
         this.skill.inData_byXML(out0["skill"]);
         this.skill.inData_byXML(out0.enemySkill);
         this.skill.inData_byXML(out0.demonSkill);
         this.skill.inData_byXML(out0.nightmareSkill);
         this.skill.inData_byXML(out0.wilderEnemySkill);
         this.skill.inData_byXML(out0.ninetySkill);
         this.skill.inData_byXML(out0.FightKingSkill);
         this.skill.inData_byXML(out0.SpiderKingSkill);
         this.skill.inData_byXML(out0.FightPigSkill);
         this.skill.inData_byXML(out0.armsSkill);
         this.skill.inData_byXML(out0.heroSkill);
         this.skill.inData_byXML(out0.wenJieSkill);
         this.skill.inData_byXML(out0.xinLingSkill);
         this.skill.inData_byXML(out0.yingSkill);
         this.skill.inData_byXML(out0.equipSkill,true);
         this.skill.inData_byXML(out0.outfitSkill,true);
         this.skill.inData_byXML(out0.vehicleSkill);
         this.gene.inData_byXML(out0.gene);
         this.gene.inProXML(out0.geneProperty);
         this.skill.inData_byXML(out0.petSkill);
         this.gene.strengthenPro.inData_byXML(out0.petStrengthen);
         this.things.inData_byXML(out0["things"]);
         this.things.inData_byXML(out0.things90);
         this.things.inData_byXML(out0.chip);
         this.things.inComposeData_byXML(out0.thingsCompose);
         this.things.inData_byXML(out0["parts"]);
         this.partsProperty.inData_byXML(out0.partsProperty);
         this.partsProperty.rarePro.inData_byXML(out0.partsRarePro);
         this.equip.inImageData_byXML(out0["equipImage"]);
         this.equip.inImageData_byXML(out0.blackEquip);
         this.equip.inImageData_byXML(out0.darkgoldEquip);
         this.skill.inData_byXML(out0.darkgoldEquip);
         this.equip.inFashionData_byXML(out0.fashion);
         this.equip.strengthen.inData_byXML(out0.itemsStrengthen);
         this.suitProperty.inData_byXML(out0.suitProperty);
         this.dropItems.inData_byXML(out0["dropItems"]);
         this.dropColor.inData_byXML(out0["dropColor"]);
         this.worldMap.inData_byXML(out0["worldMap"]);
         this.union.inXml(out0["military"],out0["unionData"],out0.unionTask,out0.unionBattle,out0.unionBuilding,out0.unionBuildingProperty);
         this.skill.inData_byXML(out0.unionSkill);
         this.vehicle.inData_byXML(out0.vehicle);
         this.vehicle.inPropertyData_byXML(out0.vehicleProperty);
         this.bullet.inData_byXML(out0.carBullet);
         this.inVehicleXml(out0._00_DesertTank);
         this.inVehicleXml(out0._01_Daybreak);
         this.inVehicleXml(out0._02_RedReaper);
         this.inVehicleXml(out0._03_Prophet);
         this.inVehicleXml(out0._04_Titans);
         this.inVehicleXml(out0._11_Diggers);
         this.inVehicleXml(out0._12_RedMoto);
         this.inVehicleXml(out0._13_BlueMoto);
         this.inVehicleXml(out0._31_BlueWhale);
         this.inVehicleXml(out0._32_SeaShark);
         this.inVehicleXml(out0._33_Punisher);
         this.inVehicleXml(out0._71_Thunder);
         this.inVehicleXml(out0._72_AircraftGun);
         this.inVehicleXml(out0._73_WatchEagleAir);
         this.inVehicleXml(out0._74_FlyDragonAir);
         this.inVehicleXml(out0._75_SaberTigerCar);
         this.inVehicleXml(out0._76_NianCar);
         this.inVehicleXml(out0._77_FireWolfCar);
         this.inVehicleXml(out0._78_BoneBreaker);
         this.inBodyXml(out0._201_meteorolite);
         this.inBodyXml(out0._301_OreTaper);
         this.inBodyXml(out0._302_OreSaw);
         this.inBodyXml(out0._303_OreOiltank);
         this.inBodyXml(out0._303_OreShip);
         this.inBodyXml(out0._304_OreBall);
         this.inBodyXml(out0._305_OreLaser);
         this.inBodyXml(out0._306_OreFlower);
         this.inBodyXml(out0._307_OreBird);
         this.inBodyXml(out0._308_OreVessel);
         this.inBodyXml(out0._311_OreWorm);
         this.inBodyXml(out0._312_WorldSnake);
         this.inBodyXml(out0.FuGuang);
         this.inBodyXml(out0.WangShu,BodySystemType.DEFINE_HERO);
         this.inCraftXml(out0._00_SilverShip);
         this.level.inData_byXML(out0["level"]);
         this.level.inData_byXML(out0.level31);
         this.level.inData_byXML(out0.level56);
         this.level.inData_byXML(out0.level71);
         this.level.inData_byXML(out0.level76);
         this.level.inData_byXML(out0.level81);
         this.level.inData_byXML(out0.level86);
         this.level.inData_byXML(out0.level_HanGuang1_1);
         this.level.inData_byXML(out0.level_HanGuang2_1);
         this.level.inData_byXML(out0.level_HanGuang3_1);
         this.level.inData_byXML(out0.level_HanGuang4_1);
         this.level.inData_byXML(out0.level_HanGuang5_1);
         this.level.inData_byXML(out0.level_Hospital1_1);
         this.level.inData_byXML(out0.level_Hospital2_1);
         this.level.inData_byXML(out0.level_Hospital3_1);
         this.level.inData_byXML(out0.level_Hospital4_1);
         this.level.inData_byXML(out0.level_Hospital5_1);
         this.level.inData_byXML(out0.level_HospitalUnder);
         this.level.inData_byXML(out0.level_breakFate);
         this.level.inData_byXML(out0.level_XingGu);
         this.level.inData_byXML(out0.level_XiShan);
         this.level.inData_byXML(out0.level_GreenIs);
         this.level.inData_byXML(out0._01_levelSolar);
         this.level.inData_byXML(out0.wilderLevel);
         this.level.inData_byXML(out0.taskLevel);
         this.level.inData_byXML(out0.otherLevel);
         this.level.inData_byXML(out0.deputyLevel);
         this.level.inData_byXML(out0.activeTaskLevel);
         this.level.inData_byXML(out0.zomSpread_level);
         this.level.inData_byXML(out0.madSpread_level);
         this.level.inData_byXML(out0.mother_level);
         this.level.inData_byXML(out0.xiaohu_level);
         this.level.inData_byXML(out0.bwall_level);
         this.level.inData_byXML(out0.custom_level);
         this.level.inData_byXML(out0.memoryLevel);
         this.level.inEndless_byXML(out0.endless);
         this.level.init();
         this.say.inData_byXML(out0.say);
         this.say.inData_byXML(out0.yingSay);
         this.say.inData_byXML(out0.zomSpread_say);
         this.say.inData_byXML(out0.madSpread_say);
         this.say.inData_byXML(out0.mother_say);
         this.say.inData_byXML(out0.xiaohu_say);
         this.say.inData_byXML(out0.bwall_say);
         this.say.inData_byXML(out0.custom_say);
         this.say.inData_byXML(out0.spaceSay);
         this.say.inData_byXML(out0.level_XiShanSay);
         this.say.inData_byXML(out0.level_GreenIsSay);
         this.task.inData_byXML(out0.task);
         this.task.inData_byXML(out0.dayTask);
         this.task.inData_byXML(out0.treasureTask);
         this.task.inData_byXML(out0.kingTask);
         this.task.inData_byXML(out0.deputyTask);
         this.task.inData_byXML(out0.extraTask);
         this.task.inData_byXML(out0.weekTask);
         this.task.inData_byXML(out0.activeTask);
         this.task.inData_byXML(out0.spreadTask);
         this.task.inData_byXML(out0.bwallTask);
         this.task.inData_byXML(out0.customTask);
         this.task.inData_byXML(out0.memoryTask);
         this.goods.inData_byXML(out0.goods);
         this.goods.inData_byXML(out0.arenaStamp);
         this.goods.inData_byXML(out0.scoreGoods);
         this.goods.inData_byXML(out0.taxStamp);
         this.goods.inData_byXML(out0.otherGoods);
         this.goods.inData_byXML(out0.exploitCards);
         this.goods.inData_byXML(out0.anniCoin);
         this.goods.inData_byXML(out0.demBall);
         this.goods.inData_byXML(out0.tenCoin);
         this.goods.inData_byXML(out0.partsCoin);
         this.goods.inData_byXML(out0.activeGoods);
         this.top.inData_byXML(out0.top);
         this.gift.inData_byXML(out0.gift);
         this.gift.inData_byXML(out0.anniverGift);
         this.gift.inData_byXML(out0.arenaGift);
         this.gift.inData_byXML(out0.askGift);
         this.gift.inData_byXML(out0.dailyGift);
         this.gift.inData_byXML(out0.gameBoxGift);
         this.gift.inData_byXML(out0.levelGift);
         this.gift.inData_byXML(out0.zhuoquGift);
         this.gift.inData_byXML(out0.guoQingGift);
         this.gift.inData_byXML(out0.blackMarketThings);
         this.giftHome.inData_byXML(out0.giftHome);
         this.active.inData_byXML(out0.active);
         this.vip.inData_byXML(out0.vip);
         this.blackMarket.inData_byXML(out0.blackMarket);
         this.achieve.inData_byXML(out0.achieve);
         this.achieve.inMedelPropertyXml(out0.medelProperty);
         this.ask.inData_byXML(out0.lifeAsk);
         this.ask.inData_byXML(out0.otherAsk);
         this.post.inData_byXML(out0.post);
         this.post.proData.inData_byXML(out0.postData);
         this.wilder.inData_byXML(out0.wilder);
         this.count.inData_byXML(out0.count);
         this.keyAction.inData_byXML(out0.keyAction);
         this.skill.inData_byXML(out0.deviceSkill);
         this.skill.inData_byXML(out0.weaponSkill);
         this.device.inData_byXML(out0.device);
         this.inDeviceXml(out0.hammerMine);
         this.inDeviceXml(out0.hurtMine);
         this.device.addNewAfterDeviceSkill();
         this.weapon.inData_byXML(out0.weapon);
         this.skill.inData_byXML(out0.jewelry);
         this.jewelry.inData_byXML(out0.jewelry);
         this.skill.inData_byXML(out0.shield);
         this.shield.inData_byXML(out0.shield);
         this.head.inData_byXML(out0.head);
         this.head.inHonor_byXML(out0.headHonor);
         this.outfit.inData_byXML(out0.outfit);
         this.love.inLevelData_byXML(out0.loveLevel);
         this.love.inTalkData_byXML(out0.loveTalk);
         this.skill.inData_byXML(out0.loveSkill,true);
         this.love.inLevelData_byXML(out0.loveLevelWenJie);
         this.love.inTalkData_byXML(this.loveXml.wenJie);
         this.skill.inData_byXML(out0.loveSkillWenJie,true);
         this.love.inLevelData_byXML(out0.loveLevelZangShi);
         this.love.inTalkData_byXML(this.loveXml.zangShi);
         this.skill.inData_byXML(out0.loveSkillZangShi,true);
         this.love.inLevelData_byXML(out0.loveLevelXinLing);
         this.love.inTalkData_byXML(this.loveXml.xinLing);
         this.skill.inData_byXML(out0.loveSkillXinLing,true);
         this.love.inLevelData_byXML(out0.loveLevelXiaoMei);
         this.love.inTalkData_byXML(this.loveXml.xiaoMei);
         this.skill.inData_byXML(out0.loveSkillXiaoMei,true);
         this.cheating.inData_byXML(out0.cheating);
         this.skill.inData_byXML(out0.peakSkill);
         this.skill.inData_byXML(out0.partsSkill);
         this.peakPro.inData_byXML(out0.peakPro);
         this.editPro.inData_byXML(out0.bossEditPro);
         this.editPro.inData_byXML(out0.armsTorPro);
         this.editPro.inData_byXML(out0.aiTorPro);
         BCardPKCreator.inXml(out0.bcardPK);
         BCardPKCreator.inXml(out0.bcardDef);
         this.partner.init();
         this.achieve.afterInit();
         this.unend.afterDeal();
         this.worldMap.afterDeal();
         ArmsEditMethod.afterDefineInit();
         ArmsBookGetter.defineInit();
         this.editPro.afterDeal();
         this.gene.afterDeal();
         this.union.fleshDefine();
         this.dropColor.afterDeal();
         this.things.inBlackEquipFatherArr(this.equip.blackFatherArr);
         this.things.inBlackArmsRangeArr(this.bullet.purgoldArmsRangeArr);
         this.things.inBlackArmsRangeArr(this.bullet.darkgoldArmsRangeArr);
         this.things.inBlackArmsRangeArr(this.bullet.blackArmsRangeArr);
         this.things.inRareArmsRangeArr(this.bullet.rareArmsRangeArr);
         this.things.init();
         this.food.addDropDefine();
         this.dropItems.addThingsAll(this.things.obj);
         this.unionInfoGroup.init();
         this.equipCreator.propertyCtreator.inData_byXML(out0["equipRange"]);
         this.equipCreator.propertyCtreator.inRedProperty(this.suitProperty.propertyArr);
         this.equipCreator.DG = this.equip as EquipDefineGroup;
         SpaceAchieveCtrl.afterGiftInit();
         this.sound.afterInit();
         this.imageUrl.dealBulletAndSkill();
         this.body.afterDeal();
         this.book.saveBookDefine();
         this.goods.outInArr("taxStamp",this.blackMarket.getGoodsDefineArr());
         this.top.addByUnionBattle(this.union.battle);
         this.post.init();
         this.task.afterInit();
         this.level.addCustomTask();
         this.must.init();
         this.skill.init();
         BossEditSkill.init();
         BossCardCreator.init();
         this.goods.fleshAllByDefine();
         ArmsSpecialAndSkill.inSkillData();
         EquipSkillCreator.init();
         this.choose.init();
         this.ask.initPropsData();
         this.ask.initOther();
         WeekDropDefine.afterDefine();
         this.achieve.init();
         this.head.init();
         this.partsPropertyBase64 = Base64.encodeObject(this.partsProperty);
         this.suitPropertyBase64 = Base64.encodeObject(this.suitProperty);
         this.blackMarketBase64 = Base64.encodeObject(this.blackMarket);
         this.dropColorBase64 = Base64.encodeObject(this.dropColor);
         for(var i:int = 0; i < 200; i++)
         {
            if(i < 100)
            {
               this.numberArr.push(Number(Number(i / 100).toFixed(2)));
            }
            else
            {
               this.numberArr.push(i - 100);
            }
         }
         this.numberBase64 = Base64.encodeObject(this.numberArr);
      }
      
      private function inVehicleXml(xml0:XML) : void
      {
         this.vehicle.inData_byXML(xml0);
         this.body.inData_byXML(xml0);
         this.bullet.inData_byXML(xml0);
         this.skill.inData_byXML(xml0);
      }
      
      private function inDeviceXml(xml0:XML) : void
      {
         this.device.inData_byXML(xml0);
         this.body.inData_byXML(xml0);
         this.bullet.inData_byXML(xml0);
         this.skill.inData_byXML(xml0);
      }
      
      private function inCraftXml(xml0:XML) : void
      {
         this.craft.inData_byXML(xml0,"father","craft");
         this.inBodyXml(xml0);
         this.peakPro.inData_byXML(xml0,"father","pro");
      }
      
      private function inBodyXml(xml0:XML, bodyType0:String = "normal") : void
      {
         this.body.inData_byXML(xml0,bodyType0);
         this.bullet.inData_byXML(xml0);
         this.skill.inData_byXML(xml0);
      }
      
      private function inArmsXml(xml0:XML, otherBulletB0:Boolean = false) : void
      {
         this.bullet.inArmsRangeData_byXML(xml0);
         if(otherBulletB0)
         {
            this.bullet.inData_byXML(xml0,"bullet","otherBullet");
         }
         this.skill.inData_byXML(xml0);
      }
      
      public function propertyAdd() : void
      {
         var class0:Class = null;
         this.dataCtrl.propertyAdd();
         ArmsTipDefine.init();
         BulletDefine.pro_arr = ClassProperty.getProArr(new BulletDefine());
         ArmsDefine.pro_arr = ClassProperty.getProArr(new ArmsDefine());
         ArmsSave.pro_arr = ClassProperty.getProArr(new ArmsSave());
         ArmsSave.dHaveProArr = ArrayMethod.intersection(ArmsDefine.pro_arr,ArmsSave.pro_arr);
         ArmsSave.dNoProArr = ArrayMethod.deductArr(ArmsSave.pro_arr,ArmsDefine.pro_arr);
         ArrayMethod.remove(ArmsSave.dNoProArr,"partsSave");
         INIT.tempTrace(ArmsSave.dHaveProArr);
         INIT.tempTrace(ArmsSave.dNoProArr);
         ArmsChargerDefine.pro_arr = ClassProperty.getProArr(new ArmsChargerDefine());
         NormalBodyDefine.pro_arr = ClassProperty.getProArr(new NormalBodyDefine());
         BodyAttackDefine.pro_arr = ClassProperty.getProArr(new BodyAttackDefine());
         HurtData.pro_arr = ClassProperty.getProArr(new HurtData());
         EquipDefine.pro_arr = ClassProperty.getProArr(new EquipDefine());
         EquipPropertyData.pro_arr = ClassProperty.getProArr(new EquipPropertyData());
         EquipFatherDefine.pro_arr = ClassProperty.getProArr(new EquipFatherDefine());
         DropItemsDefine.pro_arr = ClassProperty.getProArr(new DropItemsDefine());
         UnitOrderDefine.pro_arr = ClassProperty.getProArr(new UnitOrderDefine());
         OneUnitOrderDefine.pro_arr = ClassProperty.getProArr(new OneUnitOrderDefine());
         WorldMapDefine.pro_arr = ClassProperty.getProArr(new WorldMapDefine());
         WorldMapLevelDefine.pro_arr = ClassProperty.getProArr(new WorldMapLevelDefine());
         LevelDefine.pro_arr = ClassProperty.getProArr(new LevelDefine());
         LevelDropDefine.pro_arr = ClassProperty.getProArr(new LevelDropDefine());
         LevelInfoDefine.pro_arr = ClassProperty.getProArr(new LevelInfoDefine());
         LevelDefineFixedDefine.pro_arr = ClassProperty.getProArr(new LevelDefineFixedDefine());
         SkillDefine.pro_arr = ClassProperty.getProArr(new SkillDefine());
         HeroSkillDefine.pro_arr = ClassProperty.getProArr(new HeroSkillDefine());
         SkillTargetDefine.pro_arr = ClassProperty.getProArr(new SkillTargetDefine());
         BulletSpeedDefine.pro_arr = ClassProperty.getProArr(new BulletSpeedDefine());
         BulletFollowDefine.pro_arr = ClassProperty.getProArr(new BulletFollowDefine());
         BulletBounceDefine.pro_arr = ClassProperty.getProArr(new BulletBounceDefine());
         BulletCritDefine.pro_arr = ClassProperty.getProArr(new BulletCritDefine());
         BulletBoomDefine.pro_arr = ClassProperty.getProArr(new BulletBoomDefine());
         BulletPositionDefine.pro_arr = ClassProperty.getProArr(new BulletPositionDefine());
         BulletLineDefine.pro_arr = ClassProperty.getProArr(new BulletLineDefine());
         ArmsCrossbowDefine.pro_arr = ClassProperty.getProArr(new ArmsCrossbowDefine());
         ThingsDefine.pro_arr = ClassProperty.getProArr(new ThingsDefine());
         GiftAddDefine.pro_arr = ClassProperty.getProArr(new GiftAddDefine());
         ImageUrlDefine.pro_arr = ClassProperty.getProArr(new ImageUrlDefine());
         TaskDefine.pro_arr = ClassProperty.getProArr(new TaskDefine());
         TaskFatherDefine.pro_arr = ClassProperty.getProArr(new TaskFatherDefine());
         TaskDropDefine.pro_arr = ClassProperty.getProArr(new TaskDropDefine());
         TaskLimitDefine.pro_arr = ClassProperty.getProArr(new TaskLimitDefine());
         SkillBulletObj.pro_arr = ClassProperty.getProArr(new SkillBulletObj());
         SkillSummoneObj.pro_arr = ClassProperty.getProArr(new SkillSummoneObj());
         GoodsDefine.pro_arr = ClassProperty.getProArr(new GoodsDefine());
         GoodsFatherDefine.pro_arr = ClassProperty.getProArr(new GoodsFatherDefine());
         OneUnitDropDefine.pro_arr = ClassProperty.getProArr(new OneUnitDropDefine());
         ThingsEffectDefine.pro_arr = ClassProperty.getProArr(new ThingsEffectDefine());
         TaskConditionDefine.pro_arr = ClassProperty.getProArr(new TaskConditionDefine());
         BodyMoreDefine.pro_arr = ClassProperty.getProArr(new BodyMoreDefine());
         TopBarDefine.pro_arr = ClassProperty.getProArr(new TopBarDefine());
         DpsTopExtra.pro_arr = ClassProperty.getProArr(new DpsTopExtra());
         ArmsTopExtra.pro_arr = ClassProperty.getProArr(new ArmsTopExtra());
         ArenaTopExtra.pro_arr = ClassProperty.getProArr(new ArenaTopExtra());
         UnionBattleExtra.pro_arr = ClassProperty.getProArr(new UnionBattleExtra());
         DailyGiftDefine.pro_arr = ClassProperty.getProArr(new DailyGiftDefine());
         PartsPropertyDefine.pro_arr = ClassProperty.getProArr(new PartsPropertyDefine());
         PartsPropertyRangeDefine.pro_arr = ClassProperty.getProArr(new PartsPropertyRangeDefine());
         VipLevelDefine.pro_arr = ClassProperty.getProArr(new VipLevelDefine());
         BlackMarketWayDefine.pro_arr = ClassProperty.getProArr(new BlackMarketWayDefine());
         AchieveDefine.pro_arr = ClassProperty.getProArr(new AchieveDefine());
         AchieveConditionDefine.pro_arr = ClassProperty.getProArr(new AchieveConditionDefine());
         AchieveFatherDefine.pro_arr = ClassProperty.getProArr(new AchieveFatherDefine());
         MedelProDefine.pro_arr = ClassProperty.getProArr(new MedelProDefine());
         TopBarGatherDefine.pro_arr = ClassProperty.getProArr(new TopBarGatherDefine());
         BodyExtraDefine.pro_arr = ClassProperty.getProArr(new BodyExtraDefine());
         BodyExtraDefineGroup.pro_arr = ClassProperty.getProArr(new BodyExtraDefineGroup());
         GeneDefine.pro_arr = ClassProperty.getProArr(new GeneDefine());
         KeyActionDefine.pro_arr = ClassProperty.getProArr(new KeyActionDefine());
         BodyMotionDefine.pro_arr = ClassProperty.getProArr(new BodyMotionDefine());
         VehicleDefine.pro_arr = ClassProperty.getProArr(new VehicleDefine());
         ShootVehicleDefine.pro_arr = ClassProperty.getProArr(new ShootVehicleDefine());
         ShootVehicleDefine.mePro_arr = ComMethod.deductArr(ShootVehicleDefine.pro_arr,VehicleDefine.pro_arr);
         VehicleBulletDefine.pro_arr = ClassProperty.getProArr(new VehicleBulletDefine());
         EndlessGradeDefine.pro_arr = ClassProperty.getProArr(new EndlessGradeDefine());
         BodyDropDefine.pro_arr = ClassProperty.getProArr(new BodyDropDefine());
         MilitaryDefine.pro_arr = ClassProperty.getProArr(new MilitaryDefine());
         UnionTaskDefine.pro_arr = ClassProperty.getProArr(new UnionTaskDefine());
         UnionBattleMapDefine.pro_arr = ClassProperty.getProArr(new UnionBattleMapDefine());
         DeviceDefine.pro_arr = ClassProperty.getProArr(new DeviceDefine());
         ActiveGiftDefine.pro_arr = ClassProperty.getProArr(new ActiveGiftDefine());
         ActiveTaskDefine.pro_arr = ClassProperty.getProArr(new ActiveTaskDefine());
         WeaponDefine.pro_arr = ClassProperty.getProArr(new WeaponDefine());
         HeadDefine.pro_arr = ClassProperty.getProArr(new HeadDefine());
         HeadConditionDefine.pro_arr = ClassProperty.getProArr(new HeadConditionDefine());
         HeadHonorDefine.pro_arr = ClassProperty.getProArr(new HeadHonorDefine());
         TaskOpenDefine.pro_arr = ClassProperty.getProArr(new TaskOpenDefine());
         SkillAddDefine.pro_arr = ClassProperty.getProArr(new SkillAddDefine());
         OutfitMustDefine.pro_arr = ClassProperty.getProArr(new OutfitMustDefine());
         OutfitDefine.pro_arr = ClassProperty.getProArr(new OutfitDefine());
         LoveTalkDefine.pro_arr = ClassProperty.getProArr(new LoveTalkDefine());
         LoveLevelDefine.pro_arr = ClassProperty.getProArr(new LoveLevelDefine());
         ThingsLoveDefine.pro_arr = ClassProperty.getProArr(new ThingsLoveDefine());
         GiftHomeLevelDefine.pro_arr = ClassProperty.getProArr(new GiftHomeLevelDefine());
         MustDefine.pro_arr = ClassProperty.getProArr(new MustDefine());
         ThingsComposeDefine.pro_arr = ClassProperty.getProArr(new ThingsComposeDefine());
         UnionBuildingDefine.pro_arr = ClassProperty.getProArr(new UnionBuildingDefine());
         var classArr0:Array = [BossEditTopExtra,BodyEditDefine,PlayerStateDefine,CheatingDefine,UnionGeologyThingsDefine,UnionGeologyDefine,ShieldDefine,JewelryDefine,BulletBindingDefine,WilderDropDefine,WilderDefine,WilderFatherDefine,PostDefine,ArmsRecordDefine,UnionSendTaskDefine,UnionCookingDefine,UnionWatchmenDefine];
         for each(class0 in classArr0)
         {
            class0["pro_arr"] = ClassProperty.getProArr(new class0());
         }
      }
      
      public function getPropertyArrayDefine(name0:String) : PropertyArrayDefine
      {
         var d0:PropertyArrayDefine = EquipPropertyDataCreator.getPropertyArrayDefine(name0);
         if(!(d0 is PropertyArrayDefine))
         {
            d0 = this.suitProperty.getDefine(name0);
         }
         if(!(d0 is PropertyArrayDefine))
         {
            d0 = ArmsTipDefine.getDefine(name0);
         }
         return d0;
      }
      
      public function getNumEquipDefine(name0:String) : EquipDefine
      {
         var dgName0:String = null;
         var d0:EquipDefine = null;
         var dgNameArr0:Array = EquipType.numArr;
         for each(dgName0 in dgNameArr0)
         {
            d0 = this[dgName0].getDefine(name0);
            if(Boolean(d0))
            {
               return d0;
            }
         }
         return null;
      }
      
      public function getAllEquipDefine(name0:String) : EquipDefine
      {
         var dgName0:String = null;
         var d0:EquipDefine = this.equip.getDefine(name0);
         if(Boolean(d0))
         {
            return d0;
         }
         var dgNameArr0:Array = EquipType.aloneDefineGroupArr;
         for each(dgName0 in dgNameArr0)
         {
            d0 = this[dgName0].getDefine(name0);
            if(Boolean(d0))
            {
               return d0;
            }
            if(EquipType.name_1Arr.indexOf(dgName0) >= 0)
            {
               d0 = this[dgName0].getDefine(name0 + "_1");
               if(Boolean(d0))
               {
                  return d0;
               }
            }
         }
         return null;
      }
      
      public function getItemsDefine(name0:String) : IO_ItemsDefine
      {
         var d0:IO_ItemsDefine = null;
         d0 = this.getAllEquipDefine(name0);
         if(Boolean(d0))
         {
            return d0;
         }
         d0 = this.bullet.getArmsDefine(name0);
         if(Boolean(d0))
         {
            return d0;
         }
         d0 = this.things.getDefine(name0);
         if(Boolean(d0))
         {
            return d0;
         }
         return null;
      }
      
      private function getNowGoodsMd5Sounto() : String
      {
         var s0:String = null;
         var xml0:XML = null;
         var md50:String = null;
         var sounto0:String = null;
         var tt0:Number = getTimer();
         var arr0:Array = this.xmlOut.md5NameArr;
         if(this.nowGoodMd5Sounto == "")
         {
            s0 = "";
            for each(xml0 in arr0)
            {
               s0 += xml0.toString();
            }
            md50 = MD5.hash(s0);
            sounto0 = TextWay.toCode32(md50);
            this.nowGoodMd5Sounto = sounto0;
         }
         INIT.TRACE("getNowGoodsMd5Sounto耗时：" + (getTimer() - tt0));
         return this.nowGoodMd5Sounto;
      }
      
      public function isGoodsMatchingB() : Boolean
      {
         return this.getNowGoodsMd5Sounto() == this.xmlOut.goodMd5Sounto;
      }
      
      public function zuobiPan() : String
      {
         var funArr0:Array = [this.zuobiPan_define,this.zuobiPan_other];
         return ZuoBiPaner.getStrByFunArr(funArr0);
      }
      
      private function zuobiPan_define() : String
      {
         var n:* = undefined;
         var pro0:String = null;
         var obj0:Object = null;
         var md0:String = null;
         var md2:String = null;
         var arr0:Array = ["partsProperty","suitProperty","blackMarket","dropColor"];
         for(n in arr0)
         {
            pro0 = arr0[n];
            obj0 = this[pro0];
            md0 = Base64.encodeObject(obj0);
            md2 = this[pro0 + "Base64"];
            if(md0 != md2)
            {
               return pro0;
            }
         }
         return "";
      }
      
      private function zuobiPan_number() : String
      {
         var md0:String = Base64.encodeObject(this.numberArr);
         var md2:String = this.numberBase64;
         if(md0 != md2)
         {
            return "修改了钓鱼数值";
         }
         return "";
      }
      
      private function zuobiPan_other() : String
      {
         return "";
      }
      
      public function getIconNum() : int
      {
         var name0:String = null;
         var n0:Number = NaN;
         var num0:int = 0;
         var nameArr0:Array = ["achieve","device","equip","jewelry","vehicle","weapon","shield","gene","head","skill","things"];
         for each(name0 in nameArr0)
         {
            n0 = Number(this[name0].getIconNum());
            num0 += n0;
            trace(name0 + "图标数量：" + n0);
         }
         return num0;
      }
   }
}

