package dataAll.equip.save
{
   import com.sounto.utils.ClassProperty;
   import dataAll.items.save.ItemsSaveGroup;
   
   public class EquipSaveGroup extends ItemsSaveGroup
   {
      
      public static var pro_arr:Array = null;
      
      public static var mePro_arr:Array = [];
      
      public var showFashionB:Boolean = true;
      
      public function EquipSaveGroup()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObjAndClass(obj0,this.classPan);
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
      }
      
      public function clone() : EquipSaveGroup
      {
         var obj0:Object = ClassProperty.copyObj(this);
         var sg0:EquipSaveGroup = new EquipSaveGroup();
         sg0.inData_byObj(obj0);
         return sg0;
      }
      
      private function classPan(dataObj0:Object) : Class
      {
         if(dataObj0.hasOwnProperty("partType"))
         {
            return EquipSave.getSaveClass(dataObj0["partType"]);
         }
         return EquipSave;
      }
      
      override public function replaceItemsName(before0:String, after0:String) : void
      {
         var s0:EquipSave = null;
         for each(s0 in arr)
         {
            if(s0.name == before0)
            {
               s0.name = after0;
               s0.imgName = after0;
            }
         }
      }
      
      public function dealSaveObj(obj0:Object) : void
      {
         var n:* = undefined;
         var oldObj0:Object = null;
         var s0:EquipSave = null;
         var newArr0:Array = [];
         for(n in arr)
         {
            oldObj0 = obj0.arr[n];
            s0 = arr[n];
            newArr0.push(s0.getSaveObj(oldObj0));
         }
         obj0.arr = newArr0;
      }
   }
}

