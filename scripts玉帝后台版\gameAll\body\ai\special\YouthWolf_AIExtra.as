package gameAll.body.ai.special
{
   import dataAll.image.ImageUrlDefine;
   import dataAll.skill.define.SkillDefine;
   import flash.display.BlendMode;
   import flash.geom.Point;
   import gameAll.body.IO_NormalBody;
   import gameAll.effect.NormalEffectAddit;
   
   public class YouthWolf_AIExtra extends Po_AIExtra
   {
      
      private var comboPoint:Point = null;
      
      private var addSkillB:Boolean = false;
      
      public function YouthWolf_AIExtra()
      {
         super();
      }
      
      private function comboAttack() : void
      {
         var p0:Point = null;
         var skillD0:SkillDefine = null;
         var img0:ImageUrlDefine = null;
         var pro0:Number = NaN;
         var c_f0:int = _img.nowMc.currentFrame;
         var t0:IO_NormalBody = _ai.attackAI.targetBody;
         if(c_f0 == 5)
         {
            if(Bo<PERSON>an(t0))
            {
               p0 = new Point();
               p0.y = _mot.y;
               p0.x = t0.getMot().x + (0.5 - Math.random()) * 300;
               this.comboPoint = p0;
               _img.flipToX(p0.x);
               skillD0 = Gaming.defineGroup.skill.getDefine("YouthWolf_combo");
               img0 = skillD0.otherEffectImg;
               if(_img.rightB)
               {
                  img0 = skillD0.pointEffectImg;
               }
               NormalEffectAddit.addEffectInstant(img0,p0.x,p0.y,0);
            }
            else
            {
               this.comboPoint = null;
            }
         }
         else if(c_f0 == 27)
         {
            if(Boolean(this.comboPoint))
            {
               BB.getSkillCtrl().teleport(this.comboPoint.x,this.comboPoint.y);
            }
         }
         else if(c_f0 == 40)
         {
            pro0 = 0.6;
            if(Boolean(t0))
            {
               if(t0.getData().stateD.isInvincibleBuffB())
               {
                  pro0 = 1;
               }
            }
            if(Math.random() < pro0)
            {
               _img.gotoPlayFrame(4);
            }
         }
         if(_dat.getLifePer() > 0.3)
         {
            _dat.stateD.spellImmunityB = true;
            _dat.stateD.noUnderHitB = true;
         }
      }
      
      private function obliqueAttack() : void
      {
         var c_f0:int = _img.nowMc.currentFrame;
         if(c_f0 != 7)
         {
            if(c_f0 == 27)
            {
               Gaming.sceneGroup.extraShake.start(0.3,Math.PI / 2,30,4);
            }
         }
      }
      
      private function shakeAttack() : void
      {
         var c_f0:int = _img.nowMc.currentFrame;
         if(c_f0 == 7)
         {
            BB.getSkillCtrl().teleportToAttackBody(100,0,true);
         }
         else if(c_f0 == 24)
         {
            Gaming.sceneGroup.extraShake.start(0.2,Math.PI / 2,80,4);
         }
      }
      
      override public function FTimer() : void
      {
         var skillD0:SkillDefine = null;
         var img0:ImageUrlDefine = null;
         super.FTimer();
         if(_img.nowLabel == "shakeAttack")
         {
            this.shakeAttack();
         }
         if(_img.nowLabel == "obliqueAttack")
         {
            this.obliqueAttack();
         }
         if(_img.nowLabel == "comboAttack")
         {
            this.comboAttack();
         }
         if(!this.addSkillB)
         {
            if(_dat.getLifePer() <= 0.3)
            {
               BB.getSkill().addSkill_byNameArr(["YouthWolfUnder","YouthWolfHeroAngerAdd"]);
               skillD0 = Gaming.defineGroup.skill.getDefine("YouthWolf_crazy");
               img0 = skillD0.otherEffectImg;
               NormalEffectAddit.addSkillEffectImgInBody(img0,BB);
               _img.blendMode = BlendMode.ADD;
               this.addSkillB = true;
            }
         }
      }
   }
}

