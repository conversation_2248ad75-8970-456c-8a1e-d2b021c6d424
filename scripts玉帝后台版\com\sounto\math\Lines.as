package com.sounto.math
{
   import com.sounto.utils.BlockMethod;
   import com.sounto.utils.ClassProperty;
   import flash.geom.Point;
   
   public class Lines
   {
      
      public static var pro_arr:Array = ["x","y","z","ra","w","len"];
      
      public static const ZERO:Lines = new Lines();
      
      public var x:Number = 0;
      
      public var y:Number = 0;
      
      public var z:Number = 0;
      
      public var ra:Number = 0;
      
      public var w:Number = 0;
      
      public var len:Number = 900;
      
      public function Lines(_x0:Number = 0, _y0:Number = 0, _z0:Number = 0, _ra:Number = 0, _w:Number = 0, _len:Number = 900)
      {
         super();
         this.x = _x0;
         this.y = _y0;
         this.z = _z0;
         this.ra = _ra;
         this.w = _w;
         this.len = _len;
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         if(Boolean(obj0))
         {
            ClassProperty.inDataOnlyValue(this,obj0,pro_arr);
         }
      }
      
      public function clone() : Lines
      {
         return new Lines(this.x,this.y,this.z,this.ra,this.w,this.len);
      }
      
      public function init() : void
      {
         this.x = 0;
         this.y = 0;
         this.z = 0;
         this.ra = 0;
         this.w = 0;
         this.len = 0;
      }
      
      public function toString() : String
      {
         return "x:" + this.x + ",y:" + this.y + ",ra:" + this.ra + ",w:" + this.w;
      }
      
      public function get imgX() : Number
      {
         return this.x;
      }
      
      public function get imgY() : Number
      {
         return this.z * BlockMethod.ZMUL + this.y;
      }
      
      public function set imgX(v0:Number) : void
      {
         this.x = v0;
      }
      
      public function set imgY(v0:Number) : void
      {
         this.y = v0 - this.z * BlockMethod.ZMUL;
      }
      
      public function getEndX() : Number
      {
         return this.x + Math.cos(this.ra) * this.len;
      }
      
      public function getEndY() : Number
      {
         return this.y + Math.sin(this.ra) * this.len;
      }
      
      public function countGap(l0:Object) : Number
      {
         return Math.sqrt((this.x - l0.x) * (this.x - l0.x) + (this.y - l0.y) * (this.y - l0.y) + (this.z - l0.z) * (this.z - l0.z));
      }
      
      public function countPointGap(l0:Point) : Number
      {
         return Math.sqrt((this.x - l0.x) * (this.x - l0.x) + (this.y - l0.y) * (this.y - l0.y));
      }
      
      public function countXYZGap(x0:Number, y0:Number, z0:Number) : Number
      {
         return Math.sqrt((this.x - x0) * (this.x - x0) + (this.y - y0) * (this.y - y0) + (this.z - z0) * (this.z - z0));
      }
      
      public function getPartNoLinesB() : Boolean
      {
         return this.w == -1;
      }
   }
}

