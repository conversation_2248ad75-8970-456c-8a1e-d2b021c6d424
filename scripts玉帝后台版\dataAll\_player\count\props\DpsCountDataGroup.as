package dataAll._player.count.props
{
   public class DpsCountDataGroup
   {
      
      public var out:DpsCountData = new DpsCountData();
      
      public var act:DpsCountData = new DpsCountData();
      
      public function DpsCountDataGroup()
      {
         super();
         this.act.allColor = "FFFF00";
      }
      
      public function FTimerSecond() : void
      {
         this.out.FTimerSecond();
         this.act.FTimerSecond();
      }
      
      public function getImageDataArray(valueB0:Boolean = true, arrangeB0:Boolean = true) : Array
      {
         var da0:DpsCountData = null;
         var arr0:Array = [];
         var daArr0:Array = [this.act];
         for each(da0 in daArr0)
         {
            arr0 = arr0.concat(da0.getImageDataArr(valueB0,arrangeB0));
         }
         return arr0;
      }
   }
}

