package dataAll.pet.gene.save
{
   import com.common.data.Base64;
   import com.sounto.utils.ClassProperty;
   import dataAll.equip.define.EquipColor;
   import dataAll.items.save.ItemsSave;
   import dataAll.items.save.ShopItemsSave;
   import dataAll.pet.PetCount;
   import dataAll.pet.gene.creator.GeneDataGrowthCtrl;
   import dataAll.pet.gene.define.GeneDefine;
   
   public class GeneSave extends ShopItemsSave
   {
      
      public static var pro_arr:Array = [];
      
      public static var mePro_arr:Array = null;
      
      private var _obj:String = "";
      
      public var talentSkillArr:Array = [];
      
      public var laterSkillArr:Array = [];
      
      private var _growObj:String = "";
      
      public function GeneSave()
      {
         super();
         this.growObj = {};
      }
      
      public function set obj(obj0:Object) : void
      {
         this._obj = Base64.encodeObject(obj0);
      }
      
      public function get obj() : Object
      {
         if(this._obj != "")
         {
            return Base64.decodeObject(this._obj);
         }
         return {};
      }
      
      public function set growObj(growObj0:Object) : void
      {
         this._growObj = Base64.encodeObject(growObj0);
      }
      
      public function get growObj() : Object
      {
         if(this._growObj != "")
         {
            return Base64.decodeObject(this._growObj);
         }
         return {};
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObj(obj0);
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
         this.obj = ClassProperty.copyObj(obj0["obj"]);
         this.growObj = ClassProperty.copyObj(obj0["growObj"]);
      }
      
      public function clone() : GeneSave
      {
         var s0:GeneSave = new GeneSave();
         s0.inData_byObj(this);
         return s0;
      }
      
      override public function copyOne() : ItemsSave
      {
         return this.clone();
      }
      
      public function getLifeRateGrowObj(baseLifeRate0:Number) : Object
      {
         var obj0:Object = this.growObj;
         if(obj0.hasOwnProperty("lifeRate"))
         {
            obj0["lifeRate"] = int(obj0["lifeRate"] * baseLifeRate0);
         }
         return obj0;
      }
      
      public function getGrowAllPer() : Number
      {
         return GeneDataGrowthCtrl.getAllProValue(this.growObj);
      }
      
      public function inDataByDefine(d0:GeneDefine) : void
      {
         name = d0.name;
         cnName = d0.getTrueCnName();
      }
      
      public function transferOther(s0:GeneSave) : void
      {
         var obj0:Object = ClassProperty.copyObj(this.growObj);
         this.growObj = ClassProperty.copyObj(s0.growObj);
         s0.growObj = obj0;
      }
      
      override public function getTrueLevel() : int
      {
         return itemsLevel + addLevel;
      }
      
      public function getGeneDropLevel() : int
      {
         return this.getTrueLevel() + PetCount.cLevel;
      }
      
      public function getAllSkillArr() : Array
      {
         return this.talentSkillArr.concat(this.laterSkillArr);
      }
      
      public function getDefine() : GeneDefine
      {
         return Gaming.defineGroup.gene.getDefine(name);
      }
      
      public function dealRed496() : Boolean
      {
         var obj0:Object = null;
         if(color == EquipColor.RED)
         {
            obj0 = this.growObj;
            if(obj0["dpsMul"] >= 0.535 && obj0["hurtMul"] >= 0.535)
            {
               obj0["dpsMul"] = 0.2;
               obj0["hurtMul"] = 0.2;
               this.growObj = obj0;
               return true;
            }
         }
         return false;
      }
   }
}

