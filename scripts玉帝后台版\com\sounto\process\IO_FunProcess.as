package com.sounto.process
{
   public interface IO_FunProcess
   {
      
      function start(param1:Function = null) : void;
      
      function getState() : String;
      
      function getProPer() : Number;
      
      function setProPer(param1:Number) : void;
      
      function getProcessPer() : Number;
      
      function stopAndClear() : void;
      
      function FTimer() : void;
   }
}

