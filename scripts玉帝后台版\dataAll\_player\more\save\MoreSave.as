package dataAll._player.more.save
{
   import com.sounto.utils.ClassProperty;
   import dataAll._player.more.MorePlayerSave;
   import dataAll._player.more.NormalPlayerSave;
   import dataAll.body.define.HeroDefine;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.save.ItemsSave;
   
   public class MoreSave extends ItemsSave
   {
      
      public static var pro_arr:Array = null;
      
      public static var mePro_arr:Array = null;
      
      protected static const swapProArr:Array = ["name","cnName"];
      
      public var SAVE:NormalPlayerSave = new MorePlayerSave();
      
      public function MoreSave()
      {
         super();
         itemsType = ItemsDataGroup.TYPE_MORE;
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObj(obj0);
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
         itemsType = ItemsDataGroup.TYPE_MORE;
      }
      
      public function getSwapObj() : Object
      {
         var name0:String = null;
         var obj0:Object = {};
         for each(name0 in swapProArr)
         {
            obj0[name0] = this[name0];
         }
         return obj0;
      }
      
      private function inSwapObj(obj0:Object) : void
      {
         var name0:String = null;
         var da0:* = undefined;
         for each(name0 in swapProArr)
         {
            da0 = obj0[name0];
            this[name0] = da0;
         }
      }
      
      public function swap(s0:MoreSave) : Boolean
      {
         var obj0:Object = null;
         if(s0 != this)
         {
            obj0 = this.getSwapObj();
            this.inSwapObj(s0);
            s0.inSwapObj(obj0);
            return true;
         }
         return false;
      }
      
      public function inDataByHeroDefine(d0:HeroDefine) : void
      {
         this.SAVE.initSave();
         name = d0.name;
         cnName = d0.cnName;
      }
   }
}

