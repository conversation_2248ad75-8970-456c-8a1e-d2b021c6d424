package dataAll.gift.school
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   
   public class GiftChipBattleSave
   {
      
      public static const START_TIME:String = "2017-4-10";
      
      public static const END_TIME:String = "2017-4-30";
      
      public static const taskNameArr:Array = ["dayTask","extraTask","level","arena"];
      
      public static const dayTaskNameArr:Array = ["dailySign","ask","normalLevel","treasureTask","strength"];
      
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var oneGetB:Boolean = false;
      
      public var weekGetB:Boolean = false;
      
      public function GiftChipBattleSave()
      {
         super();
         this.nowWeek = 1;
         this.initData();
      }
      
      public function get nowWeek() : Number
      {
         return this.CF.getAttribute("nowWeek");
      }
      
      public function set nowWeek(v0:Number) : void
      {
         this.CF.setAttribute("nowWeek",v0);
      }
      
      public function get dayTask() : Number
      {
         return this.CF.getAttribute("dayTask");
      }
      
      public function set dayTask(v0:Number) : void
      {
         this.CF.setAttribute("dayTask",v0);
      }
      
      public function get extraTask() : Number
      {
         return this.CF.getAttribute("extraTask");
      }
      
      public function set extraTask(v0:Number) : void
      {
         this.CF.setAttribute("extraTask",v0);
      }
      
      public function get level() : Number
      {
         return this.CF.getAttribute("level");
      }
      
      public function set level(v0:Number) : void
      {
         this.CF.setAttribute("level",v0);
      }
      
      public function get arena() : Number
      {
         return this.CF.getAttribute("arena");
      }
      
      public function set arena(v0:Number) : void
      {
         this.CF.setAttribute("arena",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      private function initData() : void
      {
         var name0:String = null;
         for each(name0 in taskNameArr)
         {
            this[name0] = 0;
         }
      }
      
      public function testCompleteAll() : void
      {
         var name0:String = null;
         for each(name0 in taskNameArr)
         {
            this[name0] = this.getMax(name0);
         }
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.oneGetB = false;
         var cday0:int = StringDate.compareDateByStr(START_TIME,timeStr0);
         if(cday0 >= this.nowWeek * 7)
         {
            this.nowWeek = int(cday0 / 7) + 1;
            this.weekGetB = false;
            this.initData();
         }
      }
      
      public function getWeekSurplus(timeStr0:String) : int
      {
         var cday0:int = StringDate.compareDateByStr(START_TIME,timeStr0);
         return 7 - cday0 % 7;
      }
      
      public function getGiftEvent(btnLabel0:String) : void
      {
         this[btnLabel0 + "GetB"] = true;
      }
      
      private function dayTask_max() : Number
      {
         return 10;
      }
      
      private function extraTask_max() : Number
      {
         return 6;
      }
      
      private function level_max() : Number
      {
         return 15;
      }
      
      private function arena_max() : Number
      {
         return 10;
      }
      
      public function getNow(name0:String) : Number
      {
         return this[name0];
      }
      
      public function getMax(name0:String) : Number
      {
         return this[name0 + "_max"]();
      }
      
      public function getDayMax(name0:String) : Number
      {
         if(name0 == "normalLevel")
         {
            return 3;
         }
         if(name0 == "treasureTask")
         {
            return 2;
         }
         return 1;
      }
   }
}

