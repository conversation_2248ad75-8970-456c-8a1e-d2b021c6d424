package dataAll._player.role
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.StringMethod;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.body.define.BodySex;
   import dataAll.body.define.HeroDefine;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipType;
   import gameAll.body.data.NormalBodyStateData;
   import gameAll.body.skill.StateData;
   
   public class RoleName
   {
      
      public static const winTask:String = "Hospital5_plot";
      
      public static const Striker:String = "Striker";
      
      public static const Girl:String = "Girl";
      
      public static const WenJie:String = "WenJie";
      
      public static const ZangShi:String = "ZangShi";
      
      public static const XinLing:String = "XinLing";
      
      public static const XiaoMei:String = "XiaoMei";
      
      public static const XiaoHu:String = "XiaoHu";
      
      public static const CrossBone:String = "CrossBone";
      
      public static const arr:Array = [Striker,<PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>];
      
      public static const partnerArr:Array = [<PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>];
      
      public static const loveOpenArr:Array = [<PERSON>,<PERSON><PERSON>ie,<PERSON>angShi,XinLing,XiaoMei];
      
      public static const loginArr:Array = [Striker,WenJie,ZangShi,Girl,XinLing,XiaoMei];
      
      public static const loginUnlockArr:Array = [Striker,WenJie,Girl];
      
      public static const MAIN:String = Striker;
      
      public static const MAIN_CN:String = "我";
      
      public function RoleName()
      {
         super();
      }
      
      public static function getOneBySex(sex0:String) : String
      {
         if(sex0 == BodySex.MALE)
         {
            return Striker;
         }
         return Girl;
      }
      
      public static function getNewPartnerArr(roleName0:String) : Array
      {
         if(roleName0 == Girl)
         {
            return [Striker,WenJie];
         }
         return null;
      }
      
      public static function getNewPartnerBattleNum(roleName0:String) : int
      {
         if(roleName0 == Girl)
         {
            return 2;
         }
         return -1;
      }
      
      public static function dealSkillArr(d0:HeroDefine, skillArr0:Array, p1RoleName0:String) : Array
      {
         if(p1RoleName0 == Girl)
         {
            if(d0.name == Girl)
            {
               ArrayMethod.moveOne(skillArr0,"妖魅",0);
            }
            else if(d0.name == WenJie || d0.name == Striker)
            {
               ArrayMethod.moveOne(skillArr0,"群体自燃",0);
               ArrayMethod.moveOne(skillArr0,"魅惑",0);
            }
         }
         return skillArr0;
      }
      
      public static function getCpRole(name0:String) : String
      {
         if(name0 == Striker)
         {
            return Girl;
         }
         if(name0 == Girl)
         {
            return Striker;
         }
         if(name0 == WenJie)
         {
            return XiaoMei;
         }
         if(name0 == XiaoMei)
         {
            return WenJie;
         }
         if(name0 == ZangShi)
         {
            return XinLing;
         }
         if(name0 == XinLing)
         {
            return ZangShi;
         }
         return "";
      }
      
      public static function haveRoleNamePan(str0:String) : String
      {
         var s0:String = null;
         for each(s0 in arr)
         {
            if(str0.indexOf(s0) >= 0)
            {
               return s0;
            }
         }
         return "";
      }
      
      public static function clearRoleName(str0:String) : String
      {
         var s0:String = null;
         for each(s0 in arr)
         {
            str0 = StringMethod.replaceStr(str0,s0,"");
         }
         return str0;
      }
      
      public static function getLoginTip(name0:String) : String
      {
         var tip0:String = "";
         var d0:HeroDefine = Gaming.defineGroup.body.getHeroDefine(name0);
         var unlockB0:Boolean = loginUnlockArr.indexOf(name0) >= 0;
         if(unlockB0)
         {
            tip0 += "<yellow P1专属技能：/>" + d0.p1SkillArr + "";
            tip0 += "\n\n<yellow 初始队友：/>";
            if(name0 == Striker)
            {
               tip0 += "文杰";
            }
            else if(name0 == WenJie)
            {
               tip0 += "小白";
            }
            else if(name0 == Girl)
            {
               tip0 += "小白、文杰";
            }
            return tip0;
         }
         return "该角色未开放";
      }
      
      public static function getMemoryArrByLv(lv0:int) : Array
      {
         var arr0:Array = [Striker,WenJie];
         if(lv0 >= 20)
         {
            arr0.push(ZangShi);
         }
         if(lv0 >= 60)
         {
            arr0.push(Girl);
         }
         if(lv0 >= 93)
         {
            arr0.push(XinLing);
         }
         return arr0;
      }
      
      public static function getMemoryText(lv0:int) : String
      {
         var a0:Array = [];
         var roleArr0:Array = getMemoryArrByLv(lv0);
         var cnArr0:Array = Gaming.defineGroup.body.getCnArrByNameArr(roleArr0);
         return StringMethod.concatStringArr(cnArr0,99);
      }
      
      public static function haveEquipShowB(da0:EquipData, pd0:NormalPlayerData) : Boolean
      {
         var d0:HeroDefine = null;
         if(da0.isNormalEquipB() && Boolean(pd0))
         {
            d0 = pd0.heroData.def;
            if(d0.sex == BodySex.MALE)
            {
               if(da0.save.partType == EquipType.HEAD)
               {
                  return d0.name == Striker;
               }
               return true;
            }
            return false;
         }
         return true;
      }
      
      public static function getRoleArrByTask(name0:String) : Array
      {
         if(name0 == "XiShan1_plotOther" || name0 == "leavePartner_WenJie" || name0 == "madPlot_WenJie" || name0 == "BaWang_otherRole")
         {
            return [WenJie,ZangShi,Girl,XinLing];
         }
         if(name0 == "beatIronDog_otherRole")
         {
            return [Striker];
         }
         if(name0 == "GreenTown1_plot")
         {
            return [Striker,WenJie];
         }
         if(name0 == "beforeXiShan1")
         {
            return [ZangShi,Girl,XinLing,XiaoMei];
         }
         return null;
      }
      
      public static function getArmsAddLifeMul(name0:String) : Number
      {
         if(name0 == Striker)
         {
            return 2;
         }
         if(name0 == Girl)
         {
            return 3;
         }
         return 1;
      }
      
      public static function dealUnendBuff(name0:String, da0:StateData, state0:NormalBodyStateData) : void
      {
         if(name0 == RoleName.Striker)
         {
            state0.underHurtMul /= 2;
            state0.addAllHurtMul(1.3,da0);
            state0.noDizzinessB = true;
         }
         else if(name0 == RoleName.Girl)
         {
            state0.underHurtMul /= 2;
         }
      }
      
      public static function noGlobalSpurtingB(name0:String) : Boolean
      {
         if(name0 == Striker || name0 == Girl)
         {
            return true;
         }
         return false;
      }
      
      public static function getUnderHurtMulLimit(name0:String) : Number
      {
         if(name0 == Striker)
         {
            return 0.35;
         }
         if(name0 == Girl)
         {
            return 0.35;
         }
         return 0.2;
      }
   }
}

