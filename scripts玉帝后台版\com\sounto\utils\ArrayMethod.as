package com.sounto.utils
{
   public class ArrayMethod
   {
      
      private static var proName:String = "";
      
      private static var ascendingB:Boolean = true;
      
      public function ArrayMethod()
      {
         super();
      }
      
      public static function toObj(arr0:Array, idPro0:String = "name") : Object
      {
         var b0:Object = null;
         var obj0:Object = {};
         for each(b0 in arr0)
         {
            obj0[b0[idPro0]] = b0;
         }
         return obj0;
      }
      
      public static function getElementsArr(arr0:Array, idPro0:String = "name") : Array
      {
         var b0:Object = null;
         var earr0:Array = [];
         for each(b0 in arr0)
         {
            earr0.push(b0[idPro0]);
         }
         return earr0;
      }
      
      public static function replace(arr0:Array, before0:*, after0:*) : void
      {
         var f0:int = remove(arr0,before0);
         if(f0 >= 0)
         {
            arr0.splice(f0,0,after0);
         }
      }
      
      public static function getPro_byArr(arr0:Array) : int
      {
         var n:* = undefined;
         var ran0:Number = Math.random();
         var num0:Number = 0;
         for(n in arr0)
         {
            num0 += Number(arr0[n]);
            if(ran0 < num0)
            {
               return n;
            }
         }
         return -1;
      }
      
      public static function getPro_byArrSum(arr0:Array) : int
      {
         var n:* = undefined;
         var num0:Number = NaN;
         var ran0:Number = Math.random();
         var max0:Number = 0;
         for(n in arr0)
         {
            max0 += Number(arr0[n]);
         }
         num0 = 0;
         for(n in arr0)
         {
            num0 += Number(arr0[n]);
            if(ran0 < num0 / max0)
            {
               return n;
            }
         }
         return arr0.length - 1;
      }
      
      public static function getRandomArray(arr0:Array, num0:int, repeatB0:Boolean = false, proName0:String = "") : Array
      {
         var newArr0:Array = null;
         var s_arr0:Array = null;
         var i:int = 0;
         var pro_arr0:Array = null;
         var index0:int = 0;
         var len0:int = int(arr0.length);
         if(!repeatB0 && num0 >= len0 || len0 == 0)
         {
            return arr0.concat([]);
         }
         newArr0 = [];
         s_arr0 = arr0.concat([]);
         if(proName0 != "")
         {
            pro_arr0 = getProArrByArr(s_arr0,proName0);
         }
         for(i = 0; i < num0; i++)
         {
            index0 = -1;
            if(Boolean(pro_arr0))
            {
               index0 = getPro_byArrSum(pro_arr0);
            }
            else
            {
               index0 = Math.random() * s_arr0.length;
            }
            newArr0.push(s_arr0[index0]);
            if(!repeatB0)
            {
               s_arr0.splice(index0,1);
               if(Boolean(pro_arr0))
               {
                  pro_arr0.splice(index0,1);
               }
            }
         }
         return newArr0;
      }
      
      public static function getProArrByArr(arr0:Array, proName0:String) : Array
      {
         var b0:* = undefined;
         var a0:Array = [];
         for each(b0 in arr0)
         {
            if(b0[proName0] is Function)
            {
               a0.push(b0[proName0]());
            }
            else
            {
               a0.push(b0[proName0]);
            }
         }
         return a0;
      }
      
      public static function getRandomOne(arr0:Array) : *
      {
         return arr0[int(Math.random() * arr0.length)];
      }
      
      public static function getNextOne(now0:*, arr0:Array) : *
      {
         if(arr0.length == 0)
         {
            return null;
         }
         var i0:int = int(arr0.indexOf(now0));
         i0++;
         if(i0 > arr0.length - 1)
         {
            i0 %= arr0.length;
         }
         return arr0[i0];
      }
      
      public static function getNumberArrSum(arr0:Array) : Number
      {
         var num0:Number = NaN;
         var v0:Number = 0;
         for each(num0 in arr0)
         {
            v0 += num0;
         }
         return v0;
      }
      
      public static function remove(arr0:Array, element0:Object) : int
      {
         var f0:int = int(arr0.indexOf(element0));
         if(f0 >= 0)
         {
            arr0.splice(f0,1);
         }
         return f0;
      }
      
      public static function deductArr(arr0:Array, arr1:Array) : Array
      {
         var n:* = undefined;
         var arr2:Array = [];
         for(n in arr0)
         {
            if(arr1.indexOf(arr0[n]) == -1)
            {
               arr2.push(arr0[n]);
            }
         }
         return arr2;
      }
      
      public static function fixedNumberArr(arr0:Array, arr2:Array) : Array
      {
         var n:* = undefined;
         var newArr0:Array = arr0.concat([]);
         for(n in arr2)
         {
            if(!newArr0[n])
            {
               newArr0[n] = 0;
            }
            newArr0[n] += arr2[n];
         }
         return newArr0;
      }
      
      public static function intersection(arr0:Array, arr1:Array) : Array
      {
         var e0:* = undefined;
         var a2:Array = [];
         for each(e0 in arr0)
         {
            if(arr1.indexOf(e0) >= 0)
            {
               a2.push(e0);
            }
         }
         return a2;
      }
      
      public static function concatArrNull(arr0:Array, arr2:Array) : Array
      {
         if(arr0 == null)
         {
            arr0 = [];
         }
         if(Boolean(arr2) && arr2.length > 0)
         {
            arr0 = arr0.concat(arr2);
         }
         return arr0;
      }
      
      public static function addNoRepeatInArr(arr0:Array, v0:*) : Boolean
      {
         if(arr0.indexOf(v0) == -1)
         {
            arr0.push(v0);
            return true;
         }
         return false;
      }
      
      public static function addNoRepeatArrInArr(arr0:Array, arr2:Array) : void
      {
         var v0:* = undefined;
         for each(v0 in arr2)
         {
            if(arr0.indexOf(v0) == -1)
            {
               arr0.push(v0);
            }
         }
      }
      
      public static function clearRepeat(arr0:Array) : Array
      {
         var b0:* = undefined;
         var newArr0:Array = [];
         for each(b0 in arr0)
         {
            if(newArr0.indexOf(b0) == -1)
            {
               newArr0.push(b0);
            }
         }
         return newArr0;
      }
      
      public static function delOneInArr(arr0:Array, v0:*) : Boolean
      {
         var i0:int = int(arr0.indexOf(v0));
         if(i0 == -1)
         {
            return false;
         }
         arr0.splice(i0,1);
         return true;
      }
      
      public static function delSameStringInArr(arr0:Array, v0:String) : Boolean
      {
         var len0:int = 0;
         var i:int = 0;
         var s0:String = null;
         var i0:int = int(arr0.indexOf(v0));
         if(i0 == -1)
         {
            len0 = int(arr0.length);
            for(i = 0; i < len0; i++)
            {
               s0 = arr0[i];
               if(s0.indexOf(v0) >= 0)
               {
                  i0 = i;
                  break;
               }
            }
         }
         if(i0 == -1)
         {
            return false;
         }
         arr0.splice(i0,1);
         return true;
      }
      
      public static function getElement(arr0:Array, index0:int, less0:Object, more0:Object) : Object
      {
         if(index0 < 0)
         {
            return less0;
         }
         if(index0 > arr0.length - 1)
         {
            return more0;
         }
         return arr0[index0];
      }
      
      public static function getElementLimit(arr0:Array, index0:int) : *
      {
         if(index0 < 0)
         {
            return arr0[0];
         }
         if(index0 > arr0.length - 1)
         {
            return arr0[arr0.length - 1];
         }
         return arr0[index0];
      }
      
      public static function sortNumberArray(arr0:Array) : void
      {
         arr0.sort(sortNumberFun);
      }
      
      public static function sortNumberFun(a0:*, b0:*) : int
      {
         if(a0 < b0)
         {
            return -1;
         }
         if(a0 == b0)
         {
            return 0;
         }
         return 1;
      }
      
      public static function sortObjectFun(a0:Object, b0:Object) : int
      {
         if(a0 != null && b0 != null)
         {
            if(a0 < b0)
            {
               return -1;
            }
            if(a0 == b0)
            {
               return 0;
            }
            return 1;
         }
         return 0;
      }
      
      public static function randomSortFun(a0:*, b0:*) : int
      {
         if(Math.random() < 0.5)
         {
            return -1;
         }
         return 1;
      }
      
      public static function sortArrByProName(arr0:Array, proName0:String, ascendingB0:Boolean = true) : void
      {
         proName = proName0;
         ascendingB = ascendingB0;
         arr0.sort(sortArrByProNameFun);
      }
      
      private static function sortArrByProNameFun(a0:*, b0:*) : int
      {
         var obj1:Object = a0[proName];
         var obj2:Object = b0[proName];
         if(obj1 != null && obj2 != null)
         {
            return (ascendingB ? 1 : -1) * sortObjectFun(obj1,obj2);
         }
         return 0;
      }
      
      public static function moveArr(arr0:Array, c0:int) : Array
      {
         var i:int = 0;
         var index0:int = 0;
         var len0:int = int(arr0.length);
         var newArr0:Array = [];
         if(len0 > 0)
         {
            for(i = 0; i < len0; i++)
            {
               index0 = (i + c0 % len0 + len0) % len0;
               newArr0[i] = arr0[index0];
            }
         }
         return newArr0;
      }
      
      public static function moveOne(arr:Array, element:*, newIndex:int) : Boolean
      {
         if(newIndex < 0 || newIndex >= arr.length)
         {
            return false;
         }
         var currentIndex:int = int(arr.indexOf(element));
         if(currentIndex == -1)
         {
            return false;
         }
         if(currentIndex == newIndex)
         {
            return true;
         }
         arr.splice(currentIndex,1);
         arr.splice(newIndex,0,element);
         return true;
      }
      
      public static function indexOfStringInArr(arr0:Array, s0:String) : Boolean
      {
         var str0:String = null;
         for each(str0 in arr0)
         {
            if(str0.indexOf(s0) >= 0)
            {
               return true;
            }
         }
         return false;
      }
      
      public static function stringSamePan(arr1:Array, arr2:Array) : Boolean
      {
         var arr_len0:int = int(arr1.length);
         if(arr_len0 != arr2.length)
         {
            return false;
         }
         for(var i:int = 0; i < arr_len0; i++)
         {
            if(arr1[i] != arr2[i])
            {
               return false;
            }
         }
         return true;
      }
      
      public static function elementSamePan(arr1:Array, arr2:Array) : Boolean
      {
         var n:* = undefined;
         var v1:* = undefined;
         var v2:* = undefined;
         if(arr1 == arr2)
         {
            return true;
         }
         if(!arr1 || !arr2)
         {
            return false;
         }
         if(arr1.length != arr2.length)
         {
            return false;
         }
         for(n in arr1)
         {
            v1 = arr1[n];
            v2 = arr2[n];
            if(typeof v1 != typeof v2)
            {
               return false;
            }
            if(v1 != v2)
            {
               return false;
            }
         }
         return true;
      }
      
      public static function samePan(arr1:Array, arr2:Array) : Boolean
      {
         var n:* = undefined;
         var v1:* = undefined;
         var v2:* = undefined;
         var bb0:Boolean = false;
         if(arr1 == arr2)
         {
            return true;
         }
         if(!arr1 || !arr2)
         {
            return false;
         }
         if(arr1.length != arr2.length)
         {
            return false;
         }
         for(n in arr1)
         {
            v1 = arr1[n];
            v2 = arr2[n];
            if(typeof v1 != typeof v2)
            {
               return false;
            }
            bb0 = ObjectMethod.samePan(v1,v2);
            if(bb0 == false)
            {
               return false;
            }
         }
         return true;
      }
      
      public static function onlyOneElementSamePan(arr1:Array, arr2:Array) : Boolean
      {
         var a0:Object = null;
         var b0:Object = null;
         if(!arr1 || !arr2)
         {
            return false;
         }
         for each(a0 in arr1)
         {
            for each(b0 in arr2)
            {
               if(a0 == b0)
               {
                  return true;
               }
            }
         }
         return false;
      }
   }
}

