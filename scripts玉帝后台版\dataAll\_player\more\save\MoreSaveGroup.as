package dataAll._player.more.save
{
   import com.sounto.utils.ClassProperty;
   import dataAll.items.save.ItemsSaveGroup;
   
   public class MoreSaveGroup extends ItemsSaveGroup
   {
      
      public static var pro_arr:Array = null;
      
      public static var mePro_arr:Array = [];
      
      public function MoreSaveGroup()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObjAndClass(obj0,MoreSave);
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
      }
      
      public function initSave() : void
      {
         unlockTo(2 - 1);
      }
      
      public function initBagSave() : void
      {
         unlockTo(10 - 1);
      }
      
      public function getSaveByName(name0:String) : MoreSave
      {
         var s0:MoreSave = null;
         for each(s0 in arr)
         {
            if(s0.name == name0)
            {
               return s0;
            }
         }
         return null;
      }
   }
}

