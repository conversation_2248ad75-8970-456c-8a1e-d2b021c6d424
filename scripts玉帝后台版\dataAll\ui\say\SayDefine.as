package dataAll.ui.say
{
   import dataAll._player.role.RoleName;
   
   public class SayDefine
   {
      
      public static const MOBILE:String = "手机";
      
      public static const NO:String = "";
      
      public var target:String = "";
      
      public var pointer:String = "";
      
      public var type:String = "";
      
      public var iconUrl:String = "";
      
      public var r:String = "";
      
      public var contentArr:Array = [];
      
      public function SayDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var t_arr0:Array = null;
         var content_xml0:XML = null;
         this.target = xml0.@target;
         this.pointer = xml0.@pointer;
         this.iconUrl = xml0.@iconUrl;
         this.r = String(xml0.@r);
         var xmlList0:XMLList = xml0.content;
         for(n in xmlList0)
         {
            content_xml0 = xmlList0[n];
            this.contentArr.push(String(content_xml0));
         }
         t_arr0 = this.target.split("/");
         if(t_arr0.length > 1)
         {
            this.target = t_arr0[0];
            this.type = t_arr0[1];
         }
      }
      
      public function isMobileB() : Boolean
      {
         return this.type == MOBILE;
      }
      
      public function getContent() : String
      {
         return this.contentArr[int(Math.random() * this.contentArr.length)];
      }
      
      public function panP1Role(role0:String) : Boolean
      {
         if(this.r == "")
         {
            return true;
         }
         if(this.r == "noStrikerWenJie")
         {
            if(role0 == RoleName.Striker || role0 == RoleName.WenJie)
            {
               return false;
            }
            return true;
         }
         if(this.r == role0)
         {
            return true;
         }
         if(this.r.indexOf(role0) >= 0)
         {
            return true;
         }
         return false;
      }
   }
}

