package dataAll.body.attack
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._base.NormalDefine;
   import dataAll.ui.GatherColor;
   
   public class ElementHurtDefine extends NormalDefine
   {
      
      public var breakShell:String = "";
      
      public var defenceShell:String = "";
      
      public var gatherColor:String = "";
      
      public function ElementHurtDefine()
      {
         super();
      }
      
      public function getGemName() : String
      {
         return name + "Gem";
      }
      
      public function getHurtCn() : String
      {
         return cnName + "伤害";
      }
      
      public function getSensitiveCn(colorB0:Boolean) : String
      {
         var s0:String = cnName + "敏感";
         if(colorB0)
         {
            s0 = ComMethod.color(s0,this.getColor());
         }
         return s0;
      }
      
      public function getColor() : String
      {
         return GatherColor.getColor(this.gatherColor);
      }
   }
}

