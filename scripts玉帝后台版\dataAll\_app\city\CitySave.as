package dataAll._app.city
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.TextMethod;
   import dataAll._app.city.dress.CityDressSaveGroup;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.things.define.ThingsDefine;
   import dataAll.things.define.ThingsSmeltType;
   import dataAll.ui.GatherColor;
   
   public class CitySave
   {
      
      public static var pro_arr:Array = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var giftB:Boolean = false;
      
      public var countObj:Object = {};
      
      public var planArr:Array = [];
      
      public var dress:CityDressSaveGroup = new CityDressSaveGroup();
      
      public function CitySave()
      {
         super();
         this.num = 0;
         this.allNum = 0;
      }
      
      public static function getPlanMax() : int
      {
         return 15;
      }
      
      public function get num() : Number
      {
         return this.CF.getAttribute("num");
      }
      
      public function set num(v0:Number) : void
      {
         this.CF.setAttribute("num",v0);
      }
      
      public function get allNum() : Number
      {
         return this.CF.getAttribute("allNum");
      }
      
      public function set allNum(v0:Number) : void
      {
         this.CF.setAttribute("allNum",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.countObj = ClassProperty.copyObj(obj0["countObj"]);
         this.planArr = ClassProperty.copyArray(obj0["planArr"]);
      }
      
      public function newDayCtrl(timeDa0:StringDate) : void
      {
         this.num = 0;
      }
      
      public function addCount(g0:GiftAddDefineGroup) : void
      {
         var d0:GiftAddDefine = null;
         var name0:String = null;
         var td0:ThingsDefine = null;
         for each(d0 in g0.arr)
         {
            name0 = d0.name;
            td0 = Gaming.defineGroup.things.getDefine(name0);
            if(td0.smeltD.grade == 2 || td0.smeltD.type == ThingsSmeltType.armsEquip)
            {
               if(!this.countObj.hasOwnProperty(name0))
               {
                  this.countObj[name0] = 0;
               }
               this.countObj[name0] += d0.num;
            }
         }
      }
      
      public function delPlan(saveStr0:String) : void
      {
         ArrayMethod.remove(this.planArr,saveStr0);
      }
      
      public function savePlan(title0:String, plan0:String) : String
      {
         var s0:String = null;
         var f0:int = 0;
         if(this.planArr.length >= getPlanMax())
         {
            return "方案最多只能保存" + getPlanMax() + "个。";
         }
         s0 = title0 + "：" + plan0;
         f0 = int(this.planArr.indexOf(s0));
         if(f0 == -1)
         {
            this.planArr.push(s0);
            return "";
         }
         return "已存在相同方案：\n" + this.planArr[f0];
      }
      
      public function getPlanTxt(editB0:Boolean) : String
      {
         var ps0:String = null;
         var parr0:Array = null;
         var title0:String = null;
         var plan0:String = null;
         var s0:String = null;
         var allS0:String = "";
         for each(ps0 in this.planArr)
         {
            parr0 = ps0.split("：");
            title0 = parr0[0];
            plan0 = parr0[1];
            s0 = "";
            if(editB0)
            {
               s0 = TextMethod.link(title0,ps0);
               s0 += TextMethod.color("X",GatherColor.redColor) + "  ";
            }
            else
            {
               s0 = TextMethod.link(title0,plan0);
               s0 += "   ";
            }
            allS0 += s0;
         }
         return allS0;
      }
   }
}

