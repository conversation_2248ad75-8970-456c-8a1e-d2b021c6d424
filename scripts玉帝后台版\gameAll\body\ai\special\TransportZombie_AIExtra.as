package gameAll.body.ai.special
{
   public class TransportZombie_AIExtra extends AIExtra
   {
      
      public function TransportZombie_AIExtra()
      {
         super();
      }
      
      private function shakeAttack() : void
      {
         var c_f0:int = _img.nowMc.currentFrame;
         if(c_f0 == 11)
         {
            BB.getSkillCtrl().teleportToAttackBody(60,0,true);
         }
         _dat.stateD.noUnderHurtB = true;
         _dat.stateD.spellImmunityB = true;
      }
      
      override public function FTimer() : void
      {
         super.FTimer();
         if(_img.nowLabel == "shakeAttack")
         {
            this.shakeAttack();
         }
      }
   }
}

