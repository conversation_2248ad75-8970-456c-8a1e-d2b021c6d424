package gameAll.body.ai.special
{
   public class WatchEagleAir_AIExtra extends Po_AIExtra
   {
      
      public function WatchEagleAir_AIExtra()
      {
         var dat0:PoAIExtraData = null;
         super();
         dat0 = new PoAIExtraData();
         dat0.LABEL = "sprintAttack";
         dat0.FIRST_F = 3;
         dat0.END_F = 23;
         dat0.V = 30;
         dat0.min_ra = -Math.PI / 6;
         dat0.max_ra = Math.PI / 6;
         dat0.canFlipB = false;
         dat0.endNoteB = false;
         dataObj[dat0.LABEL] = dat0;
      }
   }
}

