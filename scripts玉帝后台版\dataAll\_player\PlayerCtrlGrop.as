package dataAll._player
{
   import UI.test.SaveTestBox;
   import dataAll._app.login.SaveData4399;
   import dataAll._app.top.TopBarData;
   import dataAll._player.define.MainPlayerType;
   
   public class PlayerCtrlGrop
   {
      
      private var obj:Object = {};
      
      public function PlayerCtrlGrop()
      {
         super();
      }
      
      public function add(save0:SaveData4399, topDa0:TopBarData) : PlayerCtrl
      {
         var id0:String = topDa0.getPCGId();
         var pc0:PlayerCtrl = this.getPC(id0);
         if(!(pc0 is PlayerCtrl))
         {
            this.clearAllBy10();
            pc0 = new PlayerCtrl();
            pc0.readSave(save0.data,MainPlayerType.OTHER);
            pc0.da.player.inData(save0.base,topDa0);
            SaveTestBox.addText("PlayerCtrlGrop.add:" + id0);
            SaveTestBox.addText("pc0.da.player.id:" + pc0.da.player.id);
            this.obj[id0] = pc0;
         }
         return pc0;
      }
      
      public function getPC(id0:String) : PlayerCtrl
      {
         return this.obj[id0];
      }
      
      private function clearAllBy10() : void
      {
         var n:* = undefined;
         var num0:int = 0;
         for(n in this.obj)
         {
            num0++;
         }
         if(num0 > 10)
         {
            this.clearAll();
         }
      }
      
      public function clearAll() : void
      {
         this.obj = {};
      }
      
      public function outLoginEvent() : void
      {
         this.clearAll();
      }
      
      public function getIdArr() : Array
      {
         var n:* = undefined;
         var arr0:Array = [];
         for(n in this.obj)
         {
            arr0.push(n);
         }
         return arr0;
      }
      
      public function getNowGunImageNameArr() : Array
      {
         var n:* = undefined;
         var pc0:PlayerCtrl = null;
         var arr0:Array = [];
         for(n in this.obj)
         {
            pc0 = this.obj[n];
            arr0 = arr0.concat(pc0.da.getNowGunImageNameArr());
         }
         return arr0;
      }
      
      public function startLevel(lg0:IO_PlayerLevelGetter) : void
      {
         var n:* = undefined;
         var pc0:PlayerCtrl = null;
         for(n in this.obj)
         {
            pc0 = this.obj[n];
            pc0.startLevel(lg0);
         }
      }
      
      public function overGamingClear(lg0:IO_PlayerLevelGetter) : void
      {
         var n:* = undefined;
         var pc0:PlayerCtrl = null;
         for(n in this.obj)
         {
            pc0 = this.obj[n];
            pc0.overGamingClear(lg0);
         }
      }
   }
}

