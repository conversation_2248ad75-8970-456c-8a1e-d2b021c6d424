package dataAll.level.define.event
{
   public class LevelEventDefineGroup
   {
      
      public var arr:Array = [];
      
      public var obj:Object = {};
      
      public function LevelEventDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : LevelEventOrderDefine
      {
         var i:* = undefined;
         var group_xml2:XML = null;
         var arr2:Array = null;
         var xmllist2:XMLList = null;
         var n:* = undefined;
         var xml2:XML = null;
         var d0:LevelEventDefine = null;
         var last0:LevelEventOrderDefine = null;
         if(!xml0)
         {
            return null;
         }
         var lastEnemyD0:LevelEventDefine = null;
         var lastEnemyOrder0:LevelEventOrderDefine = null;
         var group_xmllist2:XMLList = xml0.group;
         for(i in group_xmllist2)
         {
            group_xml2 = group_xmllist2[i];
            arr2 = [];
            xmllist2 = group_xml2.event;
            for(n in xmllist2)
            {
               xml2 = xmllist2[n];
               d0 = new LevelEventDefine();
               d0.inData_byXML(xml2);
               arr2.push(d0);
               this.obj[d0.id] = d0;
               last0 = d0.findCreateEnemyOrder();
               if(Boolean(last0))
               {
                  lastEnemyOrder0 = last0;
                  lastEnemyD0 = d0;
               }
            }
            this.arr[i] = arr2;
         }
         if(Boolean(lastEnemyD0))
         {
            lastEnemyD0.isLastEnemyB = true;
         }
         return lastEnemyOrder0;
      }
      
      public function getDefine(id0:String) : LevelEventDefine
      {
         return this.obj[id0];
      }
      
      public function haveStringInOrder(str0:String) : Boolean
      {
         var n:* = undefined;
         var i:* = undefined;
         var d0:LevelEventDefine = null;
         for(n in this.arr)
         {
            for(i in this.arr[n])
            {
               d0 = this.arr[n][i];
               if(d0.haveStringInOrder(str0))
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function fixedBy(eg0:LevelEventDefineGroup, type0:String) : void
      {
         if(type0 == "all")
         {
            this.arr = eg0.arr;
         }
         else if(type0 == "before")
         {
            this.arr = eg0.arr.concat(this.arr);
         }
         else if(type0 == "affter")
         {
            this.arr = this.arr.concat(eg0.arr);
         }
         this.fleshObj();
      }
      
      private function fleshObj() : void
      {
         var n:* = undefined;
         var i:* = undefined;
         var d0:LevelEventDefine = null;
         this.obj = {};
         for(n in this.arr)
         {
            for(i in this.arr[n])
            {
               d0 = this.arr[n][i];
               this.obj[d0.id] = d0;
            }
         }
      }
   }
}

