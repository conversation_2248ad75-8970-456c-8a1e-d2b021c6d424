package dataAll._app.ask
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   
   public class AskSave
   {
      
      public static var pro_arr:Array = [];
      
      public static var Version:String = "8.1";
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var nowSaveVersion:String = "1.0";
      
      public var todayTime:Number = 0;
      
      public var nowSeason:String = "";
      
      public var overB:Boolean = false;
      
      public var giftB:Boolean = false;
      
      public function AskSave()
      {
         super();
         this.score = 0;
         this.todayScore = 0;
         this.correctNum = 0;
         this.errorNum = 0;
         this.todayTime = 0;
         this.overB = false;
         this.giftB = false;
      }
      
      public function get score() : Number
      {
         return this.CF.getAttribute("score");
      }
      
      public function set score(v0:Number) : void
      {
         this.CF.setAttribute("score",v0);
      }
      
      public function get todayScore() : Number
      {
         return this.CF.getAttribute("todayScore");
      }
      
      public function set todayScore(v0:Number) : void
      {
         this.CF.setAttribute("todayScore",v0);
      }
      
      public function get correctNum() : Number
      {
         return this.CF.getAttribute("correctNum");
      }
      
      public function set correctNum(v0:Number) : void
      {
         this.CF.setAttribute("correctNum",v0);
      }
      
      public function get errorNum() : Number
      {
         return this.CF.getAttribute("errorNum");
      }
      
      public function set errorNum(v0:Number) : void
      {
         this.CF.setAttribute("errorNum",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function inData_byTempObj(obj0:Object) : void
      {
         var proArr0:Array = ["score","todayScore","correctNum","errorNum"];
         ClassProperty.inData(this,obj0,proArr0);
      }
      
      public function clone() : AskSave
      {
         var s0:AskSave = new AskSave();
         s0.inData_byObj(this);
         return s0;
      }
      
      public function getDayNowNum() : int
      {
         return this.correctNum + this.errorNum;
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.todayScore = 0;
         this.correctNum = 0;
         this.errorNum = 0;
         this.todayTime = 0;
         this.overB = false;
         this.giftB = false;
         var da0:StringDate = new StringDate(timeStr0);
         var sea0:String = String(da0.fullYear) + da0.month;
         if(this.nowSeason != sea0)
         {
            this.nowSeason = sea0;
            this.score = 0;
         }
      }
      
      public function addScore(v0:int) : void
      {
         this.score += v0;
         this.todayScore += v0;
      }
   }
}

