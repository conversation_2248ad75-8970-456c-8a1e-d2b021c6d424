package dataAll._app.blackMarket.define
{
   import dataAll.arms.define.ArmsChargerDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.equip.define.EquipType;
   import dataAll.ui.label.LabelAddData;
   
   public class BlackMarketLeftLabelDefine
   {
      
      public function BlackMarketLeftLabelDefine()
      {
         super();
      }
      
      public function getLabelAddData() : LabelAddData
      {
         var da0:LabelAddData = new LabelAddData();
         da0.inDataOne("blackMarket","神秘","BlackMarketUI/bigLabel",0);
         da0.addChildData(this.getArmsLabelAddData(0));
         da0.addChildData(this.getEquipLabelAddData(0));
         return da0;
      }
      
      private function getArmsLabelAddData(index0:int) : LabelAddData
      {
         var n:* = undefined;
         var da2:LabelAddData = null;
         var type0:String = null;
         var d0:ArmsChargerDefine = null;
         var da0:LabelAddData = new LabelAddData();
         da0.inDataOne("arms","武器","BlackMarketUI/midLabel",index0);
         var arr0:Array = ArmsType.NORMAL_TYPE_ARR;
         for(n in arr0)
         {
            da2 = new LabelAddData();
            type0 = arr0[n];
            d0 = Gaming.defineGroup.armsCharger.getDefine(type0);
            da2.inDataOne(type0,d0.cnName,"BlackMarketUI/midLabel",n);
            da0.addChildData(da2);
         }
         return da0;
      }
      
      private function getEquipLabelAddData(index0:int) : LabelAddData
      {
         var n:* = undefined;
         var da2:LabelAddData = null;
         var type0:String = null;
         var cnName0:String = null;
         var da0:LabelAddData = new LabelAddData();
         da0.inDataOne("equip","装备","BlackMarketUI/midLabel",index0);
         var arr0:Array = EquipType.NORMAL_ARR;
         for(n in arr0)
         {
            da2 = new LabelAddData();
            type0 = arr0[n];
            cnName0 = EquipType.TYPE_CN_ARR[n];
            da2.inDataOne(type0,cnName0,"BlackMarketUI/midLabel",n);
            da0.addChildData(da2);
         }
         return da0;
      }
      
      private function getPartsLabelAddData(index0:int) : LabelAddData
      {
         return null;
      }
   }
}

