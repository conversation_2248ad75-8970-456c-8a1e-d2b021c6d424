package dataAll._player
{
   import dataAll._app.login.SaveBaseData4399;
   import dataAll._app.top.TopBarData;
   
   public class PlayerUserData
   {
      
      public var id:String = "";
      
      public var uid:String = "";
      
      public var base:SaveBaseData4399 = new SaveBaseData4399();
      
      private var top:TopBarData = new TopBarData();
      
      public function PlayerUserData()
      {
         super();
      }
      
      public function inData(base0:SaveBaseData4399, da0:TopBarData) : void
      {
         this.uid = da0.uid;
         this.top = da0;
         this.base = base0;
         this.id = da0.getPCGId();
      }
   }
}

