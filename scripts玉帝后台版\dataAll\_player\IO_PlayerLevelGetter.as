package dataAll._player
{
   import dataAll._app.task.TaskData;
   import dataAll.level.define.LevelDefine;
   import gameAll.level.data.LevelData;
   
   public interface IO_PlayerLevelGetter
   {
      
      function isNormalLevelB() : Boolean;
      
      function canFoodB() : Boolean;
      
      function isMemoryTaskB() : Boolean;
      
      function isTowerLevelB() : Boolean;
      
      function isUnendLevelB() : Boolean;
      
      function isTowerOrUnlendB() : Boolean;
      
      function isCardPKB() : Boolean;
      
      function isOnlyIng() : Boolean;
      
      function isDemOrHardB() : Boolean;
      
      function isDemonB() : Boolean;
      
      function isDemonOnlyThingsB() : Boolean;
      
      function isDemonOrXingGu() : Boolean;
      
      function isXingGu() : Boolean;
      
      function isUnionBattleB() : Boolean;
      
      function getMapMode() : String;
      
      function getNowWorldMapName() : String;
      
      function getLevelName() : String;
      
      function getLevelDefineNull() : LevelDefine;
      
      function getLevelDataNull() : LevelData;
      
      function getDiff() : int;
      
      function getTaskData() : TaskData;
      
      function isActiveTaskB() : Boolean;
      
      function noAnyParnterB() : Boolean;
      
      function canPetB() : Boolean;
      
      function haveParnterB() : Boolean;
      
      function getLimitParnterNameArr() : Array;
      
      function getMemoryLvLimit() : int;
   }
}

