package gameAll.body.action
{
   import gameAll.body.IO_NormalBody;
   
   public interface IO_NormalBodyAction
   {
      
      function moveToLeft() : void;
      
      function moveToRight() : void;
      
      function toStop() : void;
      
      function rebirth() : void;
      
      function toJump(param1:Number = -100000, param2:Number = -100000, param3:Number = -1) : Boolean;
      
      function toHurt(param1:int = 1) : void;
      
      function toDie(param1:int = 1, param2:Number = 0, param3:Number = 0) : void;
      
      function toStru() : void;
      
      function getStandLabel() : String;
      
      function toStand() : void;
      
      function setStandLabel(param1:String) : void;
      
      function allowAll() : Boolean;
      
      function shootAllowAll() : Boolean;
      
      function toRide(param1:String) : void;
      
      function flipToBody(param1:IO_NormalBody) : void;
      
      function setOnlyLabel(param1:String) : void;
      
      function toJumpAction() : void;
   }
}

