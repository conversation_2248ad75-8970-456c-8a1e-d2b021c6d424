package dataAll._base
{
   public class BaseDefineGroup
   {
      
      public var fatherArrObj:Object = {};
      
      public var fatherNameArr:Array = [];
      
      public var obj:Object = {};
      
      public var arr:Array = [];
      
      protected var defineClass:Class;
      
      public function BaseDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var thingsXML0:XMLList = null;
         var fatherName0:String = null;
         var index0:int = 0;
         var n:* = undefined;
         var fatherXML0:XMLList = xml0.father;
         for(i in fatherXML0)
         {
            this.addFatherByXml(fatherXML0[i]);
            thingsXML0 = fatherXML0[i].body;
            fatherName0 = fatherXML0[i].@name;
            index0 = 0;
            for(n in thingsXML0)
            {
               this.addDefineByXml(n,thingsXML0[n],fatherName0);
            }
         }
      }
      
      protected function addFatherByXml(xml0:XML) : void
      {
      }
      
      protected function addDefineByXml(n0:int, xml0:XML, father0:String) : void
      {
         var d0:Object = new this.defineClass();
         d0["inData_byXML"](xml0,father0);
         this.addDefine(d0,father0);
      }
      
      protected function addDefine(d0:Object, father0:String) : void
      {
         if(!this.fatherArrObj.hasOwnProperty(father0))
         {
            this.fatherArrObj[father0] = [];
            this.fatherNameArr.push(father0);
         }
         this.fatherArrObj[father0].push(d0);
         this.obj[d0["name"]] = d0;
         this.arr.push(d0);
      }
      
      public function getNormalDefine(name0:String) : IO_NormalDefine
      {
         return this.obj[name0];
      }
      
      public function getArrByFather(f0:String) : Array
      {
         return this.fatherArrObj[f0];
      }
      
      public function getArrByFatherArr(fatherArr0:Array) : Array
      {
         var father0:String = null;
         var a0:Array = null;
         var arr0:Array = [];
         for each(father0 in fatherArr0)
         {
            a0 = this.getArrByFather(father0);
            if(Boolean(a0))
            {
               arr0 = arr0.concat(a0);
            }
         }
         return arr0;
      }
   }
}

