package dataAll._player
{
   import UI.api.shop.ShopBuyObject;
   import UI.test.SaveTestBox;
   import com.adobe.crypto.MD5;
   import com.sounto.cf.CodeCF;
   import com.sounto.cf.StringCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.achieve.AchieveSave;
   import dataAll._app.achieve.AchieveSaveGroup;
   import dataAll._app.active.ActiveSave;
   import dataAll._app.arena.ArenaSave;
   import dataAll._app.arena.record.ArenaRecordSave;
   import dataAll._app.arena.record.OneArenaRecordSave;
   import dataAll._app.ask.AskSave;
   import dataAll._app.blackMarket.BlackMarketCtrl;
   import dataAll._app.blackMarket.BlackMarketSave;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.GoodsSaveGroup;
   import dataAll._app.head.HeadOneSave;
   import dataAll._app.head.HeadSave;
   import dataAll._app.login.LoginData4399;
   import dataAll._app.login.SaveBaseData4399;
   import dataAll._app.love.LoveLikeSave;
   import dataAll._app.love.LoveSave;
   import dataAll._app.parts.PartsAddData;
   import dataAll._app.post.PostSave;
   import dataAll._app.setting.SettingSave;
   import dataAll._app.setting.key.SettingKeySave;
   import dataAll._app.task.save.TaskSave;
   import dataAll._app.task.save.TaskSaveGroup;
   import dataAll._app.top.TopBarData;
   import dataAll._app.top.TopBarSave;
   import dataAll._app.top.player.PlayerTopReturnData;
   import dataAll._app.top.player.PlayerTopUploadData;
   import dataAll._app.union.UnionSave;
   import dataAll._app.union.building.UnionBuildingSave;
   import dataAll._app.union.building.UnionBuildingSaveGroup;
   import dataAll._app.union.building.cooking.UnionCookingSave;
   import dataAll._app.union.building.federal.UnionSendTaskSave;
   import dataAll._app.union.building.geology.UnionGeologySave;
   import dataAll._app.union.building.geology.UnionGeologyThingsSave;
   import dataAll._app.union.extra.MemberExtra;
   import dataAll._app.union.extra.UnionExtra;
   import dataAll._app.union.info.MemberCheckListInfo;
   import dataAll._app.union.info.MemberInfo;
   import dataAll._app.union.info.UnionInfo;
   import dataAll._app.union.info.UnionListInfo;
   import dataAll._app.union.task.UnionTaskSave;
   import dataAll._app.vip.VipSave;
   import dataAll._app.wilder.WilderSave;
   import dataAll._app.wilder.WilderSaveGroup;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll._app.worldMap.save.WorldMapSaveGroup;
   import dataAll._player.base.PlayerBaseSave;
   import dataAll._player.base.PlayerMainSave;
   import dataAll._player.count.HeadCountProSave;
   import dataAll._player.count.HeadCountSave;
   import dataAll._player.count.NormalPlayerCountSave;
   import dataAll._player.count.PlayerCountSave;
   import dataAll._player.define.MainPlayerType;
   import dataAll._player.more.MoreData;
   import dataAll._player.more.MorePlayerSave;
   import dataAll._player.more.NormalPlayerData;
   import dataAll._player.more.NormalPlayerSave;
   import dataAll._player.more.SimulateMoreData;
   import dataAll._player.more.save.MoreSave;
   import dataAll._player.more.save.MoreSaveGroup;
   import dataAll._player.state.PlayerOneStateSave;
   import dataAll._player.state.PlayerStateSave;
   import dataAll._player.time.TimeSave;
   import dataAll.arms.ArmsData;
   import dataAll.arms.save.ArmsSaveGroup;
   import dataAll.drop.BodyDropData;
   import dataAll.drop.DropSave;
   import dataAll.equip.EquipData;
   import dataAll.equip.save.EquipSave;
   import dataAll.equip.save.EquipSaveGroup;
   import dataAll.equip.save.FashionSave;
   import dataAll.equip.vehicle.VehicleSave;
   import dataAll.equip.weapon.WeaponSave;
   import dataAll.gift.anniver.AnniverGmSave;
   import dataAll.gift.anniver.AnniverOnlineSave;
   import dataAll.gift.anniver.HolidaySignGiftSave;
   import dataAll.gift.dailySign.DailySignSave;
   import dataAll.gift.giftHome.GiftHomeLevelSave;
   import dataAll.gift.giftHome.GiftHomeSave;
   import dataAll.gift.guoQing.GuoQingSave;
   import dataAll.gift.save.DropEnsureSave;
   import dataAll.gift.save.GiftSave;
   import dataAll.gift.save.SummerGiftSave;
   import dataAll.gift.school.GiftChipBattleSave;
   import dataAll.gift.zhongQiu.ZhongQiuSave;
   import dataAll.items.ItemsCompareData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.save.ItemsSave;
   import dataAll.items.save.ItemsSaveGroup;
   import dataAll.level.LevelCountSave;
   import dataAll.level.TempLevel;
   import dataAll.level.define.LevelDefine;
   import dataAll.pay.OneDayPaySave;
   import dataAll.pay.PaySave;
   import dataAll.pet.*;
   import dataAll.pet.base.PetBaseSave;
   import dataAll.pet.gene.save.GeneSave;
   import dataAll.pet.gene.save.GeneSaveGroup;
   import dataAll.pet.map.PetMapSave;
   import dataAll.pet.map.PetMapSaveGroup;
   import dataAll.pet.skill.*;
   import dataAll.pet.upgrade.*;
   import dataAll.skill.SkillAddData;
   import dataAll.skill.save.HeroSkillSave;
   import dataAll.skill.save.HeroSkillSaveGroup;
   import dataAll.things.ThingsProps;
   import dataAll.things.save.ThingsSave;
   import dataAll.things.save.ThingsSaveGroup;
   import dataAll.ui.guide.GuideSave;
   import flash.utils.getTimer;
   import gameAll.arms.GameArmsCtrl;
   import gameAll.hero.HeroBody;
   import gameAll.level.PlayMode;
   import gameAll.level.arena.ArenaCtrl;
   import gameAll.level.arena.ArenaLevelData;
   import gameAll.level.data.LevelData;
   import gameAll.level.unit.WeCreatorAddData;
   import gameAll.more.DoubleCtrl;
   
   public class PlayerCtrl implements IO_UserGetter
   {
      
      private var stringCF:StringCF = new StringCF();
      
      public var da:PlayerData;
      
      public var save:PlayerSave;
      
      public var DATA:NormalPlayerData;
      
      public var SAVE:NormalPlayerSave;
      
      public var loginData:LoginData4399 = new LoginData4399();
      
      private var time_t:int = 0;
      
      public function PlayerCtrl()
      {
         super();
         this.u = "";
         this.u2 = "";
      }
      
      public function get u() : String
      {
         return this.stringCF.getAttribute("u") as String;
      }
      
      public function set u(str0:String) : void
      {
         this.stringCF.setAttribute("u",str0);
      }
      
      public function get u2() : String
      {
         return this.stringCF.getAttribute("u2") as String;
      }
      
      public function set u2(str0:String) : void
      {
         this.stringCF.setAttribute("u2",str0);
      }
      
      public function propertyAdd() : void
      {
         var class0:Class = null;
         PlayerBaseSave.pro_arr = ClassProperty.getProArr(new PlayerBaseSave());
         PlayerMainSave.pro_arr = ClassProperty.getProArr(new PlayerMainSave());
         if(ArrayMethod.onlyOneElementSamePan(PlayerBaseSave.pro_arr,PlayerMainSave.pro_arr))
         {
            INIT.showErrorMust("PlayerBaseSave和PlayerMainSave不能有同名元素");
         }
         ItemsSave.pro_arr = ClassProperty.getProArr(new ItemsSave());
         ItemsSaveGroup.pro_arr = ClassProperty.getProArr(new ItemsSaveGroup());
         ArmsSaveGroup.pro_arr = ClassProperty.getProArr(new ArmsSaveGroup());
         ArmsSaveGroup.mePro_arr = ComMethod.deductArr(ArmsSaveGroup.pro_arr,ItemsSaveGroup.pro_arr);
         ThingsSaveGroup.pro_arr = ClassProperty.getProArr(new ThingsSaveGroup());
         ThingsSaveGroup.mePro_arr = ComMethod.deductArr(ThingsSaveGroup.pro_arr,ItemsSaveGroup.pro_arr);
         ThingsSave.pro_arr = ClassProperty.getProArr(new ThingsSave());
         ThingsSave.mePro_arr = ComMethod.deductArr(ThingsSave.pro_arr,ItemsSave.pro_arr);
         EquipSaveGroup.pro_arr = ClassProperty.getProArr(new EquipSaveGroup());
         EquipSaveGroup.mePro_arr = ComMethod.deductArr(EquipSaveGroup.pro_arr,ItemsSaveGroup.pro_arr);
         EquipSave.pro_arr = ClassProperty.getProArr(new EquipSave());
         EquipSave.mePro_arr = ComMethod.deductArr(EquipSave.pro_arr,ItemsSave.pro_arr);
         VehicleSave.pro_arr = ClassProperty.getProArr(new VehicleSave());
         VehicleSave.mePro_arr = ComMethod.deductArr(VehicleSave.pro_arr,EquipSave.pro_arr);
         WeaponSave.pro_arr = ClassProperty.getProArr(new WeaponSave());
         WeaponSave.mePro_arr = ComMethod.deductArr(WeaponSave.pro_arr,EquipSave.pro_arr);
         FashionSave.pro_arr = ClassProperty.getProArr(new FashionSave());
         FashionSave.mePro_arr = ComMethod.deductArr(FashionSave.pro_arr,EquipSave.pro_arr);
         HeroSkillSave.pro_arr = ClassProperty.getProArr(new HeroSkillSave());
         HeroSkillSave.mePro_arr = ComMethod.deductArr(HeroSkillSave.pro_arr,ItemsSave.pro_arr);
         HeroSkillSaveGroup.pro_arr = ClassProperty.getProArr(new HeroSkillSaveGroup());
         HeroSkillSaveGroup.mePro_arr = ComMethod.deductArr(HeroSkillSaveGroup.pro_arr,ItemsSaveGroup.pro_arr);
         TaskSaveGroup.pro_arr = ClassProperty.getProArr(new TaskSaveGroup());
         WorldMapSave.pro_arr = ClassProperty.getProArr(new WorldMapSave());
         WorldMapSaveGroup.pro_arr = ClassProperty.getProArr(new WorldMapSaveGroup());
         MoreSave.pro_arr = ClassProperty.getProArr(new MoreSave());
         MoreSave.mePro_arr = ComMethod.deductArr(MoreSave.pro_arr,ItemsSave.pro_arr);
         MoreSaveGroup.pro_arr = ClassProperty.getProArr(new MoreSaveGroup());
         MoreSaveGroup.mePro_arr = ComMethod.deductArr(MoreSaveGroup.pro_arr,ItemsSaveGroup.pro_arr);
         PlayerSave.pro_arr = ClassProperty.getProArr(new PlayerSave());
         NormalPlayerSave.pro_arr = ClassProperty.getProArr(new NormalPlayerSave());
         MorePlayerSave.pro_arr = ClassProperty.getProArr(new MorePlayerSave());
         TaskSave.pro_arr = ClassProperty.getProArr(new TaskSave());
         TaskSaveGroup.pro_arr = ClassProperty.getProArr(new TaskSaveGroup());
         LevelCountSave.pro_arr = ClassProperty.getProArr(new LevelCountSave());
         GuideSave.pro_arr = ClassProperty.getProArr(new GuideSave());
         SettingSave.pro_arr = ClassProperty.getProArr(new SettingSave());
         TimeSave.pro_arr = ClassProperty.getProArr(new TimeSave());
         GeneSave.pro_arr = ClassProperty.getProArr(new GeneSave());
         GeneSave.mePro_arr = ComMethod.deductArr(GeneSave.pro_arr,ItemsSave.pro_arr);
         GeneSaveGroup.pro_arr = ClassProperty.getProArr(new GeneSaveGroup());
         GeneSaveGroup.mePro_arr = ComMethod.deductArr(GeneSaveGroup.pro_arr,ItemsSaveGroup.pro_arr);
         GiftSave.pro_arr = ClassProperty.getProArr(new GiftSave());
         DailySignSave.pro_arr = ClassProperty.getProArr(new DailySignSave());
         SaveBaseData4399.pro_arr = ClassProperty.getProArr(new SaveBaseData4399());
         PlayerTopReturnData.pro_arr = ClassProperty.getProArr(new PlayerTopReturnData());
         PlayerTopUploadData.pro_arr = ClassProperty.getProArr(new PlayerTopUploadData());
         TopBarData.pro_arr = ClassProperty.getProArr(new TopBarData());
         TopBarSave.pro_arr = ClassProperty.getProArr(new TopBarSave());
         PartsAddData.pro_arr = ClassProperty.getProArr(new PartsAddData());
         VipSave.pro_arr = ClassProperty.getProArr(new VipSave());
         ArenaSave.pro_arr = ClassProperty.getProArr(new ArenaSave());
         ArenaRecordSave.pro_arr = ClassProperty.getProArr(new ArenaRecordSave());
         OneArenaRecordSave.pro_arr = ClassProperty.getProArr(new OneArenaRecordSave());
         PaySave.pro_arr = ClassProperty.getProArr(new PaySave());
         OneDayPaySave.pro_arr = ClassProperty.getProArr(new OneDayPaySave());
         BlackMarketSave.pro_arr = ClassProperty.getProArr(new BlackMarketSave());
         BlackMarketCtrl.DG = Gaming.defineGroup.blackMarket;
         AchieveSave.pro_arr = ClassProperty.getProArr(new AchieveSave());
         AchieveSaveGroup.pro_arr = ClassProperty.getProArr(new AchieveSaveGroup());
         NormalPlayerCountSave.pro_arr = ClassProperty.getProArr(new NormalPlayerCountSave());
         PlayerCountSave.pro_arr = ClassProperty.getProArr(new PlayerCountSave());
         PlayerCountSave.pro_arr = ComMethod.deductArr(PlayerCountSave.pro_arr,NormalPlayerCountSave.pro_arr);
         PetSave.pro_arr = ClassProperty.getProArr(new PetSave());
         PetSaveGroup.pro_arr = ClassProperty.getProArr(new PetSaveGroup());
         PetBaseSave.pro_arr = ClassProperty.getProArr(new PetBaseSave());
         PetUpgradeSave.pro_arr = ClassProperty.getProArr(new PetUpgradeSave());
         PetUpgradeSaveGroup.pro_arr = ClassProperty.getProArr(new PetUpgradeSaveGroup());
         PetMapSave.pro_arr = ClassProperty.getProArr(new PetMapSave());
         PetMapSaveGroup.pro_arr = ClassProperty.getProArr(new PetMapSaveGroup());
         AskSave.pro_arr = ClassProperty.getProArr(new AskSave());
         SettingKeySave.pro_arr = ClassProperty.getProArr(new SettingKeySave());
         MemberInfo.pro_arr = ClassProperty.getProArr(new MemberInfo());
         UnionInfo.pro_arr = ClassProperty.getProArr(new UnionInfo());
         UnionListInfo.pro_arr = ClassProperty.getProArr(new UnionListInfo());
         MemberExtra.pro_arr = ClassProperty.getProArr(new MemberExtra());
         UnionExtra.pro_arr = ClassProperty.getProArr(new UnionExtra());
         MemberCheckListInfo.pro_arr = ClassProperty.getProArr(new MemberCheckListInfo());
         UnionSave.pro_arr = ClassProperty.getProArr(new UnionSave());
         UnionSave.init();
         UnionTaskSave.pro_arr = ClassProperty.getProArr(new UnionTaskSave());
         DropSave.pro_arr = ClassProperty.getProArr(new DropSave());
         ActiveSave.pro_arr = ClassProperty.getProArr(new ActiveSave());
         HeadSave.pro_arr = ClassProperty.getProArr(new HeadSave());
         HeadOneSave.pro_arr = ClassProperty.getProArr(new HeadOneSave());
         HeadCountSave.pro_arr = ClassProperty.getProArr(new HeadCountSave());
         HeadCountProSave.pro_arr = ClassProperty.getProArr(new HeadCountProSave());
         SkillAddData.pro_arr = ClassProperty.getProArr(new SkillAddData());
         GoodsSaveGroup.pro_arr = ClassProperty.getProArr(new GoodsSaveGroup());
         LoveSave.pro_arr = ClassProperty.getProArr(new LoveSave());
         LoveLikeSave.pro_arr = ClassProperty.getProArr(new LoveLikeSave());
         GiftHomeSave.pro_arr = ClassProperty.getProArr(new GiftHomeSave());
         GiftHomeLevelSave.pro_arr = ClassProperty.getProArr(new GiftHomeLevelSave());
         UnionBuildingSave.pro_arr = ClassProperty.getProArr(new UnionBuildingSave());
         UnionBuildingSaveGroup.pro_arr = ClassProperty.getProArr(new UnionBuildingSaveGroup());
         UnionBuildingSaveGroup.pro_arr = ClassProperty.getProArr(new UnionBuildingSaveGroup());
         var classArr0:Array = [GuoQingSave,ZhongQiuSave,SummerGiftSave,GiftChipBattleSave,AnniverGmSave,PlayerStateSave,PlayerOneStateSave,DropEnsureSave,AnniverOnlineSave,HolidaySignGiftSave,UnionGeologyThingsSave,UnionGeologySave,WilderSaveGroup,WilderSave,BodyDropData,UnionCookingSave,UnionSendTaskSave,PostSave];
         for each(class0 in classArr0)
         {
            class0["pro_arr"] = ClassProperty.getProArr(new class0());
         }
         this.testMeProArr(EquipSave.pro_arr,EquipSave.mePro_arr,ItemsSave.pro_arr);
         this.testMeProArr(ThingsSave.pro_arr,ThingsSave.mePro_arr,ItemsSave.pro_arr);
         this.testMeProArr(HeroSkillSave.pro_arr,HeroSkillSave.mePro_arr,ItemsSave.pro_arr);
         this.testMeProArr(MoreSave.pro_arr,MoreSave.mePro_arr,ItemsSave.pro_arr);
         this.testMeProArr(GeneSave.pro_arr,GeneSave.mePro_arr,ItemsSave.pro_arr);
         this.testMeProArr(VehicleSave.pro_arr,VehicleSave.mePro_arr,EquipSave.pro_arr);
         this.testMeProArr(WeaponSave.pro_arr,WeaponSave.mePro_arr,EquipSave.pro_arr);
         this.testMeProArr(FashionSave.pro_arr,FashionSave.mePro_arr,EquipSave.pro_arr);
         this.testMeProArr(ArmsSaveGroup.pro_arr,ArmsSaveGroup.mePro_arr,ItemsSaveGroup.pro_arr);
         this.testMeProArr(ThingsSaveGroup.pro_arr,ThingsSaveGroup.mePro_arr,ItemsSaveGroup.pro_arr);
         this.testMeProArr(EquipSaveGroup.pro_arr,EquipSaveGroup.mePro_arr,ItemsSaveGroup.pro_arr);
         this.testMeProArr(HeroSkillSaveGroup.pro_arr,HeroSkillSaveGroup.mePro_arr,ItemsSaveGroup.pro_arr);
         this.testMeProArr(MoreSaveGroup.pro_arr,MoreSaveGroup.mePro_arr,ItemsSaveGroup.pro_arr);
         this.testMeProArr(GeneSaveGroup.pro_arr,GeneSaveGroup.mePro_arr,ItemsSaveGroup.pro_arr);
      }
      
      private function testMeProArr(all0:Array, me0:Array, base0:Array) : Boolean
      {
         var pro0:String = null;
         if(all0.length != me0.length + base0.length)
         {
            return false;
         }
         for each(pro0 in all0)
         {
            if(me0.indexOf(pro0) == -1 && base0.indexOf(pro0) == -1)
            {
               return false;
            }
         }
         return true;
      }
      
      public function init() : void
      {
         SimulateMoreData.init();
         ThingsProps.init();
      }
      
      public function inLoginObj(obj0:Object) : void
      {
         this.loginData.inData_byObj(obj0);
         this.u2 = this.loginData.uid;
         this.u = MD5.hash(CodeCF.toCode32(this.u2));
         this.loginChangeEvent();
      }
      
      public function inLoginList(arr0:Array) : void
      {
         this.loginData.inSaveArr(arr0);
      }
      
      public function initSave(apiLogObj0:LoginData4399 = null) : void
      {
         this.clearSave();
         this.save = new PlayerSave();
         this.save.initSave();
         this.save.guide.dealObj2(this.loginData.getNew());
         this.save.guide.uu = this.u;
         this.save.guide.uu2 = this.u2;
         this.readPlayerSave(this.save,true,"me",apiLogObj0);
      }
      
      public function clearSave(clearTempB0:Boolean = true) : void
      {
         this.save = null;
         this.da = null;
         this.DATA = null;
         this.SAVE = null;
         if(clearTempB0)
         {
            TempLevel.clearData();
         }
      }
      
      public function outLoginEvent(clearLoginB0:Boolean) : void
      {
         this.clearSave();
         if(clearLoginB0)
         {
            this.loginData = new LoginData4399();
            this.loginChangeEvent();
         }
      }
      
      private function loginChangeEvent() : void
      {
         var s0:String = "--------------------------";
         s0 += "loginData:" + this.loginData.uid;
         s0 += "\nloginData.getUid():" + this.loginData.getUid();
         s0 += "\nisLog():" + Gaming.api.save.s4399.getLogUid();
         s0 += "\nCookie:" + Gaming.api.save.getUidByCookie();
         s0 += "\n--------------------------";
         SaveTestBox.addText(s0);
      }
      
      public function readSave(obj0:Object, mainPlayerType0:String = "me", apiLogObj0:LoginData4399 = null, saveObj0:Object = null) : void
      {
         var arr0:Array = null;
         var obj2:Object = null;
         if(obj0 is Array)
         {
            arr0 = obj0 as Array;
            for each(obj2 in arr0)
            {
               if(!ClassProperty.isNormalDataType(obj2))
               {
                  obj0 = obj2;
                  break;
               }
            }
         }
         var one_obj0:Object = obj0;
         var meB0:Boolean = mainPlayerType0 == "me";
         this.clearSave(meB0);
         if(meB0)
         {
            GuideSave.dealObj(this.loginData.getNew(),obj0);
            GuideSave.dealU(this.u,this.u2,obj0);
         }
         this.save = new PlayerSave();
         this.save.inData_byObj(one_obj0);
         if(Boolean(saveObj0))
         {
            this.loginData.save.inSaveObj(saveObj0);
         }
         this.readPlayerSave(this.save,false,mainPlayerType0,apiLogObj0);
      }
      
      private function readPlayerSave(s0:PlayerSave, initB0:Boolean = false, mainPlayerType0:String = "me", apiLogObj0:LoginData4399 = null) : void
      {
         this.save = s0;
         this.da = new PlayerData();
         this.da.fleshReference(this.da.more.newMainHeroData(this.da,s0));
         this.da.setMainPlayerType(mainPlayerType0);
         this.da.inData_bySave(this.save);
         if(initB0)
         {
            this.da.initSave(this.da.more.heroData);
         }
         this.da.event_AffterLoadSave();
         if(mainPlayerType0 == MainPlayerType.ME)
         {
            this.fleshGoodsPrice();
            this.dealArmsImage(true,true,true);
            this.da.main.createMd5(this.loginData,Gaming.isLocal());
            this.da.getSave().guide.dealUN(apiLogObj0);
         }
         this.setNowPlayerData(this.da);
      }
      
      public function dealArmsImage(bagB0:Boolean = false, moreB0:Boolean = false, moreBagB0:Boolean = false, clearHouseDataB0:Boolean = true) : void
      {
         GameArmsCtrl.clearArmsImage(true,true,!clearHouseDataB0);
         var armsDataArr0:Array = this.da.getArmsDataArr(bagB0,moreB0,moreBagB0);
         if(armsDataArr0.length > 0)
         {
            GameArmsCtrl.addArmsSaveResoureByArmsDataArr(armsDataArr0);
         }
      }
      
      public function payShopBuyObject(obj0:ShopBuyObject) : void
      {
         this.save.pay.addShopBuyObject(obj0);
         this.save.getCount().todayUseMoney = this.save.getCount().todayUseMoney + obj0.getTotalPrice();
      }
      
      public function setNowMoreData(da0:MoreData) : void
      {
         this.DATA = da0.DATA;
         this.SAVE = da0.save.SAVE;
         this.da.armsBag.fleshData_byEquip(this.DATA.getHeroMerge());
      }
      
      public function setNowPlayerData(da0:NormalPlayerData) : void
      {
         this.setNowMoreData(da0.heroData);
      }
      
      public function get ctrlHero() : HeroBody
      {
         return this.da.hero;
      }
      
      public function get countHero() : HeroBody
      {
         return this.da.hero;
      }
      
      public function get propsHero() : HeroBody
      {
         var hero0:HeroBody = DoubleCtrl.P_1.getData().getCtrlBody() as HeroBody;
         if(hero0 == null)
         {
            hero0 = DoubleCtrl.P_1 as HeroBody;
         }
         return hero0;
      }
      
      public function get stateHero() : HeroBody
      {
         return this.da.hero as HeroBody;
      }
      
      public function get PD_1() : NormalPlayerData
      {
         return Gaming.PG.da;
      }
      
      public function getSaveObj() : Object
      {
         this.da.achieve.fleshSaveGroup();
         return this.save.getCopyObj();
      }
      
      public function getOpposingDataGroup(dg0:ItemsDataGroup) : ItemsDataGroup
      {
         var name0:String = dg0.placeType == ItemsDataGroup.PLACE_BAG ? dg0.dataType : dg0.dataType + "Bag";
         if(this.DATA.hasOwnProperty(name0))
         {
            return this.DATA[name0];
         }
         if(this.da.hasOwnProperty(name0))
         {
            return this.da[name0];
         }
         return null;
      }
      
      public function getUICompareData(dd0:ItemsCompareData) : ItemsCompareData
      {
         var dg0:ItemsDataGroup = null;
         var dd2:ItemsCompareData = new ItemsCompareData();
         if(Boolean(dd0.dataGroup))
         {
            dg0 = dd0.dataGroup;
            if(dg0.placeType != ItemsDataGroup.PLACE_WEAR)
            {
               if(dg0.dataType == ItemsDataGroup.TYPE_ARMS)
               {
                  dd2.dataGroup = Gaming.PG.DATA.arms;
                  dd2.itemsData = Gaming.PG.DATA.arms.getNowData_MaxDpsByType((dd0.itemsData as ArmsData).armsType);
               }
               else if(dg0.dataType == ItemsDataGroup.TYPE_EQUIP)
               {
                  dd2.dataGroup = Gaming.PG.DATA.equip;
                  dd2.itemsData = Gaming.PG.DATA.equip.getOneDataByType((dd0.itemsData as EquipData).save.partType);
               }
            }
         }
         return dd2;
      }
      
      public function fleshGoodsPrice() : void
      {
         GoodsData.levelCoinIncome = Gaming.defineGroup.normal.getLevelCoinIncomePrice(this.da.level);
         GoodsData.levelCoinIncomeNoMin = Gaming.defineGroup.normal.getLevelCoinIncomeNoMin(this.da.level);
         if(GoodsData.levelCoinIncome > 2500)
         {
            GoodsData.levelCoinIncome = 2500;
         }
      }
      
      public function startLevel(pg0:IO_PlayerLevelGetter) : void
      {
         this.da.startLevel(pg0);
      }
      
      public function changeEquip() : void
      {
         var tt0:Number = getTimer();
         this.DATA.fleshAllByEquip();
         this.da.armsBag.fleshData_byEquip(this.DATA.getHeroMerge());
         this.da.armsHouse.fleshData_byEquip(this.DATA.getHeroMerge());
         trace("changeEquip()耗时：" + (getTimer() - tt0));
      }
      
      public function changeArms() : void
      {
         this.DATA.changeArmsPan();
      }
      
      public function overGamingClear(lg0:IO_PlayerLevelGetter) : void
      {
         var n:* = undefined;
         var da0:NormalPlayerData = null;
         var dataArr0:Array = this.da.getPlayerDataArr();
         for(n in dataArr0)
         {
            da0 = dataArr0[n];
            da0.setHero(null,lg0);
         }
         this.da.overGamingClear();
      }
      
      public function overLevel(nowWorldMapDefine0:WorldMapDefine, levelDat0:LevelData, levelDef0:LevelDefine, model0:String) : void
      {
         var worldMapName0:String = null;
         var a_da0:ArenaLevelData = null;
         var s0:WorldMapSave = null;
         if(Boolean(nowWorldMapDefine0))
         {
            worldMapName0 = nowWorldMapDefine0.name;
            if(model0 == PlayMode.ARENA)
            {
               a_da0 = ArenaCtrl.dat;
               this.da.arena.inNowCount(levelDat0.winB,this.da,a_da0,Gaming.api.save.nowSeverTime);
            }
            else
            {
               s0 = this.da.worldMap.saveGroup.getSave(worldMapName0);
               this.da.worldMap.overLevel(worldMapName0,levelDat0.diff);
               if(Boolean(s0))
               {
                  if(levelDat0.winB)
                  {
                     if(MapMode.isNormalLevelB(levelDat0.mapModel) || levelDat0.mapModel == "" || !levelDat0.nowTaskData)
                     {
                     }
                  }
                  else
                  {
                     ++s0.failNum;
                  }
               }
               if(!levelDat0.nowTaskData)
               {
                  this.da.overLevel(levelDat0);
               }
            }
            this.da.main.overLevel(nowWorldMapDefine0,levelDat0,levelDef0,model0);
         }
      }
      
      public function overLevelEvent(lg0:IO_PlayerLevelGetter) : void
      {
         this.da.overLevelEvent(lg0);
      }
      
      public function getMeWeCreatorAddData() : WeCreatorAddData
      {
         var da0:WeCreatorAddData = new WeCreatorAddData();
         da0.camp = "we";
         da0.moreB = true;
         da0.pcId = "";
         da0.rectId = "r_birth";
         if(this.da.isDoubleB())
         {
            da0.dieGotoState = "stru";
         }
         return da0;
      }
      
      public function FTimer(lg0:IO_PlayerLevelGetter) : void
      {
         if(this.time_t < 30)
         {
            ++this.time_t;
         }
         else
         {
            this.time_t = 0;
            this.da.FTimerSecond(lg0);
         }
      }
      
      public function test() : void
      {
         var xg:int = 0;
      }
      
      public function getPD() : PlayerData
      {
         return this.da;
      }
      
      public function getUid() : String
      {
         var uid0:String = "";
         if(Boolean(this.loginData))
         {
            uid0 = this.loginData.getUid();
         }
         if(uid0 == "0" || uid0 == "")
         {
            uid0 = Gaming.api.save.s4399.getLogUid();
         }
         return uid0;
      }
      
      public function getUidx() : String
      {
         return this.getUid() + "_" + this.getSaveIndex();
      }
      
      public function getSaveIndex() : int
      {
         if(Boolean(this.loginData))
         {
            return this.loginData.getSaveIndex();
         }
         return 0;
      }
      
      public function getPlayerName() : String
      {
         if(Boolean(this.save))
         {
            return this.save.base.playerName;
         }
         return "";
      }
      
      public function getUname() : String
      {
         if(Boolean(this.loginData))
         {
            return this.loginData.name;
         }
         return "";
      }
      
      public function getNickName() : String
      {
         if(Boolean(this.loginData))
         {
            return this.loginData.nickName;
         }
         return "";
      }
   }
}

