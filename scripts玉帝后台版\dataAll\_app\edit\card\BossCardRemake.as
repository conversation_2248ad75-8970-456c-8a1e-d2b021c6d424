package dataAll._app.edit.card
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.ObjectMethod;
   import dataAll.items.creator.OneProAgent;
   import dataAll.items.creator.OneProAgentState;
   import dataAll.must.define.MustDefine;
   import dataAll.pro.PropertyArrayDefine;
   
   public class BossCardRemake
   {
      
      public function BossCardRemake()
      {
         super();
      }
      
      public static function getUpValue(name0:String, now0:Number = -1) : Number
      {
         var v0:Number = NaN;
         var max0:Number = BossCardCreator.getRanProMax(name0);
         if(max0 > 0)
         {
            v0 = Math.round(max0 / 10 * 100) / 100;
            if(now0 >= 0)
            {
               if(now0 + v0 > max0)
               {
                  v0 = max0 - now0;
               }
            }
            return v0;
         }
         return 0;
      }
      
      public static function inStateArr(marr0:Array, arr0:Array) : void
      {
         var a0:OneProAgent = null;
         var sameA0:OneProAgent = null;
         for each(a0 in marr0)
         {
            sameA0 = getAgentInArr(a0.name,arr0);
            if(Bo<PERSON>an(sameA0))
            {
               a0.state = sameA0.state;
            }
         }
      }
      
      private static function getAgentInArr(name0:String, arr0:Array) : OneProAgent
      {
         var a0:OneProAgent = null;
         for each(a0 in arr0)
         {
            if(a0.name == name0)
            {
               return a0;
            }
         }
         return null;
      }
      
      public static function getAgentArr(da0:BossCardData) : Array
      {
         var proArr0:Array = getProAgentArr(da0.getAddObj());
         var skillArr0:Array = getSkillAgentArr(da0.getSkillNameArr());
         return proArr0.concat(skillArr0);
      }
      
      private static function getProAgentArr(obj0:Object) : Array
      {
         var n:* = undefined;
         var pro0:String = null;
         var max0:Number = NaN;
         var value0:Number = NaN;
         var min0:Number = NaN;
         var a0:OneProAgent = null;
         var arr0:Array = [];
         for(n in obj0)
         {
            pro0 = n as String;
            max0 = BossCardCreator.getRanProMax(pro0);
            if(max0 > 0)
            {
               value0 = Number(obj0[n]);
               min0 = NumberMethod.toFixed(max0 / 2,2);
               if(value0 >= min0)
               {
                  a0 = new OneProAgent();
                  a0.inPropertyArrayDefineName(pro0,value0);
                  a0.max = max0;
                  a0.state = OneProAgentState.lock;
                  arr0.push(a0);
               }
            }
         }
         return arr0;
      }
      
      private static function getSkillAgentArr(marr0:Array) : Array
      {
         var name0:String = null;
         var a0:OneProAgent = null;
         var arr0:Array = [];
         if(marr0.length > 0)
         {
            name0 = marr0[marr0.length - 1];
            if(BossCardCreator.isMagicSkill(name0))
            {
               a0 = new OneProAgent();
               a0.inSkillName(name0);
               a0.state = OneProAgentState.lock;
               arr0.push(a0);
            }
         }
         return arr0;
      }
      
      public static function remake(da0:BossCardData, agentArr0:Array) : Boolean
      {
         var a0:OneProAgent = null;
         var save0:BossCardSave = null;
         var newObj0:Object = null;
         var newSr0:Array = null;
         var dg0:BossCardDataGroup = null;
         var proArr0:Array = [];
         var upArr0:Array = [];
         var skillArr0:Array = [];
         for each(a0 in agentArr0)
         {
            if(a0.isProB())
            {
               if(a0.state == OneProAgentState.random)
               {
                  proArr0.push(a0.name);
               }
               else if(a0.state == OneProAgentState.up)
               {
                  upArr0.push(a0.name);
               }
            }
            else if(a0.isSkillB())
            {
               if(a0.state == OneProAgentState.random)
               {
                  skillArr0.push(a0.name);
               }
            }
         }
         save0 = da0.getCardSave();
         newObj0 = remakeProArr(proArr0,upArr0,save0.o,da0.getStar());
         newSr0 = remakeSkillArr(skillArr0,da0);
         if(Boolean(newObj0) && Boolean(newSr0))
         {
            save0.o = newObj0;
            save0.sr = newSr0;
            dg0 = da0.getFatherData();
            if(Boolean(dg0))
            {
               ++dg0.getSaveG().rn;
            }
            return true;
         }
         return false;
      }
      
      private static function remakeProArr(proArr0:Array, upArr0:Array, obj0:Object, star0:int) : Object
      {
         var pro0:String = null;
         var d0:PropertyArrayDefine = null;
         var now0:Number = NaN;
         var upValue0:Number = NaN;
         var o2:Object = ObjectMethod.shadowCopy(obj0);
         BossCardCreator.addRanPro(o2,proArr0.length,star0);
         o2 = ObjectMethod.removeArr(o2,proArr0);
         if(ObjectMethod.getObjElementNum(obj0) != ObjectMethod.getObjElementNum(o2))
         {
            return null;
         }
         for each(pro0 in upArr0)
         {
            d0 = Gaming.defineGroup.getPropertyArrayDefine(pro0);
            if(obj0.hasOwnProperty(pro0) == false)
            {
               return null;
            }
            now0 = Number(o2[pro0]);
            upValue0 = getUpValue(pro0,now0);
            now0 = d0.fixedNumber(now0 + upValue0);
            o2[pro0] = now0;
         }
         return o2;
      }
      
      private static function remakeSkillArr(skillArr0:Array, da0:BossCardData) : Array
      {
         var num0:int = 0;
         var newArr0:Array = null;
         var sr0:Array = da0.getCardSave().sr;
         var haveIdArr0:Array = da0.getHaveSkillIdArr();
         var skillIdArr0:Array = BossCardCreator.getIdArrBySkillNameArr(skillArr0);
         var newSr0:Array = ArrayMethod.deductArr(sr0,skillIdArr0);
         if(newSr0.length + skillArr0.length != sr0.length)
         {
            return null;
         }
         num0 = int(skillArr0.length);
         newArr0 = BossCardCreator.getSkillIdArr(0,0,num0,haveIdArr0);
         if(newArr0.length != num0)
         {
            return null;
         }
         return newSr0.concat(newArr0);
      }
      
      public static function getMust(agentArr0:Array) : MustDefine
      {
         var a0:OneProAgent = null;
         var upMul0:Number = NaN;
         var upNum0:int = 0;
         var m0:MustDefine = new MustDefine();
         var num0:int = 18;
         for each(a0 in agentArr0)
         {
            if(a0.state == OneProAgentState.random)
            {
               num0 += 0;
            }
            else if(a0.state == OneProAgentState.lock)
            {
               num0 += 6;
            }
            else if(a0.state == OneProAgentState.up)
            {
               upMul0 = getUpValue(a0.name,a0.value) / BossCardCreator.getRanProMax(a0.name) * 10;
               upNum0 = Math.round(upMul0 * 18);
               num0 += upNum0;
            }
         }
         m0.inThingsDataByArr(["ghostStone;" + num0]);
         return m0;
      }
      
      public static function isAllLockB(agentArr0:Array) : Boolean
      {
         var a0:OneProAgent = null;
         for each(a0 in agentArr0)
         {
            if(a0.state != OneProAgentState.lock)
            {
               return false;
            }
         }
         return true;
      }
   }
}

