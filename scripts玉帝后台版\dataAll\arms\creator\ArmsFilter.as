package dataAll.arms.creator
{
   public class ArmsFilter
   {
      
      public function ArmsFilter()
      {
         super();
      }
      
      public static function get FILTER_MAX() : int
      {
         return 13;
      }
      
      public static function getLabelArr() : Array
      {
         var arr0:Array = [];
         for(var i:int = 1; i <= FILTER_MAX; i++)
         {
            arr0.push(i + "");
         }
         return arr0;
      }
   }
}

