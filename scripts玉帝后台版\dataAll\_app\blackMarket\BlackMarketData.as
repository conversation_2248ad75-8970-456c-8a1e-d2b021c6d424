package dataAll._app.blackMarket
{
   import com.sounto.utils.ArrayMethod;
   import dataAll._player.PlayerData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.equip.EquipDataGroup;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.save.ItemsSave;
   import flash.utils.getTimer;
   
   public class BlackMarketData
   {
      
      public var arms:ArmsDataGroup = new ArmsDataGroup();
      
      public var equip:EquipDataGroup = new EquipDataGroup();
      
      private var PD:PlayerData;
      
      public var save:BlackMarketSave;
      
      public function BlackMarketData()
      {
         super();
         this.arms.placeType = ItemsDataGroup.PLACE_OTHER;
         this.equip.placeType = ItemsDataGroup.PLACE_OTHER;
      }
      
      public function setPlayerData(pd0:NormalPlayerData) : void
      {
         this.PD = pd0 as PlayerData;
         this.arms.setPlayerData(pd0);
         this.equip.setPlayerData(pd0);
      }
      
      public function inData_bySave(save0:BlackMarketSave) : void
      {
         this.save = save0;
         this.arms.inData_bySaveGroup(save0.arms);
         this.equip.inData_bySaveGroup(save0.equip);
      }
      
      public function newDayCtrl() : void
      {
         this.clearData();
         this.save.newDayCtrl();
      }
      
      public function delData(type0:String, da0:IO_ItemsData) : void
      {
         var dg0:ItemsDataGroup = this[type0];
         dg0.removeData(da0);
      }
      
      public function delGoods(name0:String) : void
      {
         ArrayMethod.remove(this.save.goodsNameArr,name0);
      }
      
      public function clearData() : void
      {
         this.arms.clearData();
         this.equip.clearData();
      }
      
      public function getBuyMax() : int
      {
         return 5;
      }
      
      public function getBuySurplus() : int
      {
         return this.getBuyMax() - this.save.buyNum;
      }
      
      public function buyEvent() : void
      {
         ++this.save.buyNum;
      }
      
      public function getDataGroup(type0:String) : ItemsDataGroup
      {
         return this[type0];
      }
      
      public function firstFleshWantSaveB() : Boolean
      {
         if(!this.save.addB)
         {
            this.fleshDataBreak();
            this.save.addB = true;
            return true;
         }
         return false;
      }
      
      public function fleshHand() : void
      {
         ++this.save.fleshNum;
         this.fleshDataBreak();
      }
      
      public function getFleshSurplus() : int
      {
         return 10 - this.save.fleshNum;
      }
      
      private function fleshDataBreak() : void
      {
         var n:* = undefined;
         var num0:int = 0;
         var name0:String = null;
         var dg2:ItemsDataGroup = null;
         var arr0:Array = BlackMarketSave.pro_arr;
         for(n in arr0)
         {
            name0 = arr0[n];
            if(this.hasOwnProperty(name0))
            {
               if(this[name0] is ItemsDataGroup)
               {
                  dg2 = this[arr0[n]];
                  dg2.clearData();
                  this.fleshDataByType(arr0[n]);
               }
            }
         }
         num0 = 10;
         if(this.save.fleshNum >= 1)
         {
            num0 = 15;
         }
         var nameArr0:Array = Gaming.defineGroup.blackMarket.getGoodsNameArr(num0);
         this.save.goodsNameArr = nameArr0;
      }
      
      private function fleshDataByType(type0:String) : void
      {
         var tt0:Number = getTimer();
         var dg0:ItemsDataGroup = this[type0];
         var numMul0:Number = this.save.fleshNum >= 1 ? 2 : 1;
         var newChildSaveArr0:Array = BlackMarketItemsCreator.getSaveArr(type0,this.PD.level,numMul0);
         dg0.addNewSaveArr(newChildSaveArr0);
      }
      
      public function getDataArr(type0:String, minLv0:int = -1, maxLv0:int = -1) : Array
      {
         var da0:IO_ItemsData = null;
         var s0:ItemsSave = null;
         var tt0:Number = getTimer();
         if(minLv0 == -1)
         {
            minLv0 = 0;
         }
         if(maxLv0 == -1)
         {
            maxLv0 = 999;
         }
         var dg0:ItemsDataGroup = this[type0];
         var childArr0:Array = dg0.dataArr;
         var newArr0:Array = [];
         for each(da0 in childArr0)
         {
            s0 = da0.getSave();
            if(!(s0.getTrueLevel() < minLv0 || s0.getTrueLevel() > maxLv0))
            {
               da0.setTempSortId(s0.getColorSortId() + s0.getLevelSortId());
               if(da0 is ArmsData)
               {
                  (da0 as ArmsData).fleshData_byEquip(Gaming.PG.DATA.getHeroMerge());
               }
               newArr0.push(da0);
            }
         }
         newArr0.sort(ItemsDataGroup.sortByTempSortIdFun);
         return newArr0;
      }
      
      public function getGoodsNameArr() : Array
      {
         return this.save.goodsNameArr;
      }
   }
}

