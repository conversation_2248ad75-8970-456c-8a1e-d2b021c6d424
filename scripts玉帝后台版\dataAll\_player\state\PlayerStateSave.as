package dataAll._player.state
{
   import com.sounto.utils.ClassProperty;
   import dataAll._player.IO_PlayerLevelGetter;
   import dataAll._player.state.define.PlayerStateDefine;
   
   public class PlayerStateSave
   {
      
      public static var pro_arr:Array = null;
      
      public var obj:Object = {};
      
      public function PlayerStateSave()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.obj = ClassProperty.copySaveObj(obj0["obj"],PlayerOneStateSave);
      }
      
      public function addByDefine(d0:PlayerStateDefine) : void
      {
         var s0:PlayerOneStateSave = this.obj[d0.name];
         if(<PERSON><PERSON><PERSON>(s0))
         {
            s0.surplusTime += d0.time;
         }
         else
         {
            s0 = new PlayerOneStateSave();
            s0.inDataByDefine(d0);
            this.obj[s0.name] = s0;
         }
      }
      
      private function fleshObj() : void
      {
         var s0:PlayerOneStateSave = null;
         var newObj0:Object = {};
         for each(s0 in this.obj)
         {
            if(!s0.isDieB())
            {
               newObj0[s0.name] = s0;
            }
         }
         this.obj = newObj0;
      }
      
      public function getStateIconDataArr() : Array
      {
         var s0:PlayerOneStateSave = null;
         var arr0:Array = [];
         for each(s0 in this.obj)
         {
            arr0.push(s0.getStateIconData());
         }
         return arr0;
      }
      
      public function FTimerSecond(lg0:IO_PlayerLevelGetter) : void
      {
         var s0:PlayerOneStateSave = null;
         var t0:Number = NaN;
         var d0:PlayerStateDefine = null;
         var dieB0:Boolean = false;
         var gamingB0:Boolean = Boolean(lg0.isOnlyIng());
         var demOrMain99:Boolean = Boolean(lg0.isDemOrHardB());
         var battleB0:Boolean = Boolean(lg0.isUnionBattleB());
         var demOnlyThingsB0:Boolean = Boolean(lg0.isDemonOnlyThingsB());
         var cardPKB0:Boolean = Boolean(lg0.isCardPKB());
         var towerB0:Boolean = Boolean(lg0.isTowerOrUnlendB());
         for each(s0 in this.obj)
         {
            t0 = s0.surplusTime;
            d0 = s0.getDefine();
            if(!d0.gamingB || gamingB0)
            {
               if(!(d0.demNoneB && demOrMain99))
               {
                  if(!(d0.getBattleNoneB() && battleB0))
                  {
                     if(!(d0.demO && demOnlyThingsB0 == false))
                     {
                        if(!cardPKB0)
                        {
                           if(!towerB0)
                           {
                              t0--;
                              s0.surplusTime = t0;
                           }
                        }
                     }
                  }
               }
            }
            if(t0 <= 0)
            {
               dieB0 = true;
            }
         }
         if(dieB0)
         {
            this.fleshObj();
         }
      }
   }
}

