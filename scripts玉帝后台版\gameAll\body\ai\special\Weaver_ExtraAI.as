package gameAll.body.ai.special
{
   public class Weaver_ExtraAI extends Po_AIExtra
   {
      
      public function Weaver_ExtraAI()
      {
         var dat0:PoAIExtraData = null;
         super();
         dat0 = new PoAIExtraData();
         dat0.LABEL = "thornAttack";
         dat0.FIRST_F = 17;
         dat0.END_F = 31;
         dat0.V = 50;
         dat0.min_ra = -Math.PI / 10;
         dat0.max_ra = Math.PI / 10;
         dat0.mustDirectB = true;
         dat0.flipToTargetB = true;
         dataObj[dat0.LABEL] = dat0;
      }
      
      private function thornAttack() : void
      {
         var c_f0:int = _img.nowMc.currentFrame;
         if(c_f0 == 7)
         {
            BB.getSkillCtrl().teleportToAttackBody(300,50,true);
         }
         _dat.stateD.noUnderHurtB = true;
         _dat.stateD.spellImmunityB = true;
      }
      
      override public function FTimer() : void
      {
         super.FTimer();
         if(_img.nowLabel == "thornAttack")
         {
            this.thornAttack();
         }
      }
   }
}

