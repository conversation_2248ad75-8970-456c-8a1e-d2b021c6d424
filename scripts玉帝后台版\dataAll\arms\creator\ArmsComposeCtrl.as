package dataAll.arms.creator
{
   import UI.bag.ItemsGripBtnListCtrl;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._player.PlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.save.ArmsSave;
   import dataAll.equip.define.EquipColor;
   import dataAll.things.ThingsData;
   import dataAll.things.define.ThingsDefine;
   import gameAll.arms.GameArmsCtrl;
   
   public class ArmsComposeCtrl
   {
      
      private static var tempTip:String = "";
      
      public function ArmsComposeCtrl()
      {
         super();
      }
      
      public static function getThingsTip(d0:ThingsDefine, pd0:PlayerData) : String
      {
         var num0:int = 0;
         var must0:int = 0;
         var lv0:int = 0;
         var str0:String = null;
         var armsS0:ArmsSave = null;
         var armsDa0:ArmsData = null;
         var armsD0:ArmsDefine = d0.getComposeItemsDefine() as ArmsDefine;
         if(<PERSON><PERSON><PERSON>(armsD0))
         {
            if(EquipColor.moreBlackB(armsD0.color))
            {
               return BlackArmsComposeCtrl.getThingsTip(d0,pd0);
            }
            num0 = pd0.thingsBag.getThingsNum(d0.name);
            must0 = armsD0.getComposeMustBlack();
            lv0 = getLv(pd0);
            str0 = "可合成武器|<yellow " + lv0 + "级" + armsD0.cnName + "/>";
            str0 += "\n所需碎片|" + ComMethod.colorMustNum(num0,must0) + "个";
            armsS0 = Gaming.defineGroup.armsCreator.getSuperSaveByArmsRangeName(lv0,armsD0.name);
            armsDa0 = new ArmsData();
            armsDa0.inData_bySave(armsS0,pd0);
            str0 += "\n" + armsDa0.getGatherTip(null,false,false);
            str0 += "\n<i1>|<blue <b>获得方式：</b>/>";
            return str0 + armsD0.description;
         }
         return d0.description;
      }
      
      private static function getLv(pd0:PlayerData) : int
      {
         return pd0.level;
      }
      
      public static function compose(da0:ThingsData) : void
      {
         var d0:ThingsDefine = null;
         var pd0:PlayerData = null;
         var armsD0:ArmsDefine = null;
         var num0:int = 0;
         var must0:int = 0;
         var lv0:int = 0;
         var armsS0:ArmsSave = null;
         var loginB0:Boolean = Gaming.PG.loginData.isLoginByJS();
         if(!loginB0)
         {
            Gaming.uiGroup.alertBox.showError("您的账号已经退出登录，无法进行此操作。");
         }
         else
         {
            d0 = da0.save.getDefine();
            pd0 = da0.playerData;
            armsD0 = d0.getComposeItemsDefine() as ArmsDefine;
            if(Boolean(armsD0) && armsD0.getComposeMustBlack() > 0)
            {
               if(EquipColor.moreBlackB(armsD0.color))
               {
                  BlackArmsComposeCtrl.compose(da0);
               }
               else
               {
                  num0 = da0.save.nowNum;
                  must0 = armsD0.getComposeMustBlack();
                  if(num0 < must0)
                  {
                     Gaming.uiGroup.alertBox.showError("碎片个数不足" + must0 + "个，无法合成" + armsD0.cnName + "。");
                  }
                  else if(pd0.armsBag.getSpaceSiteNum() <= 0)
                  {
                     Gaming.uiGroup.alertBox.showError("背包空位不足，无法合成装备。");
                  }
                  else
                  {
                     pd0.thingsBag.useThings(d0.name,must0);
                     lv0 = getLv(da0.playerData);
                     armsS0 = Gaming.defineGroup.armsCreator.getSuperSaveByArmsRangeName(lv0,armsD0.name);
                     armsS0.lockB = true;
                     pd0.armsBag.addSave(armsS0);
                     GameArmsCtrl.addArmsSaveResoure(armsS0);
                     ItemsGripBtnListCtrl.fleshAllBy(pd0.thingsBag);
                     tempTip = "成功合成" + armsD0.cnName + "！";
                     affterCompose();
                  }
               }
            }
         }
      }
      
      private static function affterCompose(v:* = null) : void
      {
         Gaming.uiGroup.alertBox.showSuccess(tempTip);
      }
   }
}

