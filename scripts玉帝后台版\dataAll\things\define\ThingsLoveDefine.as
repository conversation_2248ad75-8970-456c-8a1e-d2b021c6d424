package dataAll.things.define
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   
   public class ThingsLoveDefine
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var growB:Boolean = false;
      
      public function ThingsLoveDefine()
      {
         super();
         this.num = 1;
      }
      
      public function get num() : Number
      {
         return this.CF.getAttribute("num");
      }
      
      public function set num(v0:Number) : void
      {
         this.CF.setAttribute("num",v0);
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function getNum(heroLv0:int) : Number
      {
         var num0:int = 0;
         if(this.growB)
         {
            return int(Math.ceil(heroLv0 / 3 * this.num + 2));
         }
         return this.num;
      }
   }
}

