package gameAll.body.ai.attack
{
   import com.sounto.math.Maths;
   import dataAll.bullet.BulletDefine;
   import dataAll.equip.vehicle.ShootVehicleDefine;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.image.CarImage;
   import gameAll.body.motion.AircraftGroundMotion;
   import gameAll.bullet.CarBulletLauncher;
   
   public class CarAttack_AI extends ShootAttack_AI
   {
      
      private var carImg:CarImage;
      
      public var rollingB:Boolean = true;
      
      public function CarAttack_AI(_BB:IO_NormalBody)
      {
         super(_BB);
         this.carImg = _BB.getImg() as CarImage;
         minGap = 180;
      }
      
      override protected function randomChangeArms() : void
      {
         if(!dat.isLandBody() || !dat.vehicleFatherBody)
         {
            ctrlBullet.nextArms();
         }
         else if(this.subIsHideB())
         {
            ctrlBullet.nextArms();
         }
         else
         {
            (ctrlBullet as CarBulletLauncher).setArms("sub");
         }
      }
      
      private function subIsHideB() : Boolean
      {
         var sd0:ShootVehicleDefine = null;
         if(Boolean(dat.vehicleData))
         {
            sd0 = dat.vehicleData.vehicleDefine as ShootVehicleDefine;
            if(Boolean(sd0))
            {
               if(sd0.sub.hideB)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      override public function stopAttack() : void
      {
         super.stopAttack();
         ctrlBullet.stopLoop();
      }
      
      override protected function setFollowPoint(x0:int, y0:int, waveB0:Boolean = false) : void
      {
         var x2:int = 0;
         if(!dat.isLandBody())
         {
            x0 += (waveState - 0.5) * 2 * minGap * 2;
            followAI.setTargetPoint(x0,y0);
         }
         else if(this.rollingB)
         {
            x2 = x0 + (waveState - 0.5) * 2 * 250;
            followAI.setTargetPoint(x2,y0);
         }
         else
         {
            super.setFollowPoint(x0,y0,waveB0);
         }
      }
      
      override protected function stopFollow() : void
      {
         if(!this.rollingB)
         {
            super.stopFollow();
         }
      }
      
      override protected function isCanShootB(mx0:int, my0:int, x0:int, y0:int, armsD:BulletDefine) : Boolean
      {
         var max0:Number = NaN;
         var ra0:Number = NaN;
         var bb0:Boolean = super.isCanShootB(mx0,my0,x0,y0,armsD);
         if(dat.isEnemy() == false)
         {
            if(!dat.isLandBody() && bb0)
            {
               max0 = AircraftGroundMotion.MAX_RA;
               ra0 = Math.atan2(my0 - y0,mx0 - x0);
               if(!img.rightB)
               {
                  ra0 = Maths.flipRa_Y(ra0);
               }
               if(ra0 > max0 || ra0 < -max0)
               {
                  bb0 = false;
               }
            }
         }
         return bb0;
      }
      
      override public function FTimer(order0:String) : void
      {
         if(!dat.isLandBody())
         {
            dat.stateD.penetrationGap = 9999;
         }
         super.FTimer(order0);
      }
   }
}

