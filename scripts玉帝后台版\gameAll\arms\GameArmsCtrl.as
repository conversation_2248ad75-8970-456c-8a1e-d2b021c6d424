package gameAll.arms
{
   import dataAll.arms.ArmsData;
   import dataAll.arms.save.ArmsSave;
   import w_test.drop.LevelDropCount;
   
   public class GameArmsCtrl
   {
      
      public function GameArmsCtrl()
      {
         super();
      }
      
      public static function addArmsSaveResoure(s0:ArmsSave, addIconB:Boolean = true) : void
      {
         if(LevelDropCount.testB && !s0.isMoreRedB())
         {
            return;
         }
         Gaming.gunImageManager.addImage(s0.getImgLabel(),addIconB);
         Gaming.soundGroup.addSoundFull(s0.shootSoundUrl);
      }
      
      public static function clearArmsImage(noDelPlayerArmsB:Boolean = true, noDelPlayerCtrlGropB:Boolean = true, noDelHouseB:Boolean = false) : void
      {
         var armsLabelArr0:Array = null;
         var arr0:Array = [];
         if(noDelPlayerArmsB && Boolean(Gaming.PG.da))
         {
            armsLabelArr0 = Gaming.PG.da.getNowGunImageNameArr();
            arr0 = arr0.concat(armsLabelArr0);
         }
         if(noDelPlayerCtrlGropB)
         {
            arr0 = arr0.concat(Gaming.PCG.getNowGunImageNameArr());
         }
         if(noDelHouseB)
         {
            arr0 = arr0.concat(Gaming.PG.da.getPlayerGunImageNameArr(false,false,true));
         }
         Gaming.gunImageManager.clearResourceByNoArray(arr0);
      }
      
      public static function addArmsSaveResoureByArmsDataArr(arr0:Array) : void
      {
         var n:* = undefined;
         var da0:ArmsData = null;
         for(n in arr0)
         {
            da0 = arr0[n];
            addArmsSaveResoure(da0.save);
         }
      }
   }
}

