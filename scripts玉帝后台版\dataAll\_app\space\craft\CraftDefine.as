package dataAll._app.space.craft
{
   import com.sounto.utils.ClassProperty;
   import dataAll._base.NormalDefine;
   
   public class CraftDefine extends NormalDefine
   {
      
      public static var pro_arr:Array = null;
      
      public static var mePro_arr:Array = null;
      
      public var skillArr:Array = [];
      
      public function CraftDefine()
      {
         super();
      }
      
      override public function inData_byXML(xml0:XML, father0:String = "") : void
      {
         super.inData_byXML(xml0,father0);
         ClassProperty.inData_byXMLAt(this,xml0,mePro_arr);
      }
      
      override protected function getFinalProArr() : Array
      {
         return pro_arr;
      }
      
      override protected function getBaseClassProArr() : Array
      {
         return pro_arr;
      }
   }
}

