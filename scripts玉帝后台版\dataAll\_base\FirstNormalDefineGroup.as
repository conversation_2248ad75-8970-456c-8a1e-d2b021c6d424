package dataAll._base
{
   public class FirstNormalDefineGroup
   {
      
      public var fatherArrObj:Object = {};
      
      public var fatherObjObj:Object = {};
      
      public var fatherNameArr:Array = [];
      
      public var fatherCnArr:Array = [];
      
      public var obj:Object = {};
      
      protected var cnObj:Object = {};
      
      public var arr:Array = [];
      
      protected var defineClass:Class;
      
      protected var haveCnB:Boolean = false;
      
      public function FirstNormalDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var i:* = undefined;
         var thingsXML0:XMLList = null;
         var fatherName0:String = null;
         var fatherCn0:String = null;
         var index0:int = 0;
         var n:* = undefined;
         var fatherXML0:XMLList = xml0.father;
         for(i in fatherXML0)
         {
            thingsXML0 = fatherXML0[i].body;
            fatherName0 = fatherXML0[i].@name;
            fatherCn0 = fatherXML0[i].@cnName;
            index0 = 0;
            for(n in thingsXML0)
            {
               this.addDefineByXml(n,thingsXML0[n],fatherName0,fatherCn0);
            }
         }
      }
      
      protected function addDefineByXml(n0:int, xml0:XML, father0:String, fatherCn0:String = "") : void
      {
         var d0:Object = new this.defineClass();
         d0["inData_byXML"](xml0,father0);
         this.addDefine(d0,father0,fatherCn0);
      }
      
      protected function addDefine(d0:Object, father0:String, fatherCn0:String = "") : void
      {
         if(!this.fatherArrObj.hasOwnProperty(father0))
         {
            this.fatherArrObj[father0] = [];
            this.fatherNameArr.push(father0);
            this.fatherCnArr.push(fatherCn0);
         }
         this.fatherArrObj[father0].push(d0);
         if(!this.fatherObjObj.hasOwnProperty(father0))
         {
            this.fatherObjObj[father0] = {};
         }
         this.fatherObjObj[father0][d0["name"]] = d0;
         this.obj[d0["name"]] = d0;
         this.arr.push(d0);
         if(this.haveCnB)
         {
            this.cnObj[d0["cnName"]] = d0;
         }
      }
   }
}

