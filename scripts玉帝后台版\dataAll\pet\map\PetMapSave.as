package dataAll.pet.map
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.pet.gene.define.GeneDefine;
   
   public class PetMapSave
   {
      
      public static var pro_arr:Array = [];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var name:String = "";
      
      public function PetMapSave()
      {
         super();
         this.nowDropNum = 0.5;
      }
      
      public function get nowDropNum() : Number
      {
         return this.CF.getAttribute("nowDropNum");
      }
      
      public function set nowDropNum(v0:Number) : void
      {
         this.CF.setAttribute("nowDropNum",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function getGeneDefine() : GeneDefine
      {
         return Gaming.defineGroup.gene.getDefine(this.name);
      }
      
      public function getDropPro() : Number
      {
         var max0:Number = this.getGeneDefine().dropNum;
         var pro0:Number = this.nowDropNum / max0 + 0.001;
         if(pro0 > 0.9)
         {
            pro0 = 0.9;
         }
         return pro0;
      }
   }
}

