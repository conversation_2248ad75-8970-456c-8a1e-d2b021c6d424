package gameAll.achieve
{
   import dataAll._app.task.define.TaskType;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll._player.PlayerData;
   import dataAll._player.count.PlayerCountSave;
   import dataAll._player.more.MorePlayerData;
   import dataAll._player.more.NormalPlayerData;
   
   public class AchieveValueGetting
   {
      
      public function AchieveValueGetting()
      {
         super();
      }
      
      private static function get PD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      public static function getValue(pro0:String) : Number
      {
         var fun0:Function = null;
         var data0:AchieveTempData = AchieveTrigger.tempData;
         var count0:PlayerCountSave = Gaming.PG.save.getCount();
         if(data0.hasOwnProperty(pro0))
         {
            return data0[pro0];
         }
         if(count0.hasOwnProperty(pro0))
         {
            return count0[pro0];
         }
         fun0 = AchieveValueGetting[pro0];
         if(fun0 is Function)
         {
            return fun0();
         }
         return 0;
      }
      
      private static function star5TaskNum() : Number
      {
         return PD.getSave().getCount().task5NameArr.length;
      }
      
      private static function memoryTask() : Number
      {
         return PD.task.getOverTaskNum(TaskType.MEMORY);
      }
      
      private static function spreadTask() : Number
      {
         return PD.task.getOverTaskNum(TaskType.SPREAD);
      }
      
      private static function bwallTask() : Number
      {
         return PD.task.getOverTaskNum(TaskType.BWALL);
      }
      
      private static function deputyTask() : Number
      {
         return PD.task.getOverTaskNum(TaskType.DEPUTY);
      }
      
      private static function allKillNum() : Number
      {
         return PD.moreWay.getAll_countNum("killNum");
      }
      
      private static function dailySignNum() : Number
      {
         return PD.getSave().gift.daily.getSignNum();
      }
      
      private static function wilderKing() : Number
      {
         return PD.wilder.getDiffWinNum(6);
      }
      
      private static function wilderKing7() : Number
      {
         return PD.wilder.getDiffWinNum(7);
      }
      
      private static function VirtualScorpion() : Number
      {
         return PD.wilder.getWilderAllNum("VirtualScorpion");
      }
      
      private static function SaberTiger() : Number
      {
         return PD.wilder.getWilderAllNum("SaberTiger");
      }
      
      private static function endlessGrade99() : Number
      {
         var s0:WorldMapSave = PD.worldMap.saveGroup.getSave("Hospital5");
         if(Boolean(s0))
         {
            return s0.maxEndlessGrade;
         }
         return 0;
      }
      
      private static function loveMaxNum() : Number
      {
         var pd0:NormalPlayerData = null;
         var md0:MorePlayerData = null;
         var num0:int = 0;
         var daarr0:Array = PD.getPlayerDataArr();
         for each(pd0 in daarr0)
         {
            md0 = pd0 as MorePlayerData;
            if(Boolean(md0))
            {
               if(md0.love.getValue() >= 24000)
               {
                  num0++;
               }
            }
         }
         return num0;
      }
      
      private static function unionBattleScore() : Number
      {
         return PD.union.getBattleScore();
      }
      
      private static function bossCardPKNum() : Number
      {
         return PD.bossCard.getSaveG().pkWin;
      }
   }
}

