package dataAll._player.more
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._player.PlayerData;
   import dataAll._player.more.save.MoreSave;
   import dataAll.body.define.HeroDefine;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipType;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.NormalItemsData;
   import dataAll.items.save.ItemsSave;
   import gameAll.arms.GameArmsCtrl;
   
   public class MoreData extends NormalItemsData implements IO_ItemsData
   {
      
      protected static const swapProArr:Array = ["def"];
      
      public var save:MoreSave = null;
      
      public var def:HeroDefine = null;
      
      public var DATA:NormalPlayerData;
      
      public function MoreData()
      {
         super();
      }
      
      public function swap(da0:MoreData) : Boolean
      {
         var obj0:Object = null;
         if(da0 != this)
         {
            this.save.swap(da0.save);
            obj0 = this.getSwapObj();
            this.inSwapObj(da0);
            da0.inSwapObj(obj0);
            return true;
         }
         return false;
      }
      
      private function getSwapObj() : Object
      {
         var name0:String = null;
         var obj0:Object = {};
         for each(name0 in swapProArr)
         {
            obj0[name0] = this[name0];
         }
         return obj0;
      }
      
      private function inSwapObj(obj0:Object) : void
      {
         var name0:String = null;
         var da0:* = undefined;
         for each(name0 in swapProArr)
         {
            da0 = obj0[name0];
            this[name0] = da0;
         }
      }
      
      override public function setAllPlayerData(pd0:NormalPlayerData) : void
      {
         this.DATA = pd0;
      }
      
      public function inData_bySave(s0:MoreSave, pd0:NormalPlayerData) : void
      {
         this.save = s0;
         setPlayerData(pd0);
         this.def = Gaming.defineGroup.body.getHeroDefine(s0.name);
         this.DATA = new MorePlayerData();
         this.DATA.fleshReference(this);
         this.DATA.inData_bySave(this.save.SAVE);
      }
      
      public function affterAddFirstArms() : void
      {
         GameArmsCtrl.addArmsSaveResoureByArmsDataArr(this.DATA.getArmsDataArr());
         this.DATA.fleshAllByEquip();
         this.DATA.fillAllData();
      }
      
      public function normalClone() : IO_ItemsData
      {
         return null;
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.DATA.newDayCtrl(timeStr0);
      }
      
      public function get name() : String
      {
         return this.def.name;
      }
      
      override public function getSave() : ItemsSave
      {
         return this.save;
      }
      
      public function getCnType() : String
      {
         return "佣兵";
      }
      
      public function getIconImgUrl(maxWidth0:int = 0, maxHeight0:int = 0) : String
      {
         if(Boolean(this.DATA))
         {
            return this.DATA.getIconUrl();
         }
         return this.def.headIconUrl;
      }
      
      public function getMoreUIIcon() : String
      {
         var url0:String = this.getIconImgUrl();
         if(url0.indexOf("purpleMei") == 0)
         {
            url0 += "2";
         }
         return url0;
      }
      
      public function getCnName() : String
      {
         if(this.DATA is PlayerData)
         {
            return this.DATA.base.save.playerName;
         }
         return this.def.getRoleCn();
      }
      
      public function getWearCn() : String
      {
         if(this.DATA is PlayerData)
         {
            return this.DATA.base.save.playerName;
         }
         return this.def.getRoleCn();
      }
      
      public function getWearCn2() : String
      {
         var s0:String = this.def.getRoleCn();
         if(playerData.isCpB(this.DATA))
         {
            s0 += ComMethod.graydrak("（掉率不衰减）");
         }
         return s0;
      }
      
      public function getSellPrice() : Number
      {
         return 0;
      }
      
      public function getWearLevel() : int
      {
         return this.save.getTrueLevel();
      }
      
      public function getTypeId() : String
      {
         return "05";
      }
      
      public function getChildTypeId() : String
      {
         return "01";
      }
      
      public function isFightB() : Boolean
      {
         return Gaming.PG.da.moreWay.isFightB(this);
      }
      
      public function clearMoreWeapon() : void
      {
         var arr0:Array = null;
         var da0:EquipData = null;
         if(!(this.DATA is PlayerData))
         {
            arr0 = [];
            for each(da0 in this.DATA.equip.dataArr)
            {
               if(da0.save.partType == EquipType.WEAPON)
               {
                  arr0.push(da0);
               }
            }
            this.DATA.equip.removeDataArr(arr0);
         }
      }
   }
}

