package gameAll.body.ai.extraTask
{
   import dataAll.body.attack.HurtData;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.body.extra.BodyExtraDefine;
   import dataAll.body.extra.BodyExtraDefineGroup;
   import flash.utils.getDefinitionByName;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.action.IO_NormalBodyAction;
   import gameAll.body.ai.Normal_AI;
   import gameAll.body.data.NormalBodyData;
   import gameAll.body.image.NormalImage;
   import gameAll.body.motion.NormalGroundMotion;
   import gameAll.body.skill.SkillDataGroup;
   import gameAll.skill.SkillEffectData;
   
   public class ExtraTaskAI
   {
      
      private static var FightKing:ExtraTaskAI_FightKing;
      
      private static var SpiderSking:ExtraTaskAI_SpiderKing;
      
      public var enabled:Boolean = false;
      
      public var BB:IO_NormalBody;
      
      protected var _def:NormalBodyDefine;
      
      protected var _dat:NormalBodyData;
      
      protected var _img:NormalImage;
      
      protected var _mot:NormalGroundMotion;
      
      protected var _act:IO_NormalBodyAction;
      
      protected var _ai:Normal_AI;
      
      protected var _skill:SkillDataGroup;
      
      private var extraG:BodyExtraDefineGroup;
      
      private var nowExtra:BodyExtraDefine = null;
      
      public function ExtraTaskAI()
      {
         super();
      }
      
      public static function getExtra(name0:String) : ExtraTaskAI
      {
         var class0:Class = getDefinitionByName("gameAll.body.ai.extraTask." + name0) as Class;
         return new class0();
      }
      
      public function init(BB0:IO_NormalBody, ai0:Normal_AI) : *
      {
         this.BB = BB0;
         this._def = BB0.getDefine();
         this._dat = BB0.getData();
         this._img = BB0.getImg();
         this._mot = this.BB.getMot();
         this._act = this.BB.getAction();
         this._ai = ai0;
         this._skill = this.BB.getSkill();
      }
      
      public function openAI(extraG0:BodyExtraDefineGroup) : void
      {
         this.enabled = true;
         this.extraG = extraG0;
      }
      
      protected function setNowExtra(d0:BodyExtraDefine) : void
      {
         this.nowExtra = d0;
         this._skill.setSkillArrCanUse(this.extraG.allExclusiveSkillArr,false);
         this._skill.setSkillArrCanUse(d0.skillArr,true);
         if(this.extraG.stateArr.length > 0)
         {
            this.BB.getState().clearStateByBaseLabelArr(this.extraG.stateArr);
            this.BB.getSkillCtrl().doSkillByOtherNameArr(d0.stateArr);
         }
         this._dat.setEyeLightEffect(d0.eyeLight);
      }
      
      protected function lifePerPan() : void
      {
         var per0:Number = this._dat.getLifePer();
         var d0:BodyExtraDefine = this.extraG.getExtraByLifePer(per0);
         if(d0 is BodyExtraDefine)
         {
            if(this.nowExtra != d0)
            {
               this.setNowExtra(d0);
            }
         }
      }
      
      public function bodyAddBySkill(se0:SkillEffectData, b0:IO_NormalBody) : void
      {
      }
      
      public function summonedDie(b0:IO_NormalBody, b1:IO_NormalBody, h0:HurtData) : void
      {
      }
      
      public function bodyUseSkill(se0:SkillEffectData) : void
      {
      }
      
      public function FTimer() : void
      {
         if(this.enabled)
         {
            this.lifePerPan();
         }
      }
   }
}

