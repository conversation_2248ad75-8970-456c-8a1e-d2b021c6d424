package gameAll.achieve
{
   import com.common.text.TextWay;
   import dataAll._app.achieve.AchieveData;
   import dataAll._app.achieve.define.AchieveConditionDefine;
   import dataAll._app.task.TaskData;
   import dataAll._app.task.define.TaskDefine;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll.body.attack.HurtData;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.suit.SuitEquipDataObj;
   import dataAll.level.LevelDiffGetting;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyData;
   import gameAll.hero.HeroBody;
   import gameAll.level.data.LevelData;
   
   public class AchieveConditionCheck
   {
      
      public function AchieveConditionCheck()
      {
         super();
      }
      
      public function valueCompare(da0:AchieveData) : Boolean
      {
         var con0:AchieveConditionDefine = da0.def.condition;
         var now0:Number = AchieveValueGetting.getValue(con0.pro);
         var tar0:Number = con0.value;
         var bb0:Boolean = now0 >= tar0;
         if(bb0)
         {
            da0.complete();
         }
         else if(con0.isRecordB())
         {
            da0.inInfo(now0 + con0.infoUnit,now0);
         }
         return bb0;
      }
      
      public function mulCompare(da0:AchieveData) : Boolean
      {
         var con0:AchieveConditionDefine = da0.def.condition;
         var now0:Number = AchieveValueGetting.getValue(con0.pro);
         var tar0:Number = con0.mul;
         var bb0:Boolean = now0 >= tar0;
         if(bb0)
         {
            da0.complete();
         }
         return bb0;
      }
      
      public function levelPan(da0:AchieveData, model0:String, mapDefine0:WorldMapDefine, levelDat0:LevelData) : Boolean
      {
         var con0:AchieveConditionDefine = da0.def.condition;
         if(con0.model != "")
         {
            if(con0.model != model0)
            {
               return false;
            }
         }
         if(con0.mapName != "")
         {
            if(con0.mapName != mapDefine0.name)
            {
               return false;
            }
         }
         if(levelDat0.diff < con0.minDiff || levelDat0.diff > con0.maxDiff)
         {
            return false;
         }
         var enemyLv0:int = levelDat0.getEnemyNormalLevel();
         if(enemyLv0 < con0.minLevelLv || enemyLv0 > con0.maxLevelLv)
         {
            return false;
         }
         return true;
      }
      
      public function oneSuit(da0:AchieveData) : Boolean
      {
         var con0:AchieveConditionDefine = da0.def.condition;
         var obj0:SuitEquipDataObj = Gaming.PG.da.equip.suitObj;
         if(Boolean(obj0))
         {
            if(obj0.lv == con0.level && obj0.color == con0.string)
            {
               if(con0.string2 == "" || con0.string2 == obj0.name)
               {
                  da0.complete();
                  return true;
               }
               if(con0.string2 == "rare")
               {
                  if(obj0.fatherDefine.color == EquipColor.RED)
                  {
                     da0.complete();
                     return true;
                  }
               }
            }
         }
         return false;
      }
      
      public function dpsEquip(da0:AchieveData) : Boolean
      {
         var con0:AchieveConditionDefine = da0.def.condition;
         var num0:int = Gaming.PG.da.equip.getAllHurtProNum(con0.level);
         if(num0 >= con0.value)
         {
            da0.complete();
            return true;
         }
         return false;
      }
      
      public function blackArms_4(da0:AchieveData) : Boolean
      {
         if(AchieveTrigger.tempData.blackArmsNum >= da0.def.condition.value)
         {
            if(AchieveTrigger.tempData.blackArmsEvo8Num >= 3)
            {
               da0.complete();
               return true;
            }
         }
         return false;
      }
      
      public function rightHand(da0:AchieveData) : Boolean
      {
         var moreNum0:int = Gaming.PG.da.moreWay.getMoreShowNum();
         if(moreNum0 > 0)
         {
            return false;
         }
         var bulletNum0:int = Gaming.PG.da.moreWay.getAll_maxCountNum("bulletNum");
         if(bulletNum0 > 0)
         {
            return false;
         }
         var petNum0:int = int(Gaming.PG.da.pet.getFightAndSupplePetDataArr().length);
         if(petNum0 > 0)
         {
            return false;
         }
         if(!Gaming.PG.countHero.dat.countD.isOnlyHandB())
         {
            return false;
         }
         da0.complete();
         return true;
      }
      
      public function brokenGunners(da0:AchieveData) : Boolean
      {
         var nowMaxPartsNum0:int = Gaming.PG.da.moreWay.getAll_maxArmsPartsNum();
         if(nowMaxPartsNum0 > 0)
         {
            return false;
         }
         var nowBestArmsColor0:String = Gaming.PG.da.moreWay.getAll_bestArmsColor();
         if(EquipColor.firstMax(nowBestArmsColor0,"blue"))
         {
            return false;
         }
         da0.complete();
         return true;
      }
      
      public function naked(da0:AchieveData) : Boolean
      {
         var maxWearEquipNum0:int = Gaming.PG.da.moreWay.getAll_maxWearEquipNum();
         if(maxWearEquipNum0 > 0)
         {
            return false;
         }
         da0.complete();
         return true;
      }
      
      public function heroHurtLess(da0:AchieveData) : Boolean
      {
         var levelB0:Boolean = false;
         var hero0:HeroBody = Gaming.PG.countHero;
         if(hero0 is HeroBody)
         {
            if(hero0.dat.countD.allHurtValue <= da0.def.condition.value)
            {
               levelB0 = true;
               if(da0.def.condition.string == "sameLevel")
               {
                  levelB0 = Gaming.LG.nowLevel.dat.getEnemyNormalLevel() == hero0.dat.bodyLevel;
               }
               if(levelB0)
               {
                  da0.complete();
                  return true;
               }
            }
         }
         return false;
      }
      
      public function liveTime(da0:AchieveData, b0:IO_NormalBody, b1:IO_NormalBody, h0:HurtData) : Boolean
      {
         var dat0:NormalBodyData = b0.getData();
         var liveTime0:Number = dat0.countD.liveTime;
         if(dat0.countD.liveTime <= da0.def.condition.value)
         {
            da0.complete();
            return true;
         }
         da0.inInfo(int(dat0.countD.liveTime) + "秒",liveTime0);
         return false;
      }
      
      public function heroLifeMulLess(da0:AchieveData, b0:IO_NormalBody, b1:IO_NormalBody, h0:HurtData) : Boolean
      {
         var per0:Number = NaN;
         var dat0:NormalBodyData = b0.getData();
         var hero0:HeroBody = Gaming.PG.countHero;
         if(hero0 is HeroBody)
         {
            if(hero0.dat.bodyLevel == dat0.bodyLevel)
            {
               per0 = hero0.dat.getLifePer();
               if(per0 <= da0.def.condition.mul)
               {
                  da0.complete();
                  return true;
               }
               da0.inInfo(TextWay.numberToPer(per0),per0);
            }
         }
         return false;
      }
      
      public function heroLevelLess(da0:AchieveData, b0:IO_NormalBody, b1:IO_NormalBody, h0:HurtData) : Boolean
      {
         var dat0:NormalBodyData = null;
         var heroLv0:int = 0;
         var con0:AchieveConditionDefine = da0.def.condition;
         if(LevelDiffGetting.getDiff() != con0.minDiff)
         {
            return false;
         }
         if(b1 is IO_NormalBody)
         {
            dat0 = b1.getData();
            heroLv0 = Gaming.PG.da.level;
            if(heroLv0 - dat0.bodyLevel >= con0.value)
            {
               da0.complete();
               return true;
            }
         }
         return false;
      }
      
      public function dropOrange(da0:AchieveData, b0:IO_NormalBody, num0:int) : Boolean
      {
         var con0:AchieveConditionDefine = da0.def.condition;
         if(num0 >= con0.value)
         {
            da0.complete();
            return true;
         }
         return false;
      }
      
      public function taskPan(da0:AchieveData, taskDa0:TaskData) : Boolean
      {
         var diff0:int = 0;
         var con0:AchieveConditionDefine = da0.def.condition;
         var taskD0:TaskDefine = taskDa0.def;
         if(taskD0.name == con0.string)
         {
            diff0 = taskDa0.getDiff();
            if(diff0 >= con0.minDiff && diff0 <= con0.maxDiff)
            {
               da0.complete();
               return true;
            }
         }
         return false;
      }
      
      public function blackMarketMaxNum(da0:AchieveData) : Boolean
      {
         var con0:AchieveConditionDefine = da0.def.condition;
         var now0:int = Gaming.PG.save.getCount().blackMarketMaxNum;
         if(now0 >= con0.value)
         {
            da0.complete();
            return true;
         }
         da0.inInfo(now0 + "次",now0);
         return false;
      }
      
      public function lotteryCoin(da0:AchieveData) : Boolean
      {
         var con0:AchieveConditionDefine = da0.def.condition;
         var now0:int = Gaming.PG.save.getCount().lotteryCoin;
         if(now0 >= con0.value)
         {
            da0.complete();
            return true;
         }
         da0.inInfo(now0 + "次",now0);
         return false;
      }
   }
}

