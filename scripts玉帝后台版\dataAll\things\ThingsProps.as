package dataAll.things
{
   import UI.UIOrder;
   import dataAll._app.edit.boss.BossEditData;
   import dataAll._app.love.LoveData;
   import dataAll._player.PlayerData;
   import dataAll._player.more.MoreData;
   import dataAll._player.role.RoleName;
   import dataAll._player.state.define.PlayerStateDefine;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.skill.define.SkillDefine;
   import dataAll.things.define.ThingsDefine;
   import dataAll.things.define.ThingsEffectDefine;
   import dataAll.things.define.ThingsName;
   import dataAll.things.save.ThingsSave;
   import flash.geom.Rectangle;
   import gameAll.body.IO_NormalBody;
   import gameAll.hero.HeroBody;
   import gameAll.level.PlayMode;
   import gameAll.level.unit.WeCreator;
   import gameAll.more.DoubleCtrl;
   
   public class ThingsProps
   {
      
      public static var gameWorldPropsKeyObj:Object = {
         "lifeBottle":"Z",
         "teamLifeBottle":"X",
         "caisson":"C",
         "teamRebirthCard":"V",
         "skillFleshCard":"B"
      };
      
      private static var thingsObj:Object = {};
      
      private static var gameWorldPropsObj:Object = {};
      
      private static var nowRandomArr:Array = [];
      
      public function ThingsProps()
      {
         super();
      }
      
      public static function init() : void
      {
         var n:* = undefined;
         var name0:String = null;
         var d0:ThingsDefine = null;
         var s0:ThingsSave = null;
         var da0:ThingsData = null;
         var propsNameArr:Array = ThingsName.gameWorldPropsNameArr;
         for(n in propsNameArr)
         {
            name0 = propsNameArr[n];
            d0 = Gaming.defineGroup.things.getDefine(name0);
            s0 = new ThingsSave();
            s0.inData_byDefine(d0);
            s0.nowNum = 0;
            da0 = new ThingsData();
            da0.inData_bySave(s0,null,null);
            gameWorldPropsObj[name0] = da0;
         }
      }
      
      public static function outLoginEvent() : void
      {
         nowRandomArr.length = 0;
         thingsObj = {};
      }
      
      public static function doEffect(dg0:ThingsDataGroup, things_d0:ThingsDefine) : void
      {
         var hero0:HeroBody = null;
         var d0:ThingsEffectDefine = things_d0.effectD;
         var fun0:Function = ThingsProps[d0.type];
         var getArr0:Array = [];
         if(fun0 is Function)
         {
            fun0(dg0,things_d0);
            if(d0.effectLabel != "" && d0.type != "addTeamLifeMul" && d0.type != "rebirthTeam")
            {
               hero0 = Gaming.PG.propsHero;
               if(hero0 is HeroBody)
               {
                  Gaming.EG.follow.addEffectFull(d0.effectLabel,"add",hero0,"",0);
                  Gaming.soundGroup.playSoundFull(d0.effectSound);
               }
            }
         }
      }
      
      private static function addState(dg0:ThingsDataGroup, things_d0:ThingsDefine) : void
      {
         var d0:PlayerStateDefine = null;
         var hero0:HeroBody = null;
         var pd0:PlayerData = dg0.playerData;
         if(Boolean(pd0))
         {
            d0 = things_d0.stateD;
            pd0.state.addByDefine(d0);
            hero0 = Gaming.PG.stateHero;
            if(Boolean(hero0))
            {
               hero0.addAllPlayerState();
            }
         }
      }
      
      private static function addLifeMul(dg0:ThingsDataGroup, things_d0:ThingsDefine) : void
      {
         var d0:ThingsEffectDefine = things_d0.effectD;
         var hero0:HeroBody = Gaming.PG.propsHero;
         var v0:Number = d0.value;
         if(Gaming.LG.mode == PlayMode.ARENA)
         {
            v0 *= 0.3;
         }
         hero0.dat.addLife(v0,true);
         hero0.vehicleCtrl.addLife(v0,true);
      }
      
      private static function addLove(dg0:ThingsDataGroup, things_d0:ThingsDefine) : void
      {
         var d0:ThingsEffectDefine = things_d0.effectD;
         var loveDa0:LoveData = dg0.playerData.getLoveData(RoleName.Girl);
         if(Boolean(loveDa0))
         {
            loveDa0.addValue(d0.value);
            Gaming.uiGroup.alertBox.showSuccess("小樱好感度增加！");
         }
      }
      
      private static function addTeamLifeMul(dg0:ThingsDataGroup, things_d0:ThingsDefine) : void
      {
         var arr0:Array = null;
         var b0:IO_NormalBody = null;
         var d0:ThingsEffectDefine = things_d0.effectD;
         var v0:Number = d0.value;
         if(Gaming.LG.mode == PlayMode.ARENA)
         {
            v0 *= 0.3;
         }
         if(d0.effectLabel != "")
         {
            arr0 = dg0.playerData.moreWay.getMoreLiveHeroArr(false).concat(dg0.playerData.pet.getFightAndSuppleBodyArr());
            for each(b0 in arr0)
            {
               if(b0 is IO_NormalBody && b0 != Gaming.PG.propsHero)
               {
                  if(b0.getDie() == 0)
                  {
                     b0.getData().addLife(v0,true);
                     Gaming.EG.follow.addEffectFull(d0.effectLabel,"add",b0,"",0);
                     b0.getSoundCtrl().playSoundFull(d0.effectSound);
                     if(b0 is HeroBody)
                     {
                        (b0 as HeroBody).vehicleCtrl.addLife(v0,true);
                     }
                  }
               }
            }
         }
      }
      
      private static function addAllChargerMul(dg0:ThingsDataGroup, things_d0:ThingsDefine) : void
      {
         var hero0:HeroBody = null;
         var d0:ThingsEffectDefine = things_d0.effectD;
         var arr0:Array = dg0.playerData.getHeroArr();
         for each(hero0 in arr0)
         {
            if(hero0 is HeroBody)
            {
               if(hero0.getDie() == 0)
               {
                  hero0.dat.chargerData.addChargerNum(d0.value,true,"all");
                  Gaming.EG.follow.addEffectFull(d0.effectLabel,"add",hero0,"",0);
                  hero0.getSoundCtrl().playSoundFull(d0.effectSound);
               }
            }
         }
      }
      
      private static function addGift(dg0:ThingsDataGroup, things_d0:ThingsDefine) : void
      {
         var d0:ThingsEffectDefine = things_d0.effectD;
         var getArr0:Array = GiftAddit.add(things_d0.giftD);
         if(things_d0.getShowGiftB())
         {
            nowRandomArr = nowRandomArr.concat(getArr0);
         }
         if(things_d0.name == "exploitCard")
         {
            Gaming.uiGroup.alertBox.showSuccess("添加" + Gaming.PG.DATA.getCnName() + "的功勋值成功！");
         }
      }
      
      private static function rebirth(dg0:ThingsDataGroup, things_d0:ThingsDefine) : void
      {
         var hero0:HeroBody = Gaming.PG.propsHero;
         var t0:IO_NormalBody = null;
         if(hero0.getDie() == 0)
         {
            t0 = hero0.transCtrl.getBodyHaveCd();
         }
         if(t0 == null)
         {
            t0 = hero0;
         }
         t0.getDieCtrl().rebirth();
         t0.getState().addState_bySkillName("godHiding_things",hero0);
      }
      
      private static function rebirthTeam(dg0:ThingsDataGroup, things_d0:ThingsDefine) : void
      {
         var n:* = undefined;
         var da0:MoreData = null;
         var hero0:HeroBody = null;
         var viewRect0:Rectangle = null;
         var p1:IO_NormalBody = null;
         var d0:ThingsEffectDefine = things_d0.effectD;
         var arr0:Array = dg0.playerData.getMoreDataArr();
         for(n in arr0)
         {
            da0 = arr0[n];
            hero0 = da0.DATA.hero;
            if(hero0 is HeroBody)
            {
               if(hero0.getDie() > 0)
               {
                  hero0.dieCtrl.rebirth();
                  if(DoubleCtrl.levelDoubleB())
                  {
                     viewRect0 = Gaming.sceneGroup.nowViewRect;
                     p1 = DoubleCtrl.P_1;
                     if(p1 is IO_NormalBody)
                     {
                        if(!viewRect0.contains(hero0.mot.x,hero0.mot.MY))
                        {
                           hero0.x = p1.getMot().x;
                           hero0.y = p1.getMot().y;
                        }
                     }
                  }
                  Gaming.EG.follow.addEffectFull(d0.effectLabel,"add",hero0,"",0);
                  hero0.getSoundCtrl().playSoundFull(d0.effectSound);
               }
            }
         }
      }
      
      private static function fleshSkillCd(dg0:ThingsDataGroup, things_d0:ThingsDefine) : void
      {
         var hero0:HeroBody = Gaming.PG.propsHero;
         hero0.skill.fleshAllCd();
         hero0.vehicleCtrl.fleshCd();
         Gaming.uiGroup.gameWorldUI.fleshEach();
      }
      
      private static function skill(dg0:ThingsDataGroup, things_d0:ThingsDefine) : void
      {
         var name0:String = null;
         var d0:SkillDefine = null;
         var hero0:HeroBody = Gaming.PG.propsHero;
         for each(name0 in things_d0.effectD.skillArr)
         {
            d0 = Gaming.defineGroup.skill.getDefine(name0);
            Gaming.TG.skill.bodyDoSkill(d0,hero0);
            if(d0.name == "addMocha")
            {
               Gaming.LG.nowLevel.dat.addMochaB = true;
            }
         }
      }
      
      private static function bossSumCard(dg0:ThingsDataGroup, things_d0:ThingsDefine) : void
      {
         var da0:BossEditData = dg0.playerData.bossEdit.getMain();
         var p1:HeroBody = DoubleCtrl.P_1 as HeroBody;
         if(Boolean(da0) && Boolean(p1))
         {
            WeCreator.addSumBoss(da0,p1,p1.mot.getMapRect());
         }
      }
      
      public static function getGameWorldPropsArr(dg0:ThingsDataGroup) : Array
      {
         var name0:String = null;
         var da0:ThingsData = null;
         thingsObj = {};
         var propsArr0:Array = [];
         var arr0:Array = ThingsName.gameWorldPropsNameArr;
         var arr_len0:int = int(arr0.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            name0 = arr0[i];
            da0 = dg0.getDataBySaveName(name0) as ThingsData;
            if(!(da0 is ThingsData))
            {
               da0 = gameWorldPropsObj[name0];
               da0.playerData = dg0.playerData;
            }
            thingsObj[name0] = da0;
            propsArr0.push(da0);
         }
         return propsArr0;
      }
      
      public static function gameWorldPropsEvent(dg0:ThingsDataGroup, name0:String) : Boolean
      {
         var bb0:Boolean = ThingsUseCtrl.useThings(dg0,name0,1,true,"tip");
         Gaming.soundGroup.playSound("uiSound",bb0 ? "getItems" : "errorClick");
         return bb0;
      }
      
      public static function getThings(name0:String) : ThingsData
      {
         return thingsObj[name0];
      }
      
      public static function getCanUseNum(name0:String) : int
      {
         var da0:ThingsData = getThings(name0);
         if(Boolean(da0))
         {
            return da0.getLevelCanUseNum();
         }
         return 0;
      }
      
      public static function getCanUseNumBy(pd0:PlayerData, name0:String) : int
      {
         var da0:ThingsData = null;
         if(pd0.isMePlayerType())
         {
            return getCanUseNum(name0);
         }
         da0 = pd0.thingsBag.getDataBySaveName(name0) as ThingsData;
         return da0.getLevelCanUseNum();
      }
      
      public static function useIfCan(pd0:PlayerData, name0:String) : Boolean
      {
         var num0:int = getCanUseNumBy(pd0,name0);
         if(num0 > 0)
         {
            gameWorldPropsEvent(pd0.thingsBag,name0);
            return true;
         }
         return false;
      }
      
      public static function randomThingsTip(data0:*, setToSaveTipB0:Boolean = false) : void
      {
         var g0:GiftAddDefineGroup = null;
         var str0:String = null;
         if(nowRandomArr.length > 0)
         {
            g0 = new GiftAddDefineGroup();
            g0.arr = nowRandomArr.concat([]);
            g0 = g0.clone();
            g0.mergeMe();
            str0 = g0.getDescription();
            if(setToSaveTipB0)
            {
               UIOrder.yesSaveTip = str0;
            }
            else
            {
               Gaming.uiGroup.alertBox.showSuccess("获得以下物品：\n" + str0);
            }
         }
         nowRandomArr.length = 0;
      }
      
      public static function clearThingsTip() : void
      {
         nowRandomArr.length = 0;
      }
   }
}

