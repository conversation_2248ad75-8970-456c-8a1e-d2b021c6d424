package dataAll.body.attack
{
   import com.sounto.math.IDRect;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.arms.ArmsData;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.body.define.BodyPart;
   import dataAll.bullet.BulletCritDefine;
   import dataAll.bullet.BulletDefine;
   import dataAll.equip.weapon.WeaponData;
   import dataAll.equip.weapon.WeaponType;
   import dataAll.image.ImageUrlDefine;
   import dataAll.skill.IO_SkillValueGetter;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.skill.StateData;
   import gameAll.bullet.BulletBody;
   import gameAll.effect.NormalEffectAddit;
   import gameAll.skill.SkillEffectData;
   
   public class HurtData
   {
      
      public static const FROM_ATTACK:String = "attack";
      
      public static const FROM_BULLET:String = "bullet";
      
      public static const FROM_SKILL:String = "skill";
      
      public static const CHILD_WORLD:String = "world";
      
      public static const CHILD_BACK_HURT:String = "backHurt";
      
      public static const CHILD_POISON:String = "poison";
      
      public static const CHILD_SPURTING:String = "spurting";
      
      public static const CHILD_CRIT:String = "crit";
      
      public static const CHILD_CURRENT:String = "current";
      
      public static const CHILD_WEAPON:String = "weapon";
      
      public static const CHILD_SELF:String = "self";
      
      public static const CHILD_MUST_MUL:String = "mustMul";
      
      public static const countChildArr:Array = [CHILD_BACK_HURT,CHILD_SELF];
      
      public static var testCountB:Boolean = false;
      
      public static var localCountB:Boolean = false;
      
      public static var pro_arr:Array = [];
      
      private static const _addFlamer_ArmsSkill_link:String = "addFlamer_ArmsSkill_link";
      
      private static const _redFireDem:String = "redFireDem";
      
      private static const _silverScreenBulletHero:String = "silverScreenBulletHero";
      
      private static const _yearCattleSkill:String = "yearCattleSkill";
      
      public var hurtRatio:Number = 1;
      
      public var firstHurt:Number = 1;
      
      public var focoMul:Number = 0;
      
      public var skillAddMul:Number = 1;
      
      public var hurtMul:Number = 0;
      
      public var critExtraMul:Number = 1;
      
      public var critExtraPro:Number = 0;
      
      public var attackType:String = AttackType.DIRECT;
      
      public var skillArr:Array = null;
      
      public var critArr:Array = null;
      
      public var ele:String = "";
      
      public var eleMul:Number = 0;
      
      public var eleArr:Array = null;
      
      public var shakeValue:Number = 0;
      
      public var screenShakeValue:Number = 0;
      
      public var beatBack:Number = 0;
      
      public var hitImg:ImageUrlDefine = null;
      
      public var hitVolume:Number = 1;
      
      public var beatRa:Number = 0;
      
      public var beatX:Number = 0;
      
      public var beatY:Number = 0;
      
      public var targetHurtRect:IDRect = null;
      
      public var producter:IO_NormalBody = null;
      
      public var under:IO_NormalBody = null;
      
      public var from:String = "";
      
      public var fromChild:String = "";
      
      public var fromBullet:BulletBody = null;
      
      public var fromWeapon:WeaponData = null;
      
      public var fromSkill:SkillEffectData = null;
      
      public var fromState:StateData = null;
      
      public var fromAttack:BodyAttackDefine = null;
      
      public var addHurtMulB:Boolean = true;
      
      public var hurtNum:int = 1;
      
      private var _saberDartsBullet:String = "saberDartsBullet";
      
      public function HurtData()
      {
         super();
      }
      
      public static function GET_FromCn(from0:String) : String
      {
         if(from0 == FROM_BULLET)
         {
            return "子弹";
         }
         if(from0 == FROM_SKILL)
         {
            return "技能";
         }
         if(from0 == FROM_ATTACK)
         {
            return "近战";
         }
         return "";
      }
      
      public static function GET_ChildCn(s0:String) : String
      {
         if(s0 == CHILD_WORLD)
         {
            return "世界";
         }
         if(s0 == CHILD_BACK_HURT)
         {
            return "反弹";
         }
         if(s0 == CHILD_POISON)
         {
            return "毒";
         }
         if(s0 == CHILD_SPURTING)
         {
            return "溅射";
         }
         if(s0 == CHILD_CRIT)
         {
            return "暴击";
         }
         if(s0 == CHILD_CURRENT)
         {
            return "电流";
         }
         if(s0 == CHILD_WEAPON)
         {
            return "副手";
         }
         if(s0 == CHILD_SELF)
         {
            return "损己";
         }
         if(s0 == CHILD_MUST_MUL)
         {
            return "强制百分比";
         }
         return "";
      }
      
      public static function getBodyCountCn(b1:IO_NormalBody, colorB0:Boolean) : String
      {
         var s0:String = null;
         if(Boolean(b1))
         {
            s0 = b1.getDefine().getRoleCn();
            if(colorB0)
            {
               if(b1.getData().isEnemy())
               {
                  s0 = ComMethod.drakBlack(s0);
               }
               else
               {
                  s0 = ComMethod.color(s0,"#00C981");
               }
            }
            return s0;
         }
         return "";
      }
      
      public function getFromCn(noBulletB0:Boolean = false) : String
      {
         if(noBulletB0 && this.from == FROM_BULLET)
         {
            return "";
         }
         return GET_FromCn(this.from);
      }
      
      public function getChildCn() : String
      {
         return GET_FromCn(this.fromChild);
      }
      
      public function getKind() : String
      {
         var kind0:String = HurtKind.no;
         if(Boolean(this.fromBullet))
         {
            kind0 = this.fromBullet.define.kind;
         }
         if(kind0 == HurtKind.no)
         {
            if(this.fromChild == CHILD_POISON)
            {
               kind0 = HurtKind.poison;
            }
            else if(this.fromChild == CHILD_CURRENT)
            {
               kind0 = HurtKind.electric;
            }
         }
         return kind0;
      }
      
      public function setFromObj(obj0:Object) : void
      {
         if(obj0 is BulletBody)
         {
            this.fromBullet = obj0 as BulletBody;
         }
         else if(obj0 is WeaponData)
         {
            this.fromWeapon = obj0 as WeaponData;
         }
         else if(obj0 is BodyAttackDefine)
         {
            this.fromAttack = obj0 as BodyAttackDefine;
         }
      }
      
      public function inData(da0:HurtData) : void
      {
         var n0:String = null;
         for each(n0 in pro_arr)
         {
            this[n0] = da0[n0];
         }
      }
      
      public function clone() : HurtData
      {
         var n0:String = null;
         var da0:HurtData = new HurtData();
         for each(n0 in pro_arr)
         {
            da0[n0] = this[n0];
         }
         return da0;
      }
      
      public function cloneNoEffect() : HurtData
      {
         var da0:HurtData = this.clone();
         da0.skillArr = null;
         da0.critArr = null;
         return da0;
      }
      
      public function InDataBullet_byAttack(h0:HurtData) : void
      {
         this.hurtRatio = h0.hurtRatio;
         this.hurtMul = h0.hurtMul;
         this.attackType = h0.attackType;
         this.focoMul = h0.focoMul;
         if(Boolean(h0.skillArr) && h0.skillArr.length > 0)
         {
            this.skillArr = h0.skillArr;
         }
         if(h0.hitImg is ImageUrlDefine)
         {
            if(h0.hitImg.url != "")
            {
               this.hitImg = h0.hitImg;
            }
         }
      }
      
      public function inBeat(h0:HurtData) : void
      {
         this.beatX = h0.beatX;
         this.beatY = h0.beatY;
         this.beatRa = h0.beatRa;
         this.targetHurtRect = h0.targetHurtRect;
      }
      
      public function eleSamePan(ele0:String) : Boolean
      {
         if(ele0 == this.ele)
         {
            return true;
         }
         if(Boolean(this.eleArr))
         {
            if(this.eleArr.indexOf(ele0) >= 0)
            {
               return true;
            }
         }
         return false;
      }
      
      public function addCritD(critD0:BulletCritDefine) : void
      {
         if(!(this.critArr is Array))
         {
            this.critArr = [];
         }
         this.critArr.push(critD0);
      }
      
      public function randomCritMul() : Number
      {
         var arr0:Array = null;
         var arr_len0:int = 0;
         var i:int = 0;
         var d0:BulletCritDefine = null;
         var pro0:Number = NaN;
         var mul0:Number = 1;
         if(this.critArr is Array)
         {
            arr0 = this.critArr;
            arr_len0 = int(arr0.length);
            for(i = 0; i < arr_len0; i++)
            {
               d0 = arr0[i];
               pro0 = d0.pro + this.critExtraPro;
               if(localCountB && d0.mul == 3)
               {
                  pro0 = 0;
               }
               if(Math.random() < pro0)
               {
                  mul0 = d0.mul;
               }
            }
         }
         if(mul0 > 1)
         {
            mul0 *= this.critExtraMul;
         }
         return mul0;
      }
      
      public function getHurtPartId() : String
      {
         if(Boolean(this.targetHurtRect))
         {
            return this.targetHurtRect.id;
         }
         return "body";
      }
      
      public function getNoSkillAddHurt() : Number
      {
         if(this.skillAddMul <= 0)
         {
            this.skillAddMul = 1;
         }
         return this.hurtRatio / this.skillAddMul;
      }
      
      public function isBackHurtB(b0:IO_NormalBody) : Boolean
      {
         var rightB0:Boolean = Boolean(b0.getImg().rightB);
         if(rightB0)
         {
            return this.beatX < b0.getMot().x;
         }
         return this.beatX > b0.getMot().x;
      }
      
      public function isHitB() : Boolean
      {
         return this.from == FROM_ATTACK || this.from == FROM_BULLET;
      }
      
      public function isSkillHitB() : Boolean
      {
         return this.from == FROM_SKILL;
      }
      
      public function isWeaponB() : Boolean
      {
         var name0:String = null;
         var bb0:Boolean = this.fromChild == CHILD_WEAPON || Boolean(this.fromWeapon);
         if(!bb0)
         {
            if(Boolean(this.fromBullet))
            {
               name0 = this.fromBullet.define.name;
               if(name0 == WeaponType.saberDartsBullet || name0 == WeaponType.saberDartsFox)
               {
                  bb0 = true;
               }
            }
         }
         return bb0;
      }
      
      public function isHeadB() : Boolean
      {
         if(Boolean(this.targetHurtRect))
         {
            if(this.targetHurtRect.id == BodyPart.head)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getWhippB() : Boolean
      {
         if(this.fromBullet is BulletBody)
         {
            return this.fromBullet.define.whippB;
         }
         return true;
      }
      
      public function extraShakeValue() : Number
      {
         var d0:ArmsDefine = null;
         if(this.fromBullet is BulletBody)
         {
            if(this.fromBullet.define is ArmsDefine)
            {
               d0 = this.fromBullet.define as ArmsDefine;
               return ArmsType.getDieExtraShake(d0.armsType);
            }
         }
         return 0;
      }
      
      public function getBulletHitNum() : int
      {
         if(this.fromBullet is BulletBody)
         {
            return this.fromBullet.getHitNum();
         }
         return 0;
      }
      
      public function isBounceBulletB() : Boolean
      {
         if(this.fromBullet is BulletBody)
         {
            return this.fromBullet.isBounceNoDogB();
         }
         return false;
      }
      
      public function isImplodingBulletB() : Boolean
      {
         var name0:String = null;
         if(this.fromBullet is BulletBody)
         {
            name0 = this.fromBullet.define.name;
            if(name0 == "imploding_godArmsSkill")
            {
               return true;
            }
            if(name0 == "imploding_enemy")
            {
               return true;
            }
            if(name0 == "implodingEffect")
            {
               return true;
            }
            if(name0 == "implodingEffect2")
            {
               return true;
            }
            if(name0 == "imploding_pet")
            {
               return true;
            }
            if(name0 == "imploding_blackArmsSkill")
            {
               return true;
            }
         }
         return false;
      }
      
      public function getBloodMul(target0:IO_NormalBody) : Number
      {
         if(this.fromBullet is BulletBody)
         {
            return this.fromBullet.define.getBloodMul();
         }
         return 1;
      }
      
      public function getDieBloodMul(target0:IO_NormalBody) : Number
      {
         return 1;
      }
      
      public function canCloseCritB() : Boolean
      {
         var type0:String = null;
         if(this.fromBullet is BulletBody)
         {
            type0 = this.fromBullet.define.armsType;
            return ArmsType.canCloseCritB(type0);
         }
         return true;
      }
      
      public function isFollowBulletB() : Boolean
      {
         var xx0:int = 0;
         if(this.fromBullet is BulletBody)
         {
            if(this.fromBullet.define.followD.noLM == false)
            {
               return this.fromBullet.mot.followVra > 0;
            }
            xx0 = 0;
         }
         return false;
      }
      
      public function getArmsType() : String
      {
         var name0:String = null;
         if(Boolean(this.fromBullet))
         {
            return this.fromBullet.define.armsType;
         }
         if(Boolean(this.fromSkill))
         {
            name0 = this.fromSkill.define.name;
            if(name0 == _redFireDem)
            {
               return ArmsType.rocket;
            }
            if(name0 == _addFlamer_ArmsSkill_link)
            {
               return ArmsType.flamer;
            }
         }
         return "";
      }
      
      public function getArmsData() : ArmsData
      {
         if(Boolean(this.fromBullet))
         {
            return this.fromBullet.define as ArmsData;
         }
         return null;
      }
      
      public function getArmsAttackGap() : Number
      {
         var ad0:ArmsDefine = null;
         if(Boolean(this.fromBullet))
         {
            ad0 = this.fromBullet.define as ArmsDefine;
            if(Boolean(ad0))
            {
               return ad0.attackGap;
            }
         }
         return 0;
      }
      
      public function countArmsSpeedPro(mul0:Number = 1) : Boolean
      {
         var ad0:ArmsDefine = null;
         var effectPro0:Number = NaN;
         if(Boolean(this.fromBullet))
         {
            ad0 = this.fromBullet.define as ArmsDefine;
            effectPro0 = 1;
            if(Boolean(ad0))
            {
               effectPro0 = ad0.attackGap * 3 * mul0;
            }
            if(Math.random() < effectPro0)
            {
               return true;
            }
         }
         return false;
      }
      
      public function haveColorPartsB() : Boolean
      {
         var da0:ArmsData = null;
         if(Boolean(this.fromBullet))
         {
            da0 = this.fromBullet.define as ArmsData;
            if(Boolean(da0))
            {
               return da0.haveColorPartsB();
            }
         }
         return false;
      }
      
      public function ignoreSensitiveB() : Boolean
      {
         if(Boolean(this.fromBullet))
         {
            if(this.fromBullet.define.name == _silverScreenBulletHero)
            {
               return true;
            }
         }
         if(Boolean(this.fromSkill))
         {
            if(this.fromSkill.define.name == _yearCattleSkill)
            {
               return true;
            }
         }
         return false;
      }
      
      public function ignoreArmsSensitiveB() : Boolean
      {
         if(this.ignoreSensitiveB())
         {
            return true;
         }
         return false;
      }
      
      public function beforeDieEventB() : Boolean
      {
         return true;
      }
      
      public function getSkillValueGether() : IO_SkillValueGetter
      {
         if(Boolean(this.fromSkill))
         {
            return this.fromSkill;
         }
         if(Boolean(this.fromState))
         {
            return this.fromState;
         }
         return null;
      }
      
      public function getHurtDescrp(b1:IO_NormalBody = null) : String
      {
         var s0:String = ComMethod.numberToSmall(this.hurtRatio,"") + "——";
         return s0 + this.getHurtCn(b1);
      }
      
      public function getHurtCn(b1:IO_NormalBody = null) : String
      {
         var bd0:BulletDefine = null;
         var s0:String = "";
         var skill0:IO_SkillValueGetter = this.getSkillValueGether();
         if(Boolean(skill0))
         {
            s0 += "" + skill0.getCountCn();
         }
         if(Boolean(this.fromBullet))
         {
            bd0 = this.fromBullet.define;
            if(this.isTrueArms())
            {
               s0 += "★";
            }
            else
            {
               s0 += "_";
            }
            s0 += (bd0.cnName != "" ? bd0.cnName : bd0.name) + "";
         }
         if(Boolean(this.fromAttack))
         {
            s0 += "_" + this.fromAttack.getCountCn();
         }
         s0 += "【" + this.getProducterCountCn(b1) + "】";
         return s0 + this.getFromAndChildCn();
      }
      
      private function getFromAndChildCn(color0:String = "") : String
      {
         var s0:String = "";
         var fromCn0:String = this.getFromCn(true);
         var childCn0:String = this.getChildCn();
         if(fromCn0 != "")
         {
            s0 += " " + fromCn0;
         }
         if(childCn0 != "")
         {
            s0 += "（" + childCn0 + "）";
         }
         if(color0 != "")
         {
            s0 = ComMethod.color(s0,color0);
         }
         return s0;
      }
      
      public function getCountCn() : String
      {
         var first0:String = "";
         if(Boolean(this.fromBullet))
         {
            first0 = this.fromBullet.define.cnName;
         }
         else if(Boolean(this.fromWeapon))
         {
            first0 = this.fromWeapon.weaponDefine.cnName;
         }
         else if(Boolean(this.fromAttack))
         {
            first0 = this.fromAttack.getCountCn();
         }
         var skill0:String = "";
         var skillG0:IO_SkillValueGetter = this.getSkillValueGether();
         if(Boolean(skillG0))
         {
            skill0 = skillG0.getSkillDefine().getCnAndLv();
         }
         var s0:String = first0;
         if(skill0 != "")
         {
            if(skill0.indexOf(first0) >= 0)
            {
               s0 = skill0;
            }
            else
            {
               if(s0 != "")
               {
                  s0 += "-";
               }
               s0 += skill0;
            }
         }
         return s0;
      }
      
      public function getCountOneCn(tipB0:Boolean = false, dieB0:Boolean = false) : String
      {
         var s0:String = this.getProducterCountCn(null,true) + "·";
         var enemyB0:Boolean = Boolean(this.producter) && Boolean(this.producter.getData().isEnemy());
         s0 += this.getCountCn();
         if(enemyB0)
         {
            if(dieB0)
            {
               s0 += ComMethod.red("杀");
            }
            else
            {
               s0 += "→";
            }
            s0 += this.getUnderCountCn(true);
         }
         else
         {
            s0 += this.getFromAndChildCn("#999999");
         }
         if(tipB0)
         {
            if(enemyB0)
            {
               s0 = ComMethod.drakBlack("[敌]") + s0;
            }
         }
         return s0;
      }
      
      public function getProducterCountCn(b1:IO_NormalBody = null, colorB0:Boolean = false) : String
      {
         if(b1 == null)
         {
            b1 = this.producter;
         }
         return getBodyCountCn(b1,colorB0);
      }
      
      public function getUnderCountCn(colorB0:Boolean = false) : String
      {
         return getBodyCountCn(this.under,colorB0);
      }
      
      public function isTrueArms() : Boolean
      {
         var bd0:BulletDefine = null;
         if(Boolean(this.fromBullet))
         {
            bd0 = this.fromBullet.define;
            if(bd0 is ArmsData && this.from == FROM_BULLET && this.fromSkill == null && this.fromState == null)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getTransBackHurtMul() : Number
      {
         if(Boolean(this.fromBullet))
         {
            return this.fromBullet.define.transBackMul;
         }
         if(Boolean(this.fromAttack))
         {
            return this.fromAttack.transBackMul;
         }
         return 1;
      }
      
      public function setHitImgByBullet(bulletD0:BulletDefine) : void
      {
         this.hitVolume = bulletD0.getHitSoundVolume();
         this.hitImg = bulletD0.hitImg;
      }
      
      public function clearHitImg() : void
      {
         this.hitImg = ImageUrlDefine.ZERO;
      }
      
      public function showHitImg() : void
      {
         var vmul0:Number = this.hitVolume;
         if(Boolean(this.under))
         {
            if(this.under.getData().isNormalUnitB() == false)
            {
               vmul0 *= 0.5;
            }
         }
         if(Boolean(this.fromBullet))
         {
            this.fromBullet.showHitImg(this.hitImg,this.beatX,this.beatY,this.beatRa,vmul0);
         }
         else
         {
            NormalEffectAddit.addEffectInstant(this.hitImg,this.beatX,this.beatY,this.beatRa,true,vmul0);
         }
      }
   }
}

