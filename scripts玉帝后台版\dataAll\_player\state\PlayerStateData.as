package dataAll._player.state
{
   import dataAll._player.IO_PlayerLevelGetter;
   import dataAll._player.state.define.PlayerStateDefine;
   
   public class PlayerStateData
   {
      
      private var save:PlayerStateSave = new PlayerStateSave();
      
      public function PlayerStateData()
      {
         super();
      }
      
      public function inData_bySave(s0:PlayerStateSave) : void
      {
         this.save = s0;
      }
      
      public function addByDefine(d0:PlayerStateDefine) : void
      {
         this.save.addByDefine(d0);
      }
      
      public function getPlayerStateDataArr() : Array
      {
         return this.save.getStateIconDataArr();
      }
      
      public function getSaveObj() : Object
      {
         return this.save.obj;
      }
      
      public function getSkillLabelArr(mapMode0:String) : Array
      {
         var s0:PlayerOneStateSave = null;
         var d0:PlayerStateDefine = null;
         var skillArr0:Array = [];
         for each(s0 in this.save.obj)
         {
            d0 = s0.getDefine();
            if(d0.skill != "")
            {
               skillArr0.push(d0.skill);
            }
         }
         return skillArr0;
      }
      
      public function FTimerSecond(lg0:IO_PlayerLevelGetter, timeStopB0:Boolean) : void
      {
         if(timeStopB0 == false)
         {
            this.save.FTimerSecond(lg0);
         }
      }
   }
}

