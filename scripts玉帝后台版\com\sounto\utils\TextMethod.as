package com.sounto.utils
{
   import com.sounto.oldUtils.ComMethod;
   import flash.display.Sprite;
   import flash.filters.GlowFilter;
   import flash.text.StyleSheet;
   import flash.text.TextField;
   import flash.text.TextFormat;
   
   public class TextMethod
   {
      
      private static var shadowFilter:GlowFilter = new GlowFilter(0,1,3,3,20);
      
      public function TextMethod()
      {
         super();
      }
      
      public static function setSpTxt(sp0:Sprite, str0:String) : void
      {
         var txt0:TextField = null;
         if(<PERSON><PERSON><PERSON>(sp0))
         {
            txt0 = sp0["txt"];
            if(str0 != "" && str0 != null)
            {
               txt0.htmlText = str0;
               sp0.visible = true;
            }
            else
            {
               sp0.visible = false;
            }
         }
      }
      
      public static function getLineMidY(txt0:TextField, line0:int) : int
      {
         var tf0:TextFormat = txt0.defaultTextFormat;
         var size0:Number = Number(tf0.size);
         return int(txt0.y + (tf0.leading + size0) * (line0 - 1) + size0 / 2);
      }
      
      public static function getLineHeight(txt0:TextField) : Number
      {
         var tf0:TextFormat = txt0.defaultTextFormat;
         return Number(tf0.size) + Number(tf0.leading);
      }
      
      public static function color(str:String, _color1:String = "#999999", size0:int = 0) : String
      {
         var sizeStr0:String = size0 == 0 ? "" : "size=\'" + size0 + "\' ";
         var colorStr0:String = _color1 == "" ? "" : "color=\'" + _color1 + "\' ";
         if(sizeStr0 != "" || colorStr0 != "")
         {
            return "<font " + sizeStr0 + colorStr0 + ">" + str + "</font>";
         }
         return str;
      }
      
      public static function colorSurplusNum(now0:Number, max0:Number, lessColor0:String = "#00FF00", moreColor0:String = "#FF3300") : String
      {
         return colorMustNum(now0,max0,lessColor0,moreColor0,"",true);
      }
      
      public static function colorMustNum(now0:Number, must0:Number, lessColor0:String = "#FF4600", moreColor0:String = "#00FF00", zeroColor0:String = "", allB0:Boolean = false, overTip0:String = "") : String
      {
         var nowColor0:String = "";
         if(now0 >= must0 && must0 > 0)
         {
            nowColor0 = moreColor0;
         }
         else if(zeroColor0 == "" || now0 > 0)
         {
            nowColor0 = lessColor0;
         }
         else
         {
            nowColor0 = zeroColor0;
         }
         var s0:String = "";
         if(allB0)
         {
            s0 = color(now0 + "/" + must0,nowColor0);
         }
         else
         {
            s0 = color(now0 + "",nowColor0) + "/" + must0;
         }
         if(overTip0 != "")
         {
            if(now0 >= must0)
            {
               s0 += overTip0;
            }
         }
         return s0;
      }
      
      public static function redZeroOrGreen(v0:Number) : String
      {
         return color(v0 + "",v0 > 0 ? "#00FF00" : "#FF3300");
      }
      
      public static function noEnough(str0:String = "(条件不足)") : String
      {
         return color(str0,"#FF0000");
      }
      
      public static function link(str0:String, linkLabel0:String, enabled0:Boolean = true, noColor0:String = "#666666") : String
      {
         if(enabled0)
         {
            return "<a href=\"event:" + linkLabel0 + "\">" + str0 + "</a>";
         }
         return color(str0,noColor0);
      }
      
      public static function getLabelArrLink(labelArr0:Array, cnArr0:Array, chooseLabel0:String) : String
      {
         var label0:String = null;
         var cn0:String = null;
         var s0:String = null;
         var str0:String = "";
         for(var i:int = 0; i < labelArr0.length; i++)
         {
            label0 = labelArr0[i];
            cn0 = cnArr0[i];
            s0 = "";
            if(chooseLabel0 == label0)
            {
               s0 = "<b>" + ComMethod.yellow(cn0) + "</b>";
            }
            else
            {
               s0 = link(cn0,label0);
            }
            if(str0 != "")
            {
               str0 += " ";
            }
            str0 += s0;
         }
         return str0;
      }
      
      public static function findLinkLabel(str0:String) : String
      {
         var last0:int = 0;
         var label0:String = null;
         var es0:String = "event:";
         var e0:int = int(str0.indexOf(es0));
         if(e0 >= 0)
         {
            last0 = int(str0.indexOf("\">"));
            if(last0 > 0)
            {
               return str0.substring(e0 + es0.length,last0);
            }
         }
         return "";
      }
      
      public static function http(str0:String, httpUrl:String, target0:String = "_blank") : String
      {
         return "<a href=\"" + httpUrl + "\" target=\"" + target0 + "\">" + str0 + "</a>";
      }
      
      public static function getLinkCss(linkColor0:String = "#00FFFF", overColor0:String = "#FFFF00", underlineB0:Boolean = true) : StyleSheet
      {
         var style:StyleSheet = new StyleSheet();
         var lineStr0:String = underlineB0 ? "underline" : "none";
         if(linkColor0 == "")
         {
            style.setStyle("a:link",{"textDecoration":lineStr0});
         }
         else
         {
            style.setStyle("a:link",{
               "color":linkColor0,
               "textDecoration":lineStr0
            });
         }
         style.setStyle("a:hover",{
            "color":overColor0,
            "textDecoration":"underline"
         });
         return style;
      }
      
      public static function setNormalFormat(t0:TextField, size0:int = 12, leading0:int = 5, shadowB:Boolean = false, align0:String = "left", color0:uint = 16777215, font0:String = "SimSun") : void
      {
         t0.mouseWheelEnabled = false;
         t0.selectable = false;
         t0.multiline = false;
         t0.embedFonts = false;
         t0.wordWrap = false;
         t0.autoSize = align0;
         var tf:TextFormat = new TextFormat(font0,String(size0),color0,null,null,null,null,null,align0);
         tf.leading = leading0;
         t0.defaultTextFormat = tf;
         if(shadowB)
         {
            t0.filters = [shadowFilter];
         }
      }
      
      public static function setAutoFormat(t0:TextField, align0:String = "left") : void
      {
         t0.mouseWheelEnabled = false;
         t0.selectable = false;
         t0.multiline = false;
         t0.embedFonts = false;
         t0.wordWrap = false;
         t0.autoSize = align0;
      }
      
      public static function copy(t0:TextField) : TextField
      {
         var c0:TextField = new TextField();
         c0.height = t0.height;
         c0.width = t0.width;
         c0.mouseWheelEnabled = t0.mouseWheelEnabled;
         c0.selectable = t0.selectable;
         c0.multiline = t0.multiline;
         c0.embedFonts = t0.embedFonts;
         c0.wordWrap = t0.wordWrap;
         c0.autoSize = t0.autoSize;
         c0.filters = t0.filters;
         c0.defaultTextFormat = t0.defaultTextFormat;
         c0.styleSheet = t0.styleSheet;
         return c0;
      }
   }
}

