package gameAll.body.ai.special
{
   import gameAll.body.IO_NormalBody;
   
   public class WatchEagle_AIExtra extends Po_AIExtra
   {
      
      public function WatchEagle_AIExtra()
      {
         var dat0:PoAIExtraData = null;
         super();
         dat0 = new PoAIExtraData();
         dat0.LABEL = "subductionAttack";
         dat0.FIRST_F = 21;
         dat0.END_F = 32;
         dat0.V = 40;
         dat0.min_ra = Math.PI / 2 - Math.PI / 12;
         dat0.max_ra = Math.PI / 2 + Math.PI / 6;
         dat0.canFlipB = false;
         dataObj[dat0.LABEL] = dat0;
         dat0 = new PoAIExtraData();
         dat0.LABEL = "subductionAttack2";
         dat0.FIRST_F = 21;
         dat0.END_F = 32;
         dat0.V = 40;
         dat0.min_ra = -Math.PI / 2 - Math.PI / 6;
         dat0.max_ra = -Math.PI / 2 + Math.PI / 12;
         dat0.canFlipB = false;
         dataObj[dat0.LABEL] = dat0;
         dat0 = new PoAIExtraData();
         dat0.LABEL = "subductionAttack3";
         dat0.FIRST_F = 21;
         dat0.END_F = 32;
         dat0.V = 40;
         dat0.min_ra = -Math.PI / 6;
         dat0.max_ra = Math.PI / 6;
         dat0.canFlipB = false;
         dataObj[dat0.LABEL] = dat0;
         dat0 = new PoAIExtraData();
         dat0.LABEL = "sprintAttack";
         dat0.FIRST_F = 0;
         dat0.END_F = 12;
         dat0.V = 40;
         dat0.min_ra = Math.PI / 2;
         dat0.max_ra = Math.PI / 2;
         dat0.canFlipB = false;
         dataObj[dat0.LABEL] = dat0;
      }
      
      override public function FTimer() : void
      {
         var c_f0:int = 0;
         var add0:int = 0;
         var first0:int = 0;
         var end0:int = 0;
         canFlipToTargetB = true;
         var label0:String = _img.nowLabel;
         var dat0:PoAIExtraData = dataObj[label0];
         if(dat0 is PoAIExtraData)
         {
            if(label0 == "sprintAttack")
            {
               c_f0 = _img.nowMc.currentFrame;
               add0 = 0;
               if(c_f0 >= 81)
               {
                  add0 = 81;
               }
               else if(c_f0 >= 52)
               {
                  add0 = 52;
               }
               else
               {
                  add0 = 24;
               }
               first0 = dat0.FIRST_F + add0;
               end0 = dat0.END_F + add0;
               doPo(dat0,first0,end0);
               if(c_f0 == 1)
               {
                  this.closeToTarget();
               }
               else if(c_f0 == end0 + 1 && c_f0 < 81)
               {
                  this.closeToTarget();
               }
            }
            else
            {
               doPo(dat0);
            }
         }
      }
      
      private function closeToTarget() : void
      {
         var mx0:int = 0;
         var my0:int = 0;
         var mb0:IO_NormalBody = _ai.attackAI.targetBody;
         if(mb0 is IO_NormalBody)
         {
            mx0 = int(mb0.getMot().x);
            my0 = mb0.getMot().y - 400;
            mx0 = Gaming.sceneGroup.map.rectRange.limitX(mx0);
            my0 = Gaming.sceneGroup.map.rectRange.limitY(my0);
            BB.setXY(mx0,my0);
         }
      }
   }
}

