package gameAll.body.ai.special
{
   import gameAll.body.skill.SkillData;
   
   public class SnowThin_AIExtra extends Po_AIExtra
   {
      
      public function SnowThin_AIExtra()
      {
         var dat0:PoAIExtraData = null;
         super();
         dat0 = new PoAIExtraData();
         dat0.flipToTargetB = true;
         dat0.LABEL = "rotateAttack";
         dat0.FIRST_F = 17;
         dat0.END_F = 36;
         dat0.V = 25;
         dataObj[dat0.LABEL] = dat0;
      }
      
      override public function FTimer() : void
      {
         super.FTimer();
         var per0:Number = Math.ceil(_dat.getLifePer() * 10);
         var da0:SkillData = BB.getSkill().getSkill("SnowThinRuin");
         if(Boolean(da0))
         {
            da0.CD_T_add = -((10 - per0) * 2);
            if(da0.CD_T_add + da0.define.cd < 2)
            {
               da0.CD_T_add = 2 - da0.define.cd;
            }
         }
      }
   }
}

