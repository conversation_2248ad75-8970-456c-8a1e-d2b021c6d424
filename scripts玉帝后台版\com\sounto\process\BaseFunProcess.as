package com.sounto.process
{
   public class BaseFunProcess implements IO_FunProcess
   {
      
      protected var state:String = "no";
      
      protected var index:int = 0;
      
      protected var loopNum:int = 0;
      
      protected var all_t:Number = 0;
      
      protected var first_t:Number = 0;
      
      protected var proPer:Number = 0;
      
      protected var yesFun:Function = null;
      
      public function BaseFunProcess()
      {
         super();
      }
      
      public function start(_yesFun0:Function = null) : void
      {
      }
      
      protected function init() : void
      {
         this.index = 0;
         this.loopNum = 0;
         this.all_t = 0;
         this.first_t = 0;
         this.state = "no";
      }
      
      public function getState() : String
      {
         return this.state;
      }
      
      public function getProPer() : Number
      {
         return this.proPer;
      }
      
      public function setProPer(v0:Number) : void
      {
         this.proPer = v0;
      }
      
      public function getProcessPer() : Number
      {
         if(this.state == "ing" || this.state == "over")
         {
            return this.index / this.loopNum;
         }
         if(this.state == "overing")
         {
            return 1;
         }
         return 0;
      }
      
      public function stopAndClear() : void
      {
         this.init();
      }
      
      public function FTimer() : void
      {
      }
   }
}

