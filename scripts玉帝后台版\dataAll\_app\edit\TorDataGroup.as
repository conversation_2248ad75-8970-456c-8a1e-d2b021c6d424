package dataAll._app.edit
{
   import dataAll._base.OneDataGroup;
   
   public class TorDataGroup extends OneDataGroup
   {
      
      public function TorDataGroup()
      {
         super();
      }
      
      public function getDataByName(name0:String) : TorData
      {
         var da0:TorData = null;
         if(name0 == "")
         {
            return null;
         }
         for each(da0 in arr)
         {
            if(da0.getTorSave().name == name0)
            {
               return da0;
            }
         }
         return null;
      }
   }
}

