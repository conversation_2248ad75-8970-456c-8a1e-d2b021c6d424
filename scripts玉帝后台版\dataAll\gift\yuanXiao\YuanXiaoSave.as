package dataAll.gift.yuanXiao
{
   import com.common.text.TextWay;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.utils.ClassProperty;
   
   public class YuanXiaoSave
   {
      
      public static var pro_arr:Array = null;
      
      private static var OVER_TIME:String = TextWay.toCode32("2024-3-3");
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public function YuanXiaoSave()
      {
         super();
         this.num = 0;
         this.all = 0;
      }
      
      public static function getOverTime() : String
      {
         return TextWay.getText32(OVER_TIME);
      }
      
      public function get num() : Number
      {
         return this.CF.getAttribute("num");
      }
      
      public function set num(v0:Number) : void
      {
         this.CF.setAttribute("num",v0);
      }
      
      public function get all() : Number
      {
         return this.CF.getAttribute("all");
      }
      
      public function set all(v0:Number) : void
      {
         this.CF.setAttribute("all",v0);
      }
      
      public function get rare() : Number
      {
         return this.CF.getAttribute("rare");
      }
      
      public function set rare(v0:Number) : void
      {
         this.CF.setAttribute("rare",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.num = 0;
      }
      
      public function gameStart() : void
      {
         ++this.num;
         ++this.all;
      }
      
      public function getSurplus() : int
      {
         return 3 - this.num;
      }
   }
}

