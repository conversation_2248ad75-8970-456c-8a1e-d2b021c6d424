package dataAll.equip.suit
{
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.save.EquipSave;
   
   public class SuitEquipDataAll
   {
      
      public var obj:Object;
      
      public function SuitEquipDataAll()
      {
         var n:* = undefined;
         this.obj = {};
         super();
         var arr0:Array = EquipType.SUIT_TYPE_ARR;
         for(n in arr0)
         {
            this.obj[arr0[n]] = [];
         }
      }
      
      public function inDataByOne(da0:EquipData, wearB0:Boolean = true, bagB0:Boolean = true) : void
      {
         var n:* = undefined;
         var targetDa0:EquipData = null;
         var targetS0:EquipSave = null;
         var partD0:EquipDefine = null;
         var s0:EquipSave = da0.save;
         var d0:EquipDefine = s0.getDefine();
         var f0:EquipFatherDefine = Gaming.defineGroup.equip.getFatherDefine(d0.father);
         var partDefineObj0:Object = f0.partObj;
         var targetArr0:Array = [];
         if(bagB0)
         {
            targetArr0 = targetArr0.concat(Gaming.PG.da.equipBag.dataArr);
         }
         if(wearB0)
         {
            targetArr0 = targetArr0.concat(Gaming.PG.DATA.equip.dataArr);
         }
         for(n in targetArr0)
         {
            targetDa0 = targetArr0[n];
            if(targetDa0.haveSuitB)
            {
               targetS0 = targetDa0.save;
               partD0 = partDefineObj0[targetS0.partType];
               if(partD0.name == targetS0.imgName)
               {
                  this.addEquipData(targetDa0);
               }
            }
         }
      }
      
      public function addEquipData(da0:EquipData) : void
      {
         this.obj[da0.save.partType].push(da0);
      }
      
      public function isCollectAllB() : Boolean
      {
         var arr0:Array = EquipType.SUIT_TYPE_ARR;
         return this.getCollectedNum() >= arr0.length;
      }
      
      public function getCollectedText() : String
      {
         var arr0:Array = EquipType.SUIT_TYPE_ARR;
         return "(" + this.getCollectedNum() + "/" + arr0.length + ")";
      }
      
      public function getCollectedNum() : int
      {
         var n:* = undefined;
         var num0:int = 0;
         for(n in this.obj)
         {
            if(this.havePart(n))
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function havePart(type0:String) : Boolean
      {
         var arr0:Array = this.obj[type0];
         return arr0.length > 0;
      }
   }
}

