package gameAll.body.ai.one
{
   import com.sounto.utils.ClassProperty;
   
   public class HeroAiData extends ShootAiData
   {
      
      public static var pro_arr:Array = null;
      
      public var vehicleLife:Number = 0.4;
      
      public var weaponB:Boolean = true;
      
      public function HeroAiData()
      {
         super();
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      override protected function getFun(name0:*) : Function
      {
         return this[name0];
      }
      
      protected function FightShooter() : void
      {
         awayInvincibleBuffB = false;
      }
      
      protected function Madboss() : void
      {
         keepGap = 350;
         keepNum = 0;
         keepAwayMoreEnemyB = true;
      }
      
      protected function XiaoMei() : void
      {
         keepGap = 350;
         keepNum = 0;
         awayAttackBuffB = false;
         chooseArmsByGapB = false;
         keepAwayMoreEnemyB = true;
         awayInvincibleBuffB = false;
      }
      
      protected function striker() : void
      {
         keepGap = 550;
         keepNum = 0;
         normalMore();
         awayAttackBuffB = false;
         chooseArmsByGapB = false;
         gotoVeryAwayLifeMul = 0.2;
         jumpPro = 0.1;
         precisionB = false;
         setAreaEacape();
      }
      
      protected function mage() : void
      {
         keepGap = 350;
         keepNum = 0;
         normalMore();
         gotoVeryAwayLifeMul = 0;
         awayAttackBuffB = false;
      }
      
      protected function sniper() : void
      {
         keepGap = 400;
         keepNum = 1;
         normalMore();
         gotoVeryAwayLifeMul = 0;
         awayAttackBuffB = false;
      }
      
      protected function rifle() : void
      {
         keepGap = 350;
         keepNum = 2;
         normalMore();
         gotoVeryAwayLifeMul = 0;
      }
      
      protected function shotgun() : void
      {
         keepGap = 250;
         keepNum = 5;
         normalMore();
         gotoVeryAwayLifeMul = 0;
         awayAttackBuffB = false;
      }
      
      protected function armsDpsTest() : void
      {
         keepGap = 300;
         keepNum = 0;
         normalMore();
         shootHeadPro = 1;
         keepAwayMoreEnemyB = true;
         awayAttackBuffB = false;
         chooseArmsByGapB = false;
         gotoVeryAwayLifeMul = 0;
         jumpPro = 0;
         precisionB = false;
      }
   }
}

