package dataAll.level.define.unit
{
   import com.sounto.utils.ClassProperty;
   
   public class OneUnitDropDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var type:String = "";
      
      public var type2:String = "";
      
      public var name:String = "";
      
      public var num:int = 1;
      
      public var target:String = "";
      
      public var lv:int = 0;
      
      public var color:String = "";
      
      public function OneUnitDropDefine()
      {
         super();
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
   }
}

