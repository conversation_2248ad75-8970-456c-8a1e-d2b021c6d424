package dataAll._player.more
{
   import dataAll._app.love.LoveSave;
   import dataAll._app.partner.PartnerSave;
   import dataAll._data.ConstantDefine;
   import dataAll._player.base.PlayerBaseSave;
   import dataAll._player.count.NormalPlayerCountSave;
   import dataAll.arms.save.ArmsSaveGroup;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.save.EquipSaveGroup;
   import dataAll.skill.save.HeroSkillSaveGroup;
   
   public class NormalPlayerSave
   {
      
      public static var pro_arr:Array = [];
      
      protected static const swapProArr:Array = null;
      
      public var base:PlayerBaseSave = new PlayerBaseSave();
      
      public var arms:ArmsSaveGroup = new ArmsSaveGroup();
      
      public var equip:EquipSaveGroup = new EquipSaveGroup();
      
      public var skill:HeroSkillSaveGroup = new HeroSkillSaveGroup();
      
      public var skillBag:HeroSkillSaveGroup = new HeroSkillSaveGroup();
      
      public var count:NormalPlayerCountSave = new NormalPlayerCountSave();
      
      public var partner:PartnerSave = new PartnerSave();
      
      public var love:LoveSave = new LoveSave();
      
      protected var inputNoDataNameArr:Array = [];
      
      public function NormalPlayerSave()
      {
         super();
         this.inputNoDataNameArr = pro_arr.concat([]);
      }
      
      public function getInputNoDataNameArr() : Array
      {
         return this.inputNoDataNameArr;
      }
      
      public function getSwapObj() : Object
      {
         var name0:String = null;
         var obj0:Object = {};
         for each(name0 in NormalPlayerData.swapProSaveArr)
         {
            obj0[name0] = this[name0];
         }
         return obj0;
      }
      
      public function inSwapObj(obj0:Object) : void
      {
         var name0:String = null;
         for each(name0 in NormalPlayerData.swapProSaveArr)
         {
            this[name0] = obj0[name0];
         }
      }
      
      public function initSave() : void
      {
         this.base.level = 1;
         this.initGripMaxNum();
         this.arms.unlockTo(2 - 1);
         this.equip.unlockTo(this.equip.gripMaxNum - 1);
         this.skill.unlockTo(2 - 1);
         this.skillBag.unlockTo(60 - 1);
         this.partner.initSave();
      }
      
      protected function initGripMaxNum() : void
      {
         this.arms.gripMaxNum = 6;
         this.equip.gripMaxNum = EquipType.TYPE_ARR.length;
         this.skill.gripMaxNum = ConstantDefine.maxSkillNum;
         this.skillBag.gripMaxNum = 36;
         this.skillBag.unlockTo(this.skillBag.gripMaxNum - 1);
         this.equip.unlockTo(this.equip.gripMaxNum - 1);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         var arr0:Array = pro_arr;
         this._inData_byObj(obj0,arr0);
      }
      
      protected function _inData_byObj(obj0:Object, arr0:Array) : void
      {
         var n:* = undefined;
         var pro0:String = null;
         this.inputNoDataNameArr = [];
         this.initGripMaxNum();
         for(n in arr0)
         {
            pro0 = arr0[n];
            if(obj0.hasOwnProperty(pro0))
            {
               if(Boolean(this[pro0].hasOwnProperty("inData_byObj")))
               {
                  this[pro0].inData_byObj(obj0[pro0]);
               }
            }
            else
            {
               this.inputNoDataNameArr.push(pro0);
               if(pro0.indexOf("Bag") > 0)
               {
                  if(Boolean(this[pro0].hasOwnProperty("initBagSave")))
                  {
                     this[pro0]["initBagSave"]();
                  }
               }
               else if(Boolean(this[pro0].hasOwnProperty("initSave")))
               {
                  this[pro0]["initSave"]();
               }
            }
         }
         this.initGripMaxNum();
      }
   }
}

