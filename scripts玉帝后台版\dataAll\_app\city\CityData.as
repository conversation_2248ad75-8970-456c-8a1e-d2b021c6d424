package dataAll._app.city
{
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.TextMethod;
   import dataAll._app.city.dress.CityDressDataGroup;
   import dataAll._player.PlayerData;
   import dataAll._player.time.TimeData;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class CityData
   {
      
      public var playerData:PlayerData;
      
      public var save:CitySave = null;
      
      public var dress:CityDressDataGroup = new CityDressDataGroup();
      
      private var endTime:StringDate = new StringDate("2017-12-4");
      
      public var activityB:Boolean = false;
      
      public function CityData()
      {
         super();
      }
      
      public function inData_bySave(s0:CitySave) : void
      {
         this.save = s0;
         this.dress.inData_bySave(s0.dress);
      }
      
      public function setPlayerData(pd0:PlayerData) : void
      {
         this.playerData = pd0;
         this.dress.playerData = pd0;
      }
      
      public function newDayCtrl(timeDa0:StringDate) : void
      {
         this.save.newDayCtrl(timeDa0);
      }
      
      public function setReadSaveTime(timeData0:TimeData) : void
      {
         this.activityB = false;
      }
      
      public function useNum(d0:GiftAddDefineGroup, num0:int) : void
      {
         this.save.num += num0;
         this.save.allNum += num0;
         this.save.addCount(d0);
      }
      
      public function getSurplusNum() : int
      {
         return this.getAllNum() - this.save.num;
      }
      
      public function getAllNum() : int
      {
         var base0:int = this.getBaseNum();
         var vip0:int = this.playerData.vip.def.smeltNum;
         return base0 + vip0;
      }
      
      private function getBaseNum() : int
      {
         return 8;
      }
      
      public function getNumTip() : String
      {
         var base0:int = this.getBaseNum();
         var tip0:String = "每天可熔炼的次数和VIP等级有关";
         tip0 = TextMethod.color(tip0,"#00FFFF");
         return tip0 + ("\n" + Gaming.defineGroup.vip.getVipTip("smeltNum",base0,base0,this.playerData.vip.def.getTrueLevel()));
      }
   }
}

