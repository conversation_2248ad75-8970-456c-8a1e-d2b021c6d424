package dataAll._app.ask
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.ask.define.AskDefine;
   import dataAll._app.ask.define.AskPropsDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class AskData
   {
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      private var save:AskSave;
      
      private var tempSave:AskSave = new AskSave();
      
      public var nowAskTemp:AskTemp;
      
      public var alreadyArr:Array = [];
      
      public var doubleScoreB:Boolean = false;
      
      public var propsArr:Array = [];
      
      public var state:String = "";
      
      public function AskData()
      {
         super();
         this.nowTime = 0;
      }
      
      public static function getDayAllNum() : int
      {
         return 5;
      }
      
      public static function getBaseScore() : int
      {
         return 32;
      }
      
      private static function countMaxTime(now0:int, max0:int) : int
      {
         if(now0 == 0)
         {
            return 600;
         }
         return max0 - now0 + 20;
      }
      
      private static function countScore(nowTime0:int, maxTime0:int, doubleB0:Boolean) : int
      {
         var base0:int = getBaseScore();
         var doubleMul0:Number = doubleB0 ? 2 : 1;
         return int(base0 * doubleMul0);
      }
      
      private static function getGiftMul(score0:int) : int
      {
         if(score0 > 250)
         {
            score0 = 250;
         }
         return Math.ceil(score0 / 10);
      }
      
      public function get nowTime() : Number
      {
         return this.CF.getAttribute("nowTime");
      }
      
      public function set nowTime(v0:Number) : void
      {
         this.CF.setAttribute("nowTime",v0);
      }
      
      public function inData_bySave(s0:AskSave) : void
      {
         this.save = s0;
         this.tempSave = this.save.clone();
         this.state = this.save.overB ? "end" : "start";
         this.initProps();
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         this.save.newDayCtrl(timeStr0);
         this.tempSave = this.save.clone();
         this.alreadyArr.length = 0;
         this.nowTime = 0;
         this.doubleScoreB = false;
         this.nowAskTemp = null;
         this.state = "start";
         this.propsArr = [];
         this.initProps();
      }
      
      public function getNowState() : String
      {
         return this.state;
      }
      
      public function chooseAnswer(str0:String) : int
      {
         this.state = "chosen";
         var correctB0:Boolean = this.nowAskTemp.def.panAnswer(str0);
         var score0:int = 0;
         if(correctB0)
         {
            score0 = countScore(this.nowTime,this.getMaxTime(),this.doubleScoreB);
            ++this.tempSave.correctNum;
            this.tempSave.addScore(score0);
         }
         else
         {
            ++this.tempSave.errorNum;
         }
         return score0;
      }
      
      public function nextAsk() : Boolean
      {
         var d0:AskDefine = null;
         this.save.overB = true;
         if(this.haveNextAskB())
         {
            this.state = "ing";
            d0 = this.getNextAskDefine();
            this.nowAskTemp = new AskTemp();
            this.nowAskTemp.inData_byDefine(d0);
            this.alreadyArr.push(d0.getId());
            this.nowTime = this.getMaxTime();
            this.doubleScoreB = false;
            return false;
         }
         this.save.inData_byTempObj(this.tempSave);
         this.state = "end";
         return true;
      }
      
      private function getNextAskDefine() : AskDefine
      {
         var nowD0:AskDefine = null;
         var d0:AskDefine = null;
         var loopNum0:int = 20;
         for(var i:int = 0; i < loopNum0; i++)
         {
            d0 = Gaming.defineGroup.ask.getRandomDefine();
            if(this.alreadyArr.indexOf(d0.getId()) == -1 || i == loopNum0 - 1)
            {
               nowD0 = d0;
               break;
            }
         }
         return d0;
      }
      
      public function haveNextAskB() : Boolean
      {
         return this.tempSave.getDayNowNum() < getDayAllNum();
      }
      
      public function getTimeString() : String
      {
         var now0:int = this.nowTime;
         return "答题剩余时间：" + Math.ceil(now0) + "秒";
      }
      
      private function getMaxTime() : int
      {
         var now0:int = this.tempSave.getDayNowNum();
         return countMaxTime(now0,getDayAllNum());
      }
      
      public function timer() : Boolean
      {
         var bb0:Boolean = false;
         if(this.state == "ing")
         {
            bb0 = this.nowTime <= 0;
            --this.nowTime;
            return bb0;
         }
         return true;
      }
      
      public function getEndString() : String
      {
         var str0:String = "";
         if(this.save.todayScore == 0)
         {
            str0 += "今天你没有获得答题积分，无法获得奖励。";
         }
         else
         {
            str0 += "今天你总共获得了" + ComMethod.color(this.save.todayScore + "分","#00FFFF") + "的答题积分，可获得以下奖励：";
         }
         return str0;
      }
      
      public function getGift() : GiftAddDefineGroup
      {
         var baseGift0:GiftAddDefineGroup = null;
         var extraGift0:GiftAddDefineGroup = null;
         var mul0:int = getGiftMul(this.save.todayScore);
         if(mul0 == 0)
         {
            return null;
         }
         baseGift0 = Gaming.defineGroup.gift.getOne("askBaseGift").clone();
         baseGift0.addNumMulClear(mul0);
         if(this.save.correctNum == getDayAllNum())
         {
            extraGift0 = Gaming.defineGroup.gift.getOne("askExtraGift").clone();
            baseGift0.arr = extraGift0.arr.concat(baseGift0.arr);
         }
         return baseGift0;
      }
      
      public function getHaveGetGiftB() : Boolean
      {
         return this.save.giftB;
      }
      
      public function setHaveGetGiftB(bb0:Boolean) : void
      {
         this.save.giftB = bb0;
      }
      
      public function getNumString() : String
      {
         return "第" + this.tempSave.getDayNowNum() + "/" + getDayAllNum() + "题";
      }
      
      public function getCorrectNumString() : String
      {
         return this.tempSave.correctNum + "/" + this.tempSave.getDayNowNum();
      }
      
      public function isAllRightB() : Boolean
      {
         return this.save.overB && this.save.correctNum >= getDayAllNum();
      }
      
      public function initProps() : void
      {
         var d0:AskPropsDefine = null;
         var da0:AskPropsData = null;
         var arr0:Array = Gaming.defineGroup.ask.propsArr;
         for each(d0 in arr0)
         {
            da0 = new AskPropsData();
            da0.def = d0;
            this.propsArr.push(da0);
         }
      }
      
      public function useProps(index0:int) : void
      {
         var da0:AskPropsData = this.propsArr[index0];
         ++da0.useNum;
      }
      
      public function getScore() : int
      {
         return this.save.score;
      }
      
      public function getTodayScore() : int
      {
         return this.tempSave.todayScore;
      }
   }
}

