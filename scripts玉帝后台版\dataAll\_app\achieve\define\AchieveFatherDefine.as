package dataAll._app.achieve.define
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.pro.PropertyArrayDefine;
   
   public class AchieveFatherDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var maxDef:AchieveDefine = null;
      
      private var labelCn:String = "";
      
      public var autoLowB:Boolean = false;
      
      public function AchieveFatherDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function getLabelCn() : String
      {
         var s0:String = null;
         var addStr0:String = null;
         var addObj0:Object = null;
         var n:* = undefined;
         var proD0:PropertyArrayDefine = null;
         var g0:GiftAddDefineGroup = null;
         var oneG0:GiftAddDefine = null;
         if(this.labelCn == "")
         {
            s0 = this.cnName;
            if(Boolean(this.maxDef))
            {
               addStr0 = "";
               addObj0 = this.maxDef.getAddObj();
               for(n in addObj0)
               {
                  proD0 = Gaming.defineGroup.getPropertyArrayDefine(n);
                  if(Boolean(proD0))
                  {
                     addStr0 = proD0.cnName;
                     break;
                  }
               }
               if(addStr0 == "")
               {
                  if(Boolean(this.maxDef.getGift()))
                  {
                     g0 = this.maxDef.getGift();
                     if(g0.haveDataB())
                     {
                        oneG0 = g0.arr[0];
                        addStr0 = oneG0.getCnName();
                     }
                  }
               }
               if(addStr0 != "")
               {
                  s0 += ComMethod.graydrak("  " + addStr0);
               }
            }
            this.labelCn = s0;
         }
         return this.labelCn;
      }
   }
}

