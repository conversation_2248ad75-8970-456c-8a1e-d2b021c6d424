package dataAll._player.define
{
   import dataAll.equip.define.EquipPro;
   
   public class PlayerDataName
   {
      
      public static const equip:String = "equip";
      
      public static const achieve:String = "achieve";
      
      public static const pet:String = "pet";
      
      public static const head:String = "head";
      
      public static const peak:String = "peak";
      
      public static const union:String = "union";
      
      public static const time:String = "time";
      
      public static const post:String = "post";
      
      public static const bossCard:String = "bossCard";
      
      public static const tower:String = "tower";
      
      public static const lotteryGetter:String = "lotteryGetter";
      
      public static const moreWay:String = "moreWay";
      
      public static const vip:String = "vip";
      
      public static const normal_proAddArr:Array = [equip];
      
      public static const player_proAddArr:Array = [achieve,pet,head,peak];
      
      public static const me_proAddArr:Array = [union,time,post,bossCard,tower];
      
      public static const last_proAddArr:Array = [lotteryGetter];
      
      public static const show_proAddArr:Array = [vip,moreWay];
      
      public static const proGotoArr:Array = [pet,head,peak,union,post,tower,bossCard,vip];
      
      public static const cnObj:Object = {
         "equip":"装备",
         "achieve":"成就勋章",
         "pet":"尸宠",
         "head":"称号",
         "peak":"巅峰",
         "union":"军队",
         "time":"道具",
         "post":"职务",
         "bossCard":"魂卡",
         "tower":"虚天塔",
         "lotteryGetter":"幸运值",
         "moreWay":"队友装备",
         "vip":"VIP"
      };
      
      public function PlayerDataName()
      {
         super();
      }
      
      public static function getCn(name0:String, pro0:String = "") : String
      {
         if(name0 == moreWay)
         {
            if(EquipPro.bagDropArr.indexOf(pro0) >= 0)
            {
               return "队友或背包装备";
            }
         }
         return cnObj[name0];
      }
      
      public static function getOneCn(name0:String, pro0:String) : String
      {
         var cn0:String = getCn(name0);
         if(name0 == pet)
         {
            return "宠";
         }
         if(name0 == vip)
         {
            return cn0;
         }
         if(name0 == moreWay)
         {
            if(EquipPro.bagDropArr.indexOf(pro0) >= 0)
            {
               return "队";
            }
         }
         return cn0.substr(0,1);
      }
   }
}

