package dataAll._app.post.define
{
   import dataAll._base.BaseDefineGroup;
   import dataAll.pro.PropertyArrayDefineGroup;
   import dataAll.things.define.ThingsDefine;
   
   public class PostDefineGroup extends BaseDefineGroup
   {
      
      public var proData:PropertyArrayDefineGroup = new PropertyArrayDefineGroup();
      
      public function PostDefineGroup()
      {
         super();
         defineClass = PostDefine;
      }
      
      public static function numDisFun(nowNum0:int) : int
      {
         var c0:int = int(nowNum0 / 6);
         var n0:int = c0 * 2 - 1;
         if(nowNum0 >= 11)
         {
            if(nowNum0 == c0 * 6 + 5)
            {
               return n0 + 1;
            }
            return n0;
         }
         if(nowNum0 >= 6)
         {
            return 1;
         }
         return 0;
      }
      
      public static function test() : void
      {
         for(var i:int = 0; i < 100; i++)
         {
            trace(i + ":" + numDisFun(i));
         }
      }
      
      public function getDefine(name0:String) : PostDefine
      {
         return obj[name0];
      }
      
      override protected function addDefineByXml(n0:int, xml0:XML, father0:String) : void
      {
         var d0:PostDefine = new PostDefine();
         d0.inData_byXML(xml0,father0,n0 + 1);
         addDefine(d0,father0);
      }
      
      public function compare(name1:String, name2:String) : int
      {
         var d1:PostDefine = this.getDefine(name1);
         var d2:PostDefine = this.getDefine(name2);
         var lv1:int = Boolean(d1) ? int(d1.lv) : 0;
         var lv2:int = Boolean(d2) ? int(d2.lv) : 0;
         if(lv1 < lv2)
         {
            return -1;
         }
         if(lv1 > lv2)
         {
            return 1;
         }
         return 0;
      }
      
      public function init() : void
      {
         var d0:PostDefine = null;
         var thingD0:ThingsDefine = null;
         for each(d0 in obj)
         {
            thingD0 = Gaming.defineGroup.things.getDefine(d0.name);
            thingD0.description = d0.getGatherTip();
         }
         thingD0 = Gaming.defineGroup.things.getDefine(PostDefine.platinumMonthCard3);
         d0 = this.getDefine(PostDefine.platinumMonthCard);
         thingD0.description = d0.getGatherTip(PostDefine.getOnlyDay(PostDefine.platinumMonthCard3));
      }
   }
}

