package dataAll._player.base
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.cf.StringCF;
   import com.sounto.utils.ClassProperty;
   import dataAll._player.role.RoleName;
   
   public class PlayerMainSave
   {
      
      public static var pro_arr:Array = null;
      
      protected var CF:NiuBiCF = new NiuBiCF();
      
      private var stringCF:StringCF = new StringCF();
      
      public var girlSkillNum:Number = 0;
      
      public var SkillNum1:Number = 0;
      
      public var SkillNum2:Number = 0;
      
      public var delSS:Number = 0;
      
      public var ai:int = 0;
      
      public var thin:int = 0;
      
      public var role:String = "Striker";
      
      public var before:String = "";
      
      public var swapNum:int = 0;
      
      public var swapEquipB:Boolean = false;
      
      public var uP1:Array = [];
      
      public var bP1:Array = [];
      
      public var armsSkin:Array = [];
      
      public var bookObj:Object = {};
      
      public var isZuobiB:Boolean = false;
      
      public var versionNumber:Number = 0;
      
      public var gift18:Boolean = false;
      
      public var yearArms:Boolean = false;
      
      public var thisYearArms:Boolean = false;
      
      public var weekArmsArr:Array = [];
      
      public var changeNameB:Boolean = false;
      
      public var memArms:int = 0;
      
      public var vehicleBackB:Boolean = false;
      
      public var wt27B:Boolean = false;
      
      public var ab27:String = "";
      
      public var arms283:int = 0;
      
      public var t38:Boolean = false;
      
      public function PlayerMainSave()
      {
         super();
         this.coin = 0;
         this.score = 0;
         this.uidMd5 = "";
         this.pass = "";
         this.zuobiReason = "";
         this.goldSpade = 0;
      }
      
      public static function getSwapMax() : int
      {
         return 40;
      }
      
      public static function getScoreDescription() : String
      {
         var str0:String = "<yellow <b>玩家积分获取方式：</b>/>";
         return str0 + "\n1、每次获得竞技场分数都会增加同等数量的玩家积分。";
      }
      
      public function getOweNuclearStone() : Number
      {
         var createTime0:String = Gaming.PG.loginData.getCreateTime();
         if(createTime0.indexOf("2025-07-16") >= 0 || createTime0.indexOf("2025-07-17") >= 0)
         {
            return 0;
         }
         if(Boolean(Gaming.PG.da))
         {
            if(Gaming.PG.da.getBeforeRole() == RoleName.Girl)
            {
               return 0;
            }
         }
         var more0:Number = this.SkillNum2 - this.SkillNum1;
         return more0 - this.delSS;
      }
      
      public function get zongzi25() : Number
      {
         return this.CF.getAttribute("zongzi25");
      }
      
      public function set zongzi25(v0:Number) : void
      {
         this.CF.setAttribute("zongzi25",v0);
      }
      
      public function setActivePrice(v0:*) : void
      {
         this.zongzi25 = v0;
      }
      
      public function getActivePrice() : Number
      {
         return this.zongzi25;
      }
      
      public function get zongzi25_num() : Number
      {
         return this.CF.getAttribute("zongzi25_num");
      }
      
      public function set zongzi25_num(v0:Number) : void
      {
         this.CF.setAttribute("zongzi25_num",v0);
      }
      
      public function addActiveDay(v0:Number) : void
      {
         this.zongzi25_num += v0;
      }
      
      public function setActiveDay(v0:Number) : void
      {
         this.zongzi25_num = v0;
      }
      
      public function getActiveDay() : Number
      {
         return this.zongzi25_num;
      }
      
      public function get maxDp() : Number
      {
         return this.CF.getAttribute("maxDp");
      }
      
      public function set maxDp(v0:Number) : void
      {
         this.CF.setAttribute("maxDp",v0);
      }
      
      public function get coin() : Number
      {
         return this.CF.getAttribute("coin");
      }
      
      public function set coin(v0:Number) : void
      {
         this.CF.setAttribute("coin",v0);
      }
      
      public function get score() : Number
      {
         return this.CF.getAttribute("score");
      }
      
      public function set score(v0:Number) : void
      {
         this.CF.setAttribute("score",v0);
      }
      
      public function get zuobiReason() : String
      {
         return this.stringCF.getAttribute("zuobiReason") as String;
      }
      
      public function set zuobiReason(str0:String) : void
      {
         this.stringCF.setAttribute("zuobiReason",str0);
      }
      
      public function get uidMd5() : String
      {
         return this.stringCF.getAttribute("uidMd5") as String;
      }
      
      public function set uidMd5(str0:String) : void
      {
         this.stringCF.setAttribute("uidMd5",str0);
      }
      
      public function get pass() : String
      {
         return this.stringCF.getAttribute("pass") as String;
      }
      
      public function set pass(str0:String) : void
      {
         this.stringCF.setAttribute("pass",str0);
      }
      
      public function get s18() : Number
      {
         return this.CF.getAttribute("s18");
      }
      
      public function set s18(v0:Number) : void
      {
         this.CF.setAttribute("s18",v0);
      }
      
      public function get barren() : Number
      {
         return this.CF.getAttribute("barren");
      }
      
      public function set barren(v0:Number) : void
      {
         this.CF.setAttribute("barren",v0);
      }
      
      public function get goldSpade() : Number
      {
         return this.CF.getAttribute("goldSpade");
      }
      
      public function set goldSpade(v0:Number) : void
      {
         this.CF.setAttribute("goldSpade",v0);
      }
      
      public function get bs() : Number
      {
         return this.CF.getAttribute("bs");
      }
      
      public function set bs(v0:Number) : void
      {
         this.CF.setAttribute("bs",v0);
      }
      
      public function get dembs() : Number
      {
         return this.CF.getAttribute("dembs");
      }
      
      public function set dembs(v0:Number) : void
      {
         this.CF.setAttribute("dembs",v0);
      }
      
      public function get anniCoin() : Number
      {
         return this.CF.getAttribute("anniCoin");
      }
      
      public function set anniCoin(v0:Number) : void
      {
         this.CF.setAttribute("anniCoin",v0);
      }
      
      public function get tenCoin() : Number
      {
         return this.CF.getAttribute("tenCoin");
      }
      
      public function set tenCoin(v0:Number) : void
      {
         this.CF.setAttribute("tenCoin",v0);
      }
      
      public function get partsC() : Number
      {
         return this.CF.getAttribute("partsC");
      }
      
      public function set partsC(v0:Number) : void
      {
         this.CF.setAttribute("partsC",v0);
      }
      
      public function get daySweeping() : Number
      {
         return this.CF.getAttribute("daySweeping");
      }
      
      public function set daySweeping(v0:Number) : void
      {
         this.CF.setAttribute("daySweeping",v0);
      }
      
      public function get dayLottery() : Number
      {
         return this.CF.getAttribute("dayLottery");
      }
      
      public function set dayLottery(v0:Number) : void
      {
         this.CF.setAttribute("dayLottery",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.bookObj = ClassProperty.copyObj(obj0["bookObj"]);
      }
      
      public function newDayCtrl() : void
      {
         this.yearArms = false;
         this.thisYearArms = false;
         this.daySweeping = 0;
         this.goldSpade = 0;
         this.dayLottery = 0;
         this.swapNum = 0;
         this.setActiveDay(0);
      }
      
      public function newWeek() : void
      {
         this.changeNameB = false;
         this.weekArmsArr.length = 0;
         this.bs = 0;
         this.dembs = 0;
         this.barren = 0;
      }
      
      public function addUnlockP1(name0:String) : void
      {
         if(this.isUnlockP1(name0) == false)
         {
            this.uP1.push(name0);
         }
      }
      
      public function isUnlockP1(name0:String) : Boolean
      {
         return this.uP1.indexOf(name0) >= 0;
      }
      
      public function addHavedP1(name0:String) : void
      {
         if(this.isHaveP1(name0) == false)
         {
            this.bP1.push(name0);
         }
      }
      
      public function isHaveP1(name0:String) : Boolean
      {
         return this.bP1.indexOf(name0) >= 0;
      }
   }
}

