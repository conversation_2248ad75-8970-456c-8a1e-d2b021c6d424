package dataAll._player
{
   import com.sounto.utils.ClassProperty;
   import dataAll._app.achieve.AchieveSaveGroup;
   import dataAll._app.active.ActiveSave;
   import dataAll._app.arena.ArenaSave;
   import dataAll._app.ask.AskSave;
   import dataAll._app.blackMarket.BlackMarketSave;
   import dataAll._app.city.CitySave;
   import dataAll._app.edit.arms.ArmsTorSaveGroup;
   import dataAll._app.edit.boss.BossEditSaveGroup;
   import dataAll._app.edit.card.BossCardSaveGroup;
   import dataAll._app.food.FoodSave;
   import dataAll._app.goods.GoodsSaveGroup;
   import dataAll._app.head.HeadSave;
   import dataAll._app.peak.PeakSave;
   import dataAll._app.post.PostSave;
   import dataAll._app.setting.SettingSave;
   import dataAll._app.space.SpaceSave;
   import dataAll._app.task.save.TaskSaveGroup;
   import dataAll._app.tower.TowerSave;
   import dataAll._app.union.UnionSave;
   import dataAll._app.vip.VipSave;
   import dataAll._app.wilder.WilderSaveGroup;
   import dataAll._app.worldMap.save.WorldMapSaveGroup;
   import dataAll._player.base.PlayerMainSave;
   import dataAll._player.count.HeadCountSave;
   import dataAll._player.count.PlayerCountSave;
   import dataAll._player.more.NormalPlayerSave;
   import dataAll._player.more.save.MoreSave;
   import dataAll._player.more.save.MoreSaveGroup;
   import dataAll._player.state.PlayerStateSave;
   import dataAll._player.time.TimeSave;
   import dataAll.arms.save.ArmsSaveGroup;
   import dataAll.drop.DropSave;
   import dataAll.equip.save.EquipSaveGroup;
   import dataAll.gift.save.GiftSave;
   import dataAll.pay.PaySave;
   import dataAll.pet.PetSaveGroup;
   import dataAll.pet.gene.save.GeneSaveGroup;
   import dataAll.things.save.ThingsSaveGroup;
   import dataAll.ui.guide.GuideSave;
   import flash.utils.ByteArray;
   
   public class PlayerSave extends NormalPlayerSave
   {
      
      public static var pro_arr:Array = [];
      
      public var main:PlayerMainSave = new PlayerMainSave();
      
      public var armsBag:ArmsSaveGroup = new ArmsSaveGroup();
      
      public var armsHouse:ArmsSaveGroup = new ArmsSaveGroup();
      
      public var equipBag:EquipSaveGroup = new EquipSaveGroup();
      
      public var equipHouse:EquipSaveGroup = new EquipSaveGroup();
      
      public var thingsBag:ThingsSaveGroup = new ThingsSaveGroup();
      
      public var partsBag:ThingsSaveGroup = new ThingsSaveGroup();
      
      public var more:MoreSaveGroup = new MoreSaveGroup();
      
      public var moreBag:MoreSaveGroup = new MoreSaveGroup();
      
      public var geneBag:GeneSaveGroup = new GeneSaveGroup();
      
      public var goods:GoodsSaveGroup = new GoodsSaveGroup();
      
      public var worldMap:WorldMapSaveGroup = new WorldMapSaveGroup();
      
      public var task:TaskSaveGroup = new TaskSaveGroup();
      
      public var guide:GuideSave = new GuideSave();
      
      public var setting:SettingSave = new SettingSave();
      
      public var time:TimeSave = new TimeSave();
      
      public var state:PlayerStateSave = new PlayerStateSave();
      
      public var gift:GiftSave = new GiftSave();
      
      public var city:CitySave = new CitySave();
      
      public var vip:VipSave = new VipSave();
      
      public var arena:ArenaSave = new ArenaSave();
      
      public var pay:PaySave = new PaySave();
      
      public var blackMarket:BlackMarketSave = new BlackMarketSave();
      
      public var achieve:AchieveSaveGroup = new AchieveSaveGroup();
      
      public var pet:PetSaveGroup = new PetSaveGroup();
      
      public var ask:AskSave = new AskSave();
      
      public var union:UnionSave = new UnionSave();
      
      public var drop:DropSave = new DropSave();
      
      public var active:ActiveSave = new ActiveSave();
      
      public var headCount:HeadCountSave = new HeadCountSave();
      
      public var head:HeadSave = new HeadSave();
      
      public var post:PostSave = new PostSave();
      
      public var wilder:WilderSaveGroup = new WilderSaveGroup();
      
      public var peak:PeakSave = new PeakSave();
      
      public var food:FoodSave = new FoodSave();
      
      public var bossEdit:BossEditSaveGroup = new BossEditSaveGroup();
      
      public var armsTor:ArmsTorSaveGroup = new ArmsTorSaveGroup();
      
      public var bossCard:BossCardSaveGroup = new BossCardSaveGroup();
      
      public var space:SpaceSave = new SpaceSave();
      
      public var tower:TowerSave = new TowerSave();
      
      public function PlayerSave()
      {
         super();
         count = new PlayerCountSave();
         inputNoDataNameArr = pro_arr.concat([]);
      }
      
      override public function initSave() : void
      {
         super.initSave();
         this.armsBag.unlockTo(32 - 1);
         this.equipBag.unlockTo(60 - 1);
         this.thingsBag.unlockTo(120 - 1);
         this.partsBag.unlockTo(48 - 1);
         this.geneBag.unlockTo(48 - 1);
         this.more.initSave();
         this.moreBag.initBagSave();
         this.setting.initSave();
         this.gift.initSave();
         this.union.initSave();
         this.worldMap.unlockOne("WoTu");
      }
      
      override protected function initGripMaxNum() : void
      {
         super.initGripMaxNum();
         this.armsBag.gripMaxNum = 80 + 32;
         this.armsHouse.gripMaxNum = 240 - 6 + 18 * 5;
         if(this.armsHouse.lockLen < 30)
         {
            this.armsHouse.unlockTo(30 - 1);
         }
         this.equipBag.gripMaxNum = 180;
         this.equipHouse.gripMaxNum = 240 + 48 * 3;
         if(this.equipHouse.lockLen < 30)
         {
            this.equipHouse.unlockTo(30 - 1);
         }
         this.thingsBag.gripMaxNum = 600;
         if(this.thingsBag.lockLen < 600 - 1)
         {
            this.thingsBag.unlockTo(600 - 1);
         }
         this.partsBag.gripMaxNum = 144;
         this.more.gripMaxNum = 6;
         this.moreBag.gripMaxNum = 20;
         this.geneBag.gripMaxNum = 360;
         if(this.geneBag.lockLen < 47)
         {
            this.geneBag.unlockTo(48 - 1);
         }
         base.playerCtrlB = true;
      }
      
      public function getCopyObj() : Object
      {
         var byte0:ByteArray = new ByteArray();
         byte0.writeObject(this);
         byte0.position = 0;
         return byte0.readObject();
      }
      
      private function dealThinSaveObj(obj0:Object) : Object
      {
         var n:* = undefined;
         var moreObj0:Object = null;
         var moreSa0:MoreSave = null;
         this.task.dealSaveObj(obj0["task"]);
         this.achieve.dealSaveObj(obj0["achieve"]);
         this.worldMap.dealSaveObj(obj0["worldMap"]);
         arms.dealSaveObj(obj0["arms"]);
         this.armsBag.dealSaveObj(obj0["armsBag"]);
         this.armsHouse.dealSaveObj(obj0["armsHouse"]);
         equip.dealSaveObj(obj0["equip"]);
         this.equipBag.dealSaveObj(obj0["equipBag"]);
         this.equipHouse.dealSaveObj(obj0["equipHouse"]);
         for(n in obj0.more.arr)
         {
            moreObj0 = obj0.more.arr[n];
            moreSa0 = this.more.arr[n];
            this.dealNormalPlayerSaveObj(moreObj0["SAVE"],moreSa0.SAVE);
         }
         for(n in obj0.moreBag.arr)
         {
            moreObj0 = obj0.moreBag.arr[n];
            moreSa0 = this.moreBag.arr[n];
            this.dealNormalPlayerSaveObj(moreObj0["SAVE"],moreSa0.SAVE);
         }
         return ClassProperty.copyObj(obj0);
      }
      
      private function dealNormalPlayerSaveObj(obj0:Object, pd0:NormalPlayerSave) : void
      {
         pd0.arms.dealSaveObj(obj0["arms"]);
         pd0.equip.dealSaveObj(obj0["equip"]);
      }
      
      public function getCopyObjThin() : Object
      {
         var obj0:Object = ClassProperty.copyObj(this);
         return this.dealThinSaveObj(obj0);
      }
      
      public function getCopyObjNoThin() : Object
      {
         return ClassProperty.copyObj(this);
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         var arr0:Array = pro_arr;
         this.main.inData_byObj(obj0["base"]);
         _inData_byObj(obj0,arr0);
         this.partsBag.mergeAllSameName();
      }
      
      public function getCount() : PlayerCountSave
      {
         return count as PlayerCountSave;
      }
   }
}

