package dataAll._app.outfit
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.outfit.define.OutfitDefine;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.skill.define.SkillDefine;
   
   public class OutfitDataGroup
   {
      
      public var dataArr:Array = [];
      
      public function OutfitDataGroup()
      {
         super();
      }
      
      public function initData() : void
      {
         var d0:OutfitDefine = null;
         var da0:OutfitData = null;
         this.dataArr.length = 0;
         var arr0:Array = Gaming.defineGroup.outfit.arr;
         for each(d0 in arr0)
         {
            da0 = new OutfitData();
            da0.inDataByDefine(d0);
            this.dataArr.push(da0);
         }
      }
      
      public function getDataArr() : Array
      {
         return this.dataArr;
      }
      
      public function fleshData(pd0:NormalPlayerData) : void
      {
         var da0:OutfitData = null;
         for each(da0 in this.dataArr)
         {
            OutfitDataPan.pan(da0,pd0);
         }
      }
      
      public function getSkillNameArr() : Array
      {
         var da0:OutfitData = null;
         var arr0:Array = [];
         for each(da0 in this.dataArr)
         {
            if(da0.haveB)
            {
               arr0.push(da0.define.skill);
            }
         }
         return arr0;
      }
      
      public function getBtnName() : String
      {
         var num0:int = this.getHaveNum();
         var s0:String = "套件";
         if(num0 > 0)
         {
            s0 += ComMethod.green("x" + num0);
         }
         return s0;
      }
      
      private function getHaveNum() : int
      {
         var da0:OutfitData = null;
         var num0:int = 0;
         for each(da0 in this.dataArr)
         {
            if(da0.haveB)
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function getTip() : String
      {
         var da0:OutfitData = null;
         var d0:OutfitDefine = null;
         var skillD0:SkillDefine = null;
         var str0:String = "";
         for each(da0 in this.dataArr)
         {
            if(da0.haveB)
            {
               d0 = da0.define;
               skillD0 = d0.getSkillDefine();
               str0 += "<b><green " + d0.cnName + "/></b>\n";
               str0 += "<yellow " + skillD0.cnName + "/>：";
               str0 += skillD0.getDescription() + "\n\n";
            }
         }
         return str0;
      }
   }
}

