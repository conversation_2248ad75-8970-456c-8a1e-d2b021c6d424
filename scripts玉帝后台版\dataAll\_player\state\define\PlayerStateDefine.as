package dataAll._player.state.define
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   
   public class PlayerStateDefine
   {
      
      public static var pro_arr:Array = null;
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var father:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var gamingB:Boolean = true;
      
      public var demNoneB:Boolean = false;
      
      public var demO:Boolean = false;
      
      public var skill:String = "";
      
      public var iconUrl:String = "";
      
      public function PlayerStateDefine()
      {
         super();
         this.time = 0;
      }
      
      public function get time() : Number
      {
         return this.CF.getAttribute("time");
      }
      
      public function set time(v0:Number) : void
      {
         this.CF.setAttribute("time",v0);
      }
      
      public function inData_byXML(xml0:XML, father0:String) : void
      {
         this.father = father0;
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
      
      public function getBattleNoneB() : Boolean
      {
         return this.skill == "superSpreadCard";
      }
   }
}

