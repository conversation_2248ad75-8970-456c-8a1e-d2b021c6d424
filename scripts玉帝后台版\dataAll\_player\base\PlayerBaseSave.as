package dataAll._player.base
{
   import com.common.text.TextWay;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.Sounto64;
   import com.sounto.utils.ClassProperty;
   import dataAll._player.define.KeywordShield;
   
   public class PlayerBaseSave
   {
      
      public static var pro_arr:Array = null;
      
      protected var CF:NiuBiCF = new NiuBiCF();
      
      private var _level:String = "";
      
      public var lockB:Boolean = false;
      
      public var playerName:String = "爆枪小战士";
      
      public var skillResetedNum:Number = 0;
      
      public var playerCtrlB:Boolean = false;
      
      public function PlayerBaseSave()
      {
         super();
         this.level = 1;
         this.exp = 0;
      }
      
      public function get exp() : Number
      {
         return this.CF.getAttribute("exp");
      }
      
      public function set exp(v0:Number) : void
      {
         this.CF.setAttribute("exp",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         this.playerName = TextWay.clearHtmlNo(this.playerName);
         this.playerName = KeywordShield.clearKey(this.playerName);
      }
      
      public function set level(v0:Number) : void
      {
         this._level = Sounto64.encode(String(v0));
      }
      
      public function get level() : Number
      {
         return Number(Sounto64.decode(this._level));
      }
   }
}

