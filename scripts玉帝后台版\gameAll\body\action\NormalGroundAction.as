package gameAll.body.action
{
   import com.sounto.math.Maths;
   import com.sounto.utils.ArrayMethod;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.body.img.BodyImageLabel;
   import flash.geom.Point;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.ai.Attack_AI;
   import gameAll.body.image.CarImage;
   import gameAll.body.image.NormalImage;
   import gameAll.body.motion.GroundMotionState;
   import gameAll.body.motion.NormalGroundMotion;
   
   public class NormalGroundAction implements IO_NormalBodyAction
   {
      
      private static const flipCtrlBy_:String = "flipCtrlBy_";
      
      private var BB:IO_NormalBody;
      
      public var enabled:Boolean = true;
      
      private var img:NormalImage;
      
      private var mot:NormalGroundMotion;
      
      private var def:NormalBodyDefine;
      
      public var airState:String = "jump";
      
      protected var onlyActionLabel:String = "";
      
      private var nowStandLabel:String = "stand";
      
      public var fillDirectIndex:int = 1;
      
      private var jumpDelayT:Number = 0.1;
      
      private var moveWhenVB:Boolean = false;
      
      private var haveJumpUpB:Boolean = true;
      
      private var haveFillUpB:Boolean = true;
      
      private var haveRunB:Boolean = false;
      
      private var haveMoveB:Boolean = true;
      
      private var haveIndex2B:Boolean = false;
      
      public function NormalGroundAction(_image:NormalImage, _motion:NormalGroundMotion, _BB:IO_NormalBody)
      {
         super();
         this.BB = _BB;
         this.img = _image;
         this.mot = _motion;
         this.def = this.BB.getDefine();
      }
      
      public function fleshByDefine() : void
      {
         var labelArr0:Array = this.img.labelArr;
         this.haveJumpUpB = labelArr0.indexOf("jumpUp") >= 0;
         this.haveFillUpB = labelArr0.indexOf("fill1_Up") >= 0;
         this.haveRunB = labelArr0.indexOf(BodyImageLabel.run) >= 0;
         this.haveMoveB = labelArr0.indexOf(BodyImageLabel.move) >= 0;
         this.haveIndex2B = labelArr0.indexOf("die2") >= 0;
         this.jumpDelayT = this.def.motionD.jumpDelayT;
         this.moveWhenVB = this.def.motionD.moveWhenVB;
         if(this.def.haveStandRanB())
         {
            this.nowStandLabel = ArrayMethod.getRandomOne(this.def.getStandImgArr());
         }
      }
      
      public function getStandLabel() : String
      {
         return this.nowStandLabel;
      }
      
      private function getMoveLabel() : String
      {
         if(this.haveMoveB)
         {
            return BodyImageLabel.move;
         }
         return this.getStandLabel();
      }
      
      public function setStandLabel(str0:String) : void
      {
         this.nowStandLabel = str0;
      }
      
      public function setJumpDelayT(t0:Number) : void
      {
         this.jumpDelayT = t0;
      }
      
      public function moveToLeft() : void
      {
         if(this.allowAll())
         {
            this.mot.moveToLeft();
            if(this.def.flipCtrlBy == "")
            {
               this.img.flipToLeft();
            }
            this.airState = BodyImageLabel.jump;
         }
      }
      
      public function moveToRight() : void
      {
         if(this.allowAll())
         {
            this.mot.moveToRight();
            if(this.def.flipCtrlBy == "")
            {
               this.img.flipToRight();
            }
            this.airState = BodyImageLabel.jump;
         }
      }
      
      public function toStopAndStand() : void
      {
         this.toStop();
         this.toStand();
         this.BB.getAi().extraAI.actionToStopAndStand();
      }
      
      public function toStand() : void
      {
         this.img.play(this.getStandLabel());
      }
      
      public function toStop() : void
      {
         this.mot.toStop();
      }
      
      public function fullyStop() : void
      {
         if(this.mot.isGroundB())
         {
            this.mot.Fi_vx = 0;
         }
         this.mot.toStop();
      }
      
      public function toJump(x0:Number = -100000, y0:Number = -100000, delayT0:Number = -1) : Boolean
      {
         if(this.mot.nowJumpNum < this.mot.getMaxJumpNum() && this.allowAll() && this.mot.delayT == -100 && (Boolean(this.BB.getData().playerCtrlB) || this.img.nowLabel != "jumpDown__"))
         {
            if(delayT0 == -1)
            {
               delayT0 = this.jumpDelayT;
            }
            this.mot.delayToJump(delayT0,x0,y0);
            this.airState = BodyImageLabel.jump;
            if(this.haveJumpUpB && this.baseAllowAll())
            {
               this.img.toPlay("jumpUp");
            }
            return true;
         }
         return false;
      }
      
      public function toJumpAction() : void
      {
         this.img.setToPlay("__jumpUp","jumpUp");
      }
      
      public function toRide(label0:String) : void
      {
         if(this.img.nowLabel != label0)
         {
            this.img.setOrder([label0]);
         }
      }
      
      private function flipTimer() : void
      {
         if(this.def.flipCtrlBy != "")
         {
            if(this.shootAllowAll())
            {
               this[flipCtrlBy_ + this.def.flipCtrlBy]();
            }
         }
      }
      
      private function flipCtrlBy_no() : void
      {
      }
      
      private function flipCtrlBy_target() : void
      {
         var dir0:int = 0;
         var ai0:Attack_AI = null;
         var t0:IO_NormalBody = null;
         var t_x0:int = 0;
         var t_dir0:int = 0;
         if(this.BB.getAi().extraAI.canFlipToTargetB)
         {
            dir0 = 0;
            ai0 = this.BB.getAi().attackAI;
            t0 = ai0.targetBody;
            if(t0 is IO_NormalBody && ai0.state != Attack_AI.FOLLOWING)
            {
               t_x0 = t0.getMot().x - this.mot.x;
               t_dir0 = Maths.Pn(t_x0);
               dir0 = t_dir0;
            }
            else
            {
               dir0 = this.mot.getMoveDirection();
            }
            if(dir0 == -1)
            {
               this.img.flipToLeft();
            }
            else if(dir0 == 1)
            {
               this.img.flipToRight();
            }
         }
      }
      
      private function flipCtrlBy_mouse() : void
      {
         var p0:Point = null;
         var flipB0:Boolean = false;
         if(this.shootAllowAll())
         {
            if(!(this.img is CarImage && this.mot.isFlyState()))
            {
               if(this.baseAllowAll() == false)
               {
                  return;
               }
            }
            p0 = this.BB.getAi().getMousePoint();
            if(p0 is Point)
            {
               flipB0 = false;
               if(p0.x > this.mot.x)
               {
                  flipB0 = this.img.flipToRight();
               }
               else
               {
                  flipB0 = this.img.flipToLeft();
               }
               if(flipB0)
               {
                  this.BB.setRa(-this.mot.ra);
               }
            }
         }
      }
      
      public function flipToBody(b0:IO_NormalBody) : void
      {
         if(this.mot.x < b0.getMot().x)
         {
            this.img.flipToRight();
         }
         else
         {
            this.img.flipToLeft();
         }
      }
      
      public function toHurt(fillDirectIndex0:int = 1) : void
      {
         if(!this.haveIndex2B)
         {
            fillDirectIndex0 = 1;
         }
         this.img.setOrder([BodyImageLabel.hurt + fillDirectIndex0,this.getStandLabel()]);
         if(this.mot.isGroundB())
         {
            this.mot.toStop();
         }
      }
      
      public function toDie(fillDirectIndex0:int = 1, cx0:Number = 0, vx0:Number = 0) : void
      {
         this.onlyActionLabel = "";
         this.mot.toStop();
         this.mot.state = GroundMotionState.stand;
         this.mot.e_vx = vx0 * (Math.random() * 0.6 + 0.6) * this.def.dieJumpMul;
         this.mot.toJump((Math.random() * 0.3 + 0.3) * this.def.dieJumpMul);
         if(!this.haveIndex2B)
         {
            fillDirectIndex0 = 1;
         }
         this.airState = "fill" + fillDirectIndex0 + "_";
         this.fillDirectIndex = fillDirectIndex0;
         if(this.haveFillUpB)
         {
            this.img.setToPlay("__" + this.airState + "Up",this.airState + "Up");
         }
         else
         {
            this.img.play("die" + this.fillDirectIndex);
         }
      }
      
      public function rebirth() : void
      {
         this.airState = "jump";
         this.mot.state = this.BB.getData().getMotionState();
         this.mot.e_vx = 0;
         this.mot.e_vy = 0;
         this.img.play(this.getStandLabel());
      }
      
      public function toStru() : void
      {
         this.onlyActionLabel = "";
         this.img.setToPlay("__stru","stru");
         this.mot.toStop();
         this.mot.state = GroundMotionState.stand;
      }
      
      public function setOnlyLabel(label0:String) : void
      {
         this.onlyActionLabel = label0;
         var l0:String = this.img.nowLabel;
         if(l0 != this.onlyActionLabel)
         {
            this.img.toPlay(this.onlyActionLabel);
         }
      }
      
      protected function onlyLoopPan() : void
      {
         var l0:String = this.img.nowLabel;
         if(l0 != this.onlyActionLabel)
         {
            this.img.toPlay(this.onlyActionLabel);
         }
      }
      
      protected function ActionCtrl() : void
      {
         var upOrDown:String = null;
         var moveB0:Boolean = false;
         var l0:String = this.img.nowLabel;
         var type0:String = this.img.nowLabelType;
         if(this.baseAllowAll())
         {
            upOrDown = this.mot.Fi_vy > 0 ? BodyImageLabel.Down : BodyImageLabel.Up;
            if(this.mot.Fi_vy == 0)
            {
               upOrDown = "";
            }
            if(this.mot.state == GroundMotionState.stand)
            {
               if(this.mot.dof.down == 1)
               {
                  if(!(l0 == BodyImageLabel.__jumpUp || l0.indexOf(BodyImageLabel.__fill) == 0))
                  {
                     if(this.BB.getDie() >= 1)
                     {
                        this.img.toPlay(BodyImageLabel.die + this.fillDirectIndex,true);
                     }
                     else
                     {
                        moveB0 = this.mot.fi == 0;
                        if(this.moveWhenVB)
                        {
                           moveB0 = this.moveWhenVB && this.mot.vx == 0;
                        }
                        if(moveB0)
                        {
                           this.img.toPlay(this.getStandLabel(),true);
                        }
                        else if(Math.abs(this.mot.Fi_vx) > this.def.runStartVx && this.haveRunB)
                        {
                           this.img.toPlay(BodyImageLabel.run,true);
                        }
                        else
                        {
                           this.img.toPlay(this.getMoveLabel(),true);
                        }
                     }
                  }
               }
               else if(!this.mot.isGroundB() && upOrDown != "")
               {
                  this.img.toPlay(this.airState + upOrDown,true);
               }
            }
            else
            {
               if(this.def.flipCtrlBy == "" || this.def.flipCtrlBy == "target")
               {
                  if(this.mot.Fi_vx > 1)
                  {
                     this.img.flipToRight();
                  }
                  else if(this.mot.Fi_vx < -1)
                  {
                     this.img.flipToLeft();
                  }
               }
               if(this.haveJumpUpB)
               {
                  if(upOrDown != "")
                  {
                     this.img.play(this.airState + upOrDown,true);
                  }
               }
               else
               {
                  this.img.toPlay(this.mot.Fi_vx != 0 || this.mot.Fi_vy != 0 ? this.getMoveLabel() : this.getStandLabel(),true);
               }
            }
         }
      }
      
      public function allowAll() : Boolean
      {
         var l0:String = this.img.nowLabelType;
         return BodyImageLabel.normalAllowArr.indexOf(l0) == -1;
      }
      
      public function shootAllowAll() : Boolean
      {
         var l0:String = this.img.nowLabelType;
         return BodyImageLabel.normalAllowArr.indexOf(l0) == -1;
      }
      
      protected function baseAllowAll() : Boolean
      {
         var l0:String = this.img.nowLabelType;
         return BodyImageLabel.baseAllowArr.indexOf(l0) == -1;
      }
      
      protected function haveNowAirStateB() : Boolean
      {
         if(this.airState == BodyImageLabel.jump)
         {
            return this.haveJumpUpB;
         }
         return this.haveFillUpB;
      }
      
      public function actionTimer() : void
      {
         if(this.enabled)
         {
            if(this.onlyActionLabel == "")
            {
               this.ActionCtrl();
            }
            else
            {
               this.onlyLoopPan();
            }
            this.flipTimer();
         }
      }
   }
}

