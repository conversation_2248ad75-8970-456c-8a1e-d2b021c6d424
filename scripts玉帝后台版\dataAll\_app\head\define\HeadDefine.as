package dataAll._app.head.define
{
   import com.adobe.serialization.json.JSON2;
   import com.common.data.Base64;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   import dataAll.gift.define.IO_GiftDefine;
   
   public class HeadDefine implements IO_GiftDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var father:String = "";
      
      public var iconUrl:String = "";
      
      public var iconUrl48:String = "";
      
      public var cnName:String = "";
      
      public var unlockLv:int = 0;
      
      public var diff:Number = 0;
      
      public var life:int = 0;
      
      public var noEquip:Boolean = false;
      
      private var _addObjJson:String = "";
      
      public var description:String = "";
      
      public var condition:HeadConditionDefine = new HeadConditionDefine();
      
      public function HeadDefine()
      {
         super();
         this.addObjJson = "";
      }
      
      public function set addObjJson(v0:String) : void
      {
         this._addObjJson = Base64.encodeString(String(v0));
      }
      
      public function get addObjJson() : String
      {
         return Base64.decodeString(this._addObjJson);
      }
      
      public function inData_byXML(xml0:XML, father0:String) : void
      {
         if(!xml0)
         {
            return;
         }
         ClassProperty.inData_byXML(this,xml0,pro_arr);
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         this.father = father0;
         this.condition.inData_byXML(xml0.condition[0]);
         if(this.condition.fun == "")
         {
            this.condition.fun = this.name;
         }
         if(this.condition.pro == "")
         {
            this.condition.pro = this.name;
         }
         if(this.iconUrl == "")
         {
            this.iconUrl = "IconGather/" + this.name;
         }
      }
      
      public function getIconUrl48() : String
      {
         if(this.iconUrl48 == "")
         {
            return this.iconUrl;
         }
         return this.iconUrl48;
      }
      
      public function getAddObj() : Object
      {
         var str0:String = null;
         var json0:String = this.addObjJson;
         if(json0 != "")
         {
            str0 = TextWay.replaceStr(json0,"\'","\"");
            return JSON2.decode(str0);
         }
         return {};
      }
      
      public function haveLifeB() : Boolean
      {
         return this.life > 0;
      }
      
      public function getHonorValue() : int
      {
         return this.diff * 2;
      }
      
      public function getUnlockStr(heroLv0:int) : String
      {
         var str0:String = null;
         var lv0:int = this.unlockLv;
         if(lv0 > 0)
         {
            str0 = this.unlockLv + "级";
            if(heroLv0 < lv0)
            {
               str0 += ComMethod.noEnough("（不足）");
            }
            return str0;
         }
         return "无";
      }
      
      public function getGatherTip() : String
      {
         var str0:String = "";
         if(this.noEquip)
         {
            str0 += "<purple 无需装备也能获得属性加成。/>\n";
         }
         str0 += "<i1>|<blue <b>属性：</b>/>";
         str0 += "\n寿命|<yellow " + (this.haveLifeB() ? this.life + "天" : "永久") + "/>";
         str0 += "\n解锁等级|<yellow " + this.getUnlockStr(999) + "/>";
         str0 += "\n荣誉值|<yellow " + this.getHonorValue() + "/>";
         str0 += "\n\n<i1>|<blue <b>提升：</b>/>";
         return str0 + ("\n" + EquipPropertyDataCreator.getText_byObj(this.getAddObj()));
      }
      
      public function getGiftCn() : String
      {
         return this.cnName;
      }
      
      public function getGiftTip() : String
      {
         return this.getGatherTip();
      }
      
      public function getGiftIconUrl() : String
      {
         return this.getIconUrl48();
      }
   }
}

