package dataAll.items
{
   import UI.bag.ItemsGripBtnListCtrl;
   import UI.bag.ItemsResolveCtrl;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.parts.PartsMethod;
   import dataAll._player.PlayerData;
   import dataAll._player.io.IO_HavePlayerData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.equip.EquipData;
   import dataAll.items.creator.ItemsRefiningCtrl;
   import dataAll.items.save.ItemsSave;
   import dataAll.items.save.ItemsSaveGroup;
   import dataAll.ui.tip.CheckData;
   
   public class ItemsDataGroup implements IO_HavePlayerData
   {
      
      public static const TYPE_ARMS:String = "arms";
      
      public static const TYPE_EQUIP:String = "equip";
      
      public static const TYPE_THINGS:String = "things";
      
      public static const TYPE_PARTS:String = "parts";
      
      public static const TYPE_SKILL:String = "skill";
      
      public static const TYPE_MORE:String = "more";
      
      public static const TYPE_GENE:String = "gene";
      
      public static const PLACE_WEAR:String = "wear";
      
      public static const PLACE_BAG:String = "bag";
      
      public static const PLACE_HOUSE:String = "house";
      
      public static const PLACE_OTHER:String = "other";
      
      private static const cnNameObj:Object = {
         "arms":"武器",
         "equip":"装备",
         "things":"物品",
         "parts":"零件",
         "skill":"技能",
         "more":"队友",
         "gene":"基因体",
         "bossCard":"魂卡"
      };
      
      private static const placeCnNameObj:Object = {
         "wear":"装备栏",
         "bag":"背包",
         "house":"仓库"
      };
      
      public var dataType:String = "";
      
      public var placeType:String = "wear";
      
      public var dataArr:Array = [];
      
      protected var _siteDataArr:Array = null;
      
      public var playerData:PlayerData;
      
      public var normalPlayerData:NormalPlayerData;
      
      public function ItemsDataGroup()
      {
         super();
      }
      
      public static function sortByTempSortIdFun(da1:IO_ItemsData, da2:IO_ItemsData) : int
      {
         var id1:String = da1.getTempSortId();
         var id2:String = da2.getTempSortId();
         if(id1 < id2)
         {
            return -1;
         }
         if(id1 > id2)
         {
            return 1;
         }
         return 0;
      }
      
      public static function getCnNameBy(name0:String) : String
      {
         return cnNameObj[name0];
      }
      
      public static function getPlaceCnNameBy(name0:String) : String
      {
         return placeCnNameObj[name0];
      }
      
      public static function swapTo(dg1:ItemsDataGroup, dg2:ItemsDataGroup, site1:int, site2:int, wearCompareLevel:int = 9999, numSwapB0:Boolean = false) : CheckData
      {
         var m_da0:IO_ItemsData = null;
         var check0:CheckData = new CheckData();
         var lockB1:Boolean = dg1.getSaveGroup().getUnlockBySite(site1);
         var lockB2:Boolean = dg2.getSaveGroup().getUnlockBySite(site2);
         if(!lockB1 || !lockB2)
         {
            check0.bb = true;
            return check0;
         }
         var da1:IO_ItemsData = dg1.getDataBySite(site1);
         var da2:IO_ItemsData = dg2.getDataBySite(site2);
         if(dg1 != dg2 && wearCompareLevel < 999)
         {
            if(dg1.placeType == PLACE_WEAR && Boolean(da2))
            {
               m_da0 = da2;
            }
            else if(dg2.placeType == PLACE_WEAR && Boolean(da1))
            {
               m_da0 = da1;
            }
            if(Boolean(m_da0))
            {
               if(m_da0.getWearLevel() > wearCompareLevel)
               {
                  check0.bb = false;
                  check0.info = "物品等级超过" + wearCompareLevel + "级，无法装备。";
                  return check0;
               }
            }
         }
         var canNumSwapB0:Boolean = false;
         if(numSwapB0 && dg1.placeType != dg2.placeType && Boolean(da1.isCanNumSwapB()))
         {
            if(Boolean(da2))
            {
               canNumSwapB0 = Boolean(da2.isCanNumSwapB());
            }
            else
            {
               canNumSwapB0 = true;
            }
         }
         if(canNumSwapB0)
         {
            check0 = numSwapTo(dg1,dg2,site1,site2);
            if(!check0.bb)
            {
               return check0;
            }
         }
         else
         {
            if(Boolean(da1))
            {
               da1.getSave().site = site2;
               dg1.removeDataNoFleshSave(da1);
               dg2.swap_addData(da1);
            }
            if(Boolean(da2))
            {
               da2.getSave().site = site1;
               dg2.removeDataNoFleshSave(da2);
               dg1.swap_addData(da2);
            }
         }
         dg1.fleshSaveGroup();
         if(dg1 != dg2)
         {
            dg2.fleshSaveGroup();
         }
         dg1._siteDataArr = null;
         dg2._siteDataArr = null;
         return check0;
      }
      
      private static function numSwapTo(dg1:ItemsDataGroup, dg2:ItemsDataGroup, site1:int, site2:int) : CheckData
      {
         var sameDa0:IO_ItemsData = null;
         var bagNew_da:IO_ItemsData = null;
         var check0:CheckData = new CheckData();
         var da1:IO_ItemsData = dg1.getDataBySite(site1);
         var da2:IO_ItemsData = dg2.getDataBySite(site2);
         var wearDg:ItemsDataGroup = dg1;
         var bagDg:ItemsDataGroup = dg2;
         var wear_site:int = site1;
         var bag_site:int = site2;
         if(dg1.placeType == PLACE_BAG)
         {
            wearDg = dg2;
            bagDg = dg1;
            wear_site = site2;
            bag_site = site1;
         }
         var wear_da:IO_ItemsData = wearDg.getDataBySite(wear_site);
         var bag_da:IO_ItemsData = bagDg.getDataBySite(bag_site);
         if(Boolean(wear_da))
         {
            sameDa0 = bagDg.getDataBySaveName(wear_da.getSave().name);
         }
         var OverlayB:Boolean = false;
         if(sameDa0 is IO_ItemsData)
         {
            if(sameDa0.isCanOverlayB())
            {
               OverlayB = true;
            }
         }
         if(Boolean(wear_da) && Boolean(bag_da))
         {
            if(wear_da.getSave().name == bag_da.getSave().name)
            {
               if(dg1.placeType == PLACE_WEAR)
               {
                  wearDg.removeDataNoFleshSave(wear_da);
                  bag_da.addNowNum(wear_da.getNowNum(),wear_da);
               }
               else
               {
                  if(wearDg.placeType != PLACE_HOUSE)
                  {
                     check0.bb = false;
                     check0.name = "sameParts";
                     return check0;
                  }
                  dg1.removeDataNoFleshSave(da1);
                  da2.addNowNum(da1.getNowNum(),da1);
               }
            }
            else
            {
               if(bag_da.getNowNum() > 1 && !OverlayB && bagDg.getSpaceSiteNum() == 0)
               {
                  check0.bb = false;
                  check0.name = SwapFail.bagFill;
                  check0.info = "背包已满！";
                  return check0;
               }
               if(bag_da.getNowNum() > 1 && wearDg.placeType != PLACE_HOUSE)
               {
                  bag_da.addNowNum(-1);
                  bagNew_da = bag_da.normalClone();
                  bagNew_da.setNowNum(1);
                  bagNew_da.getSave().site = wear_site;
                  wearDg.swap_addData(bagNew_da);
               }
               else
               {
                  bag_da.getSave().site = wear_site;
                  bagDg.removeDataNoFleshSave(bag_da);
                  wearDg.swap_addData(bag_da);
               }
               wearDg.removeDataNoFleshSave(wear_da);
               if(!OverlayB)
               {
                  wear_da.getSave().site = bagDg.spaceSiteOf();
                  bagDg.swap_addData(wear_da);
               }
               else
               {
                  sameDa0.addNowNum(wear_da.getNowNum(),wear_da);
               }
            }
         }
         else if(Boolean(wear_da))
         {
            wearDg.removeDataNoFleshSave(wear_da);
            if(OverlayB)
            {
               sameDa0.addNowNum(wear_da.getNowNum(),wear_da);
            }
            else
            {
               wear_da.getSave().site = bag_site;
               bagDg.swap_addData(wear_da);
            }
         }
         else if(Boolean(bag_da))
         {
            if(bag_da.getNowNum() > 1 && wearDg.placeType != PLACE_HOUSE)
            {
               bag_da.addNowNum(-1);
               bagNew_da = bag_da.normalClone();
               bagNew_da.setNowNum(1);
               bagNew_da.getSave().site = wear_site;
               wearDg.swap_addData(bagNew_da);
            }
            else if(wearDg.placeType == PLACE_HOUSE)
            {
               bagDg.removeDataNoFleshSave(bag_da);
               wearDg.swapAddDataSpace(bag_da,wear_site);
            }
            else
            {
               bag_da.getSave().site = wear_site;
               bagDg.removeDataNoFleshSave(bag_da);
               wearDg.swap_addData(bag_da);
            }
         }
         return check0;
      }
      
      public function testPlayerData(pd0:NormalPlayerData) : void
      {
         var da0:IO_ItemsData = null;
         if(this.normalPlayerData != pd0)
         {
            INIT.TRACE(this.dataType + "  " + this.getPDCn(this.normalPlayerData) + "  " + this.getPDCn(pd0) + " 类型不匹配");
         }
         for each(da0 in this.dataArr)
         {
            if(da0.getPlayerData() != pd0)
            {
               INIT.TRACE(this.dataType + "   " + da0 + "  " + this.getPDCn(this.normalPlayerData) + "  " + this.getPDCn(pd0) + " 类型不匹配");
            }
            if(this.getSaveGroup().arr.indexOf(da0.getSave()) == -1)
            {
               INIT.TRACE(this.dataType + "  " + this.getPDCn(this.normalPlayerData) + "  " + this.getPDCn(pd0) + " save不在数组中");
            }
         }
      }
      
      private function getPDCn(pd0:NormalPlayerData) : String
      {
         var s0:String = "";
         if(Boolean(pd0))
         {
            if(pd0 is PlayerData)
            {
               s0 += "【p1】";
            }
            s0 += pd0.getCnName();
         }
         else
         {
            s0 += "null";
         }
         return s0;
      }
      
      public function setPlayerData(pd0:NormalPlayerData) : void
      {
         this.normalPlayerData = pd0;
         this.playerData = this.normalPlayerData as PlayerData;
      }
      
      public function setAllPlayerData(pd0:NormalPlayerData) : void
      {
         var n:* = undefined;
         var da0:IO_ItemsData = null;
         this.setPlayerData(pd0);
         for(n in this.dataArr)
         {
            da0 = this.dataArr[n];
            da0.setPlayerData(pd0);
         }
      }
      
      public function getMainPlayerData() : PlayerData
      {
         if(Boolean(this.normalPlayerData))
         {
            return this.normalPlayerData.getMainPlayerData();
         }
         return null;
      }
      
      public function checkNormalPlayerData(pd0:NormalPlayerData) : void
      {
         var n:* = undefined;
         var da0:Object = null;
         for(n in this.dataArr)
         {
            da0 = this.dataArr[n];
            if(da0.hasOwnProperty("checkNormalPlayerData"))
            {
               da0["checkNormalPlayerData"](pd0);
            }
            else if(da0.hasOwnProperty("normalPlayerData"))
            {
               if(pd0 != da0["normalPlayerData"])
               {
               }
            }
         }
      }
      
      public function newDayCtrl(timeStr0:String) : void
      {
         var da0:IO_ItemsData = null;
         for each(da0 in this.dataArr)
         {
         }
      }
      
      public function inData_bySaveGroup(sg0:ItemsSaveGroup) : void
      {
      }
      
      protected function fleshSaveGroup() : void
      {
         var n:* = undefined;
         var da0:IO_ItemsData = null;
         var save0:ItemsSave = null;
         var arr0:Array = [];
         for(var _loc7_ in this.dataArr)
         {
            n = _loc7_;
            _loc7_;
            da0 = this.dataArr[n];
            save0 = da0.getSave();
            arr0.push(save0);
         }
         this.getSaveGroup().arr = arr0;
      }
      
      public function inCloneData(dg0:ItemsDataGroup) : void
      {
         this.dataType = dg0.dataType;
         this.placeType = dg0.placeType;
         this.playerData = dg0.playerData;
         this.normalPlayerData = dg0.normalPlayerData;
         this._siteDataArr = null;
      }
      
      public function replaceData(m0:IO_ItemsData, new0:IO_ItemsData) : Boolean
      {
         var i0:int = int(this.dataArr.indexOf(m0));
         if(i0 >= 0)
         {
            this.dataArr[i0] = new0;
            new0.setPlaceType;
            this.getSaveGroup().replaceSave(m0.getSave(),new0.getSave());
            this._siteDataArr = null;
            return true;
         }
         return false;
      }
      
      public function clearData() : void
      {
         this.dataArr.length = 0;
         this._siteDataArr = null;
         if(Boolean(this.getSaveGroup()))
         {
            this.fleshSaveGroup();
         }
      }
      
      public function addSave(s0:ItemsSave, fleshSaveGroupB0:Boolean = true) : IO_ItemsData
      {
         INIT.showErrorMust("该方法必须被覆盖");
         return null;
      }
      
      public function addSavePanRepeat(s0:ItemsSave) : void
      {
         var sameDa0:IO_ItemsData = this.findDataBySave(s0);
         if(Boolean(sameDa0))
         {
            INIT.TRACE("出现重复save，不添加");
         }
         else
         {
            this.addSave(s0);
         }
      }
      
      public function addNewSaveArr(arr0:Array) : void
      {
         var arr_len0:int = int(arr0.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            this.addSave(arr0[i],false);
         }
         this.fleshSaveGroup();
      }
      
      public function addData(da0:IO_ItemsData, fleshSaveGroupB0:Boolean = true) : void
      {
         var save0:ItemsSave = null;
         var site0:int = this.spaceSiteOf();
         if(site0 >= 0)
         {
            da0.setPlaceType(this.placeType);
            da0.newB = true;
            save0 = da0.getSave();
            save0.site = site0;
            save0.setGetTime(StringDate.getLocalTimeStr());
            if(save0.getSeverTime() == "")
            {
               save0.setSeverTime(Gaming.api.save.getNowServerDate().getStr());
            }
            this.setID(da0);
            this.addDataInArr(da0);
            this._siteDataArr = null;
            if(fleshSaveGroupB0)
            {
               this.fleshSaveGroup();
            }
         }
      }
      
      protected function addDataInArr(da0:IO_ItemsData) : void
      {
         this.dataArr.push(da0);
         da0.setFatherData(this);
      }
      
      protected function setIDIfNo(da0:IO_ItemsData) : void
      {
         var save0:ItemsSave = da0.getSave();
         if(save0.id == "")
         {
            this.setID(da0);
         }
      }
      
      protected function setID(da0:IO_ItemsData) : void
      {
         var save0:ItemsSave = da0.getSave();
         save0.id = this.getID(da0);
         this.getSaveGroup().lastId++;
      }
      
      protected function getID(da0:IO_ItemsData) : String
      {
         return ItemsID.getID2(da0,this.getSaveGroup().lastId);
      }
      
      private function removeDataNoFleshSave(da0:IO_ItemsData) : void
      {
         var index0:int = int(this.dataArr.indexOf(da0));
         if(index0 != -1)
         {
            da0.setFatherData(null);
            this.dataArr.splice(index0,1);
            this._siteDataArr = null;
         }
      }
      
      public function removeName(name0:String) : void
      {
         var da0:IO_ItemsData = this.getDataBySaveName(name0);
         if(Boolean(da0))
         {
            this.removeData(da0);
         }
      }
      
      public function removeNameArr(nameArr0:Array) : void
      {
         var name0:String = null;
         for each(var _loc5_ in nameArr0)
         {
            name0 = _loc5_;
            _loc5_;
            this.removeName(name0);
         }
      }
      
      public function removeAllKeepNameArr(nameArr0:Array) : void
      {
         var da0:IO_ItemsData = null;
         var delArr0:Array = [];
         for each(var _loc6_ in this.dataArr)
         {
            da0 = _loc6_;
            _loc6_;
            if(nameArr0.indexOf(da0.getSave().name) == -1)
            {
               delArr0.push(da0);
            }
         }
         this.removeDataArr(delArr0);
      }
      
      public function removeData(da0:IO_ItemsData) : void
      {
         this.removeDataNoFleshSave(da0);
         this.fleshSaveGroup();
      }
      
      public function removeDataByNum(da0:IO_ItemsData, num0:int) : void
      {
         var now0:int = int(da0.getNowNum());
         if(now0 > num0)
         {
            da0.addNowNum(-num0);
         }
         else if(now0 == num0)
         {
            this.removeData(da0);
         }
      }
      
      public function removeDataArr(arr0:Array) : void
      {
         var n:* = undefined;
         var da0:IO_ItemsData = null;
         var new_arr0:Array = [];
         for(var _loc7_ in this.dataArr)
         {
            n = _loc7_;
            _loc7_;
            da0 = this.dataArr[n];
            if(arr0.indexOf(da0) == -1)
            {
               new_arr0.push(da0);
            }
         }
         this.dataArr = new_arr0;
         this._siteDataArr = null;
         this.fleshSaveGroup();
      }
      
      public function noInArrPan(arr0:Array) : Number
      {
         var da0:IO_ItemsData = null;
         var num0:int = 0;
         for each(var _loc6_ in arr0)
         {
            da0 = _loc6_;
            _loc6_;
            if(this.dataArr.indexOf(da0) == -1)
            {
               num0++;
            }
         }
         return num0;
      }
      
      public function getDataBySite(site0:int) : IO_ItemsData
      {
         var n:* = undefined;
         var da0:IO_ItemsData = null;
         for(var _loc6_ in this.dataArr)
         {
            n = _loc6_;
            _loc6_;
            da0 = this.dataArr[n];
            if(da0.getSave().site == site0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getDataBySaveName(name0:String) : IO_ItemsData
      {
         var n:* = undefined;
         var da0:IO_ItemsData = null;
         for(var _loc6_ in this.dataArr)
         {
            n = _loc6_;
            _loc6_;
            da0 = this.dataArr[n];
            if(da0.getSave().name == name0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function haveData(da0:IO_ItemsData) : Boolean
      {
         return this.dataArr.indexOf(da0) >= 0;
      }
      
      public function getArrByChildType(type0:String) : Array
      {
         var da0:IO_ItemsData = null;
         var arr0:Array = [];
         for each(var _loc6_ in this.dataArr)
         {
            da0 = _loc6_;
            _loc6_;
            if(da0.getSave().getChildType() == type0)
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      public function findDataBySave(s0:ItemsSave) : IO_ItemsData
      {
         var da0:IO_ItemsData = null;
         for each(var _loc5_ in this.dataArr)
         {
            da0 = _loc5_;
            _loc5_;
            if(da0.getSave() == s0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getSameSaveNum(s0:ItemsSave) : int
      {
         var da0:IO_ItemsData = null;
         var num0:int = 0;
         for each(var _loc6_ in this.dataArr)
         {
            da0 = _loc6_;
            _loc6_;
            if(da0.getSave() == s0)
            {
               num0++;
            }
         }
         return null;
      }
      
      public function sortByTime() : void
      {
         var da0:IO_ItemsData = null;
         for each(var _loc4_ in this.dataArr)
         {
            da0 = _loc4_;
            _loc4_;
            da0.setTempSortId(da0.getSave().getGetTime());
         }
         this.sortByTempSortId();
      }
      
      protected function sortByTempSortId(str0:String = "") : void
      {
         var n:* = undefined;
         var da0:IO_ItemsData = null;
         var da2:IO_ItemsData = null;
         var arr0:Array = this.dataArr.concat([]);
         if(str0 != "")
         {
            for each(var _loc8_ in arr0)
            {
               da0 = _loc8_;
               _loc8_;
               da0.toOneSortId(str0);
            }
         }
         arr0.sort(sortByTempSortIdFun);
         for(_loc8_ in arr0)
         {
            n = _loc8_;
            _loc8_;
            da2 = arr0[n];
            da2.getSave().site = n;
         }
         this._siteDataArr = null;
      }
      
      public function getSiteDataArray() : Array
      {
         var arr0:Array = null;
         if(!this._siteDataArr)
         {
            arr0 = this.dataArr.concat([]);
            arr0.sort(this.siteSortFun);
            this._siteDataArr = arr0;
         }
         return this._siteDataArr;
      }
      
      protected function siteSortFun(a0:IO_ItemsData, b0:IO_ItemsData) : int
      {
         var s0:int = int(a0.getSave().site);
         var s1:int = int(b0.getSave().site);
         if(s0 < s1)
         {
            return -1;
         }
         if(s0 > s1)
         {
            return 1;
         }
         return 0;
      }
      
      public function getSortBtnText() : String
      {
         return "排序";
      }
      
      public function getSortBtnTip() : String
      {
         return "";
      }
      
      public function getSortBtnText2() : String
      {
         return "排序";
      }
      
      public function getSortBtnTip2() : String
      {
         return "";
      }
      
      public function getBatchSellDataArr(colorArr0:Array) : Array
      {
         var da0:IO_ItemsData = null;
         var bb0:Boolean = false;
         var sell_arr0:Array = [];
         for each(var _loc7_ in this.dataArr)
         {
            da0 = _loc7_;
            _loc7_;
            if(!da0.getSave().getLockB())
            {
               bb0 = ItemsBatchColor.dataPanColorArr(da0.getSave(),colorArr0);
               if(bb0)
               {
                  sell_arr0.push(da0);
               }
            }
         }
         return sell_arr0;
      }
      
      public function getBatchSellTextTip() : String
      {
         return "";
      }
      
      public function getComposeTextTip() : String
      {
         return "";
      }
      
      public function getCnName() : String
      {
         return cnNameObj[this.dataType];
      }
      
      public function getPlaceCnName() : String
      {
         return placeCnNameObj[this.dataType];
      }
      
      public function sort(dg0:ItemsDataGroup) : void
      {
         this.getSaveGroup().sort();
         this._siteDataArr = null;
      }
      
      public function sort2(dg0:ItemsDataGroup) : void
      {
      }
      
      public function getMaxItemsLevel() : int
      {
         var da0:IO_ItemsData = null;
         var lv0:int = 0;
         var max0:int = 0;
         for each(var _loc6_ in this.dataArr)
         {
            da0 = _loc6_;
            _loc6_;
            lv0 = int(da0.getSave().getTrueLevel());
            if(lv0 > max0)
            {
               max0 = lv0;
            }
         }
         return lv0;
      }
      
      public function spaceSiteOf() : int
      {
         var n:* = undefined;
         var before_site0:int = 0;
         var i:* = undefined;
         var da0:IO_ItemsData = null;
         var s0:ItemsSave = null;
         var now_site0:int = 0;
         var lockLen0:int = this.getSaveGroup().getCanFillLen();
         if(this.dataArr.length >= lockLen0)
         {
            return -1;
         }
         if(this.dataArr.length == 0)
         {
            return 0;
         }
         var haveSiteArr0:Array = [];
         for(var _loc11_ in this.dataArr)
         {
            n = _loc11_;
            _loc11_;
            da0 = this.dataArr[n];
            s0 = da0.getSave();
            haveSiteArr0.push(s0.site);
         }
         ComMethod.sortNumberArray(haveSiteArr0);
         before_site0 = -1;
         for(_loc11_ in haveSiteArr0)
         {
            i = _loc11_;
            _loc11_;
            now_site0 = int(haveSiteArr0[i]);
            if(now_site0 - before_site0 > 1)
            {
               return before_site0 + 1;
            }
            if(i == haveSiteArr0.length - 1)
            {
               if(i + 1 <= lockLen0 - 1)
               {
                  return i + 1;
               }
            }
            before_site0 = now_site0;
         }
         return -1;
      }
      
      public function getSpaceSiteNum() : int
      {
         return this.getSaveGroup().getCanFillLen() - this.dataArr.length;
      }
      
      public function swapByOther(dg2:ItemsDataGroup, site1:int, site2:int) : void
      {
         swapTo(this,dg2,site1,site2);
      }
      
      private function swapAddDataSpace(da1:IO_ItemsData, site0:int) : void
      {
         var sameDa0:IO_ItemsData = null;
         if(da1.isCanOverlayB())
         {
            sameDa0 = this.getDataBySaveName(da1.getSave().name);
            if(Boolean(sameDa0))
            {
               sameDa0.addNowNum(da1.getNowNum(),da1);
            }
            else
            {
               da1.getSave().site = site0;
               this.swap_addData(da1);
            }
         }
         else
         {
            da1.getSave().site = site0;
            this.swap_addData(da1);
         }
      }
      
      public function swap_addData(da1:IO_ItemsData) : void
      {
         da1.setPlaceType(this.placeType);
         da1.setPlayerData(this.normalPlayerData);
         this.addDataInArr(da1);
      }
      
      public function getAllMustArenaStampNum() : Number
      {
         var da0:IO_ItemsData = null;
         var name0:String = null;
         var goodDefine0:GoodsDefine = null;
         var num0:Number = 0;
         for each(var _loc7_ in this.dataArr)
         {
            da0 = _loc7_;
            _loc7_;
            name0 = da0.getSave().name;
            if(da0 is EquipData)
            {
               name0 = (da0 as EquipData).save.imgName;
            }
            goodDefine0 = Gaming.defineGroup.goods.getDefine(name0);
            if(goodDefine0 is GoodsDefine)
            {
               if(goodDefine0.father == "arena" && goodDefine0.noOtherPathB)
               {
                  num0 = num0 + goodDefine0.price;
               }
            }
         }
         return num0;
      }
      
      public function findArenaGiftItemsData(str0:String) : IO_ItemsData
      {
         var da0:IO_ItemsData = null;
         var name0:String = null;
         for each(var _loc6_ in this.dataArr)
         {
            da0 = _loc6_;
            _loc6_;
            name0 = da0.getSave().name;
            if(da0 is EquipData)
            {
               name0 = (da0 as EquipData).save.imgName;
            }
            if(name0 == str0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getMaxUseTimeData() : IO_ItemsData
      {
         return this.dataArr[0];
      }
      
      public function getChosenArr(mustUnlockB0:Boolean = false) : Array
      {
         var da0:IO_ItemsData = null;
         var lockPanB0:Boolean = false;
         var arr0:Array = [];
         for each(var _loc7_ in this.dataArr)
         {
            da0 = _loc7_;
            _loc7_;
            lockPanB0 = true;
            if(mustUnlockB0)
            {
               if(da0.getSave().getLockB())
               {
                  lockPanB0 = false;
               }
               else if(da0.getSave().isImportantB())
               {
                  lockPanB0 = false;
               }
            }
            if(Boolean(da0.isChosen) && lockPanB0)
            {
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      public function setAllChosen(bb0:Boolean) : void
      {
         var da0:IO_ItemsData = null;
         for each(var _loc5_ in this.dataArr)
         {
            da0 = _loc5_;
            _loc5_;
            da0.isChosen = bb0;
         }
      }
      
      public function getChosenCtrlOrderArr() : Array
      {
         var arr0:Array = [];
         arr0.push(ItemsMoreOrder.lock);
         arr0.push(ItemsMoreOrder.unlock);
         return arr0;
      }
      
      public function getChosenChooseOrderArr() : Array
      {
         return [];
      }
      
      public function doChosenOrder(name0:String) : Boolean
      {
         return this[name0 + "ChosenOrder"]();
      }
      
      public function getChosenOrderTip(name0:String) : String
      {
         if(ItemsMoreOrder.tipArr.indexOf(name0) >= 0)
         {
            return this[name0 + "ChosenTip"]();
         }
         return "";
      }
      
      protected function allChooseChosenOrder() : Boolean
      {
         var da0:IO_ItemsData = null;
         for each(var _loc4_ in this.dataArr)
         {
            da0 = _loc4_;
            _loc4_;
            da0.isChosen = true;
         }
         return true;
      }
      
      protected function invertChooseChosenOrder() : Boolean
      {
         var da0:IO_ItemsData = null;
         for each(var _loc4_ in this.dataArr)
         {
            da0 = _loc4_;
            _loc4_;
            da0.isChosen = !da0.isChosen;
         }
         return true;
      }
      
      protected function unlockChooseChosenOrder() : Boolean
      {
         var da0:IO_ItemsData = null;
         for each(var _loc4_ in this.dataArr)
         {
            da0 = _loc4_;
            _loc4_;
            da0.isChosen = !da0.getSave().getLockB();
         }
         return true;
      }
      
      protected function lockChosenOrder() : Boolean
      {
         var da0:IO_ItemsData = null;
         for each(var _loc4_ in this.dataArr)
         {
            da0 = _loc4_;
            _loc4_;
            if(da0.isChosen)
            {
               da0.getSave().setLockB(true);
            }
         }
         return true;
      }
      
      protected function unlockChosenOrder() : Boolean
      {
         var da0:IO_ItemsData = null;
         for each(var _loc4_ in this.dataArr)
         {
            da0 = _loc4_;
            _loc4_;
            if(da0.isChosen)
            {
               da0.getSave().setLockB(false);
            }
         }
         return true;
      }
      
      protected function sellChosenTip() : String
      {
         var num0:int = int(this.getChosenArr(true).length);
         return "当前选择的物品中，有" + ComMethod.color(num0 + "个","#00FF00") + "可被出售，\n要继续吗？";
      }
      
      protected function sellChosenOrder() : Boolean
      {
         var arr0:Array = this.getChosenArr(true);
         ItemsGripBtnListCtrl.batchSell(arr0,this);
         return true;
      }
      
      protected function decomposeChosenTip() : String
      {
         var num0:int = int(this.getChosenArr(true).length);
         return "当前选择的物品中，有" + ComMethod.color(num0 + "个","#00FF00") + "可被拆解成零件，\n要继续吗？";
      }
      
      protected function decomposeChosenOrder() : Boolean
      {
         var arr0:Array = this.getChosenArr(true);
         PartsMethod.batchDecompose(this,this.playerData.partsBag,null,arr0);
         return true;
      }
      
      protected function getRefiningChosenArr() : Array
      {
         var da0:IO_ItemsData = null;
         var bb0:Boolean = false;
         var armsDa0:ArmsData = null;
         var arr0:Array = [];
         for each(var _loc7_ in this.dataArr)
         {
            da0 = _loc7_;
            _loc7_;
            if(Boolean(da0.isChosen) && !da0.getSave().getLockB())
            {
               if(da0.canRefiningB())
               {
                  bb0 = true;
                  armsDa0 = da0 as ArmsData;
                  if(Boolean(armsDa0))
                  {
                     if(armsDa0.havePartsB())
                     {
                        bb0 = false;
                     }
                  }
                  if(bb0)
                  {
                     arr0.push(da0);
                  }
               }
            }
         }
         return arr0;
      }
      
      protected function refiningChosenTip() : String
      {
         var num0:int = int(this.getRefiningChosenArr().length);
         return "当前选择的物品中，有" + ComMethod.color(num0 + "个","#00FF00") + "可被分解，\n要继续吗？";
      }
      
      protected function refiningChosenOrder() : Boolean
      {
         var arr0:Array = this.getRefiningChosenArr();
         ItemsRefiningCtrl.refiningDataArr(arr0,this);
         return true;
      }
      
      protected function resolveChosenTip() : String
      {
         var num0:int = int(this.getResolveChosenArr().length);
         return "当前选择的物品中，有" + ComMethod.color(num0 + "个","#00FF00") + "可被分解成碎片，\n要继续吗？";
      }
      
      protected function resolveChosenOrder() : Boolean
      {
         var arr0:Array = this.getResolveChosenArr();
         ItemsResolveCtrl.resolveArr(arr0,this);
         return true;
      }
      
      protected function getResolveChosenArr() : Array
      {
         var da0:IO_ItemsData = null;
         var bb0:Boolean = false;
         var equipDa0:EquipData = null;
         var arr0:Array = [];
         for each(var _loc7_ in this.dataArr)
         {
            da0 = _loc7_;
            _loc7_;
            if(Boolean(da0.isChosen) && !da0.getSave().getLockB())
            {
               if(da0.canResolveB())
               {
                  bb0 = false;
                  equipDa0 = da0 as EquipData;
                  if(Boolean(equipDa0))
                  {
                     if(equipDa0.isNormalEquipB())
                     {
                        bb0 = true;
                     }
                  }
                  if(bb0)
                  {
                     arr0.push(da0);
                  }
               }
            }
         }
         return arr0;
      }
      
      public function delNoPositionItems() : void
      {
         var da0:IO_ItemsData = null;
         var site0:int = 0;
         var delArr0:Array = [];
         for each(var _loc6_ in this.dataArr)
         {
            da0 = _loc6_;
            _loc6_;
            site0 = int(da0.getSave().site);
            if(!this.getSaveGroup().getUnlockBySite(site0))
            {
               delArr0.push(da0);
            }
         }
         this.removeDataArr(delArr0);
      }
      
      public function getSaveGroup() : ItemsSaveGroup
      {
         INIT.showError("该方法必须被覆盖");
         return null;
      }
      
      public function getGripType() : String
      {
         INIT.showError("该方法必须被覆盖");
         return "";
      }
      
      public function getAllChildTypeArr() : Array
      {
         return [];
      }
      
      public function getBlackMarketCanShowTypeArr() : Array
      {
         return [];
      }
   }
}

