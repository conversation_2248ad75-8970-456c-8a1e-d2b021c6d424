package dataAll._player.count.props
{
   import dataAll._player.count.props.testDiy.ArmsDpsTestDiy;
   import dataAll._player.count.props.testDiy.TestDiy;
   import dataAll.body.attack.HurtData;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyData;
   
   public class DpsCountCtrl
   {
      
      public static var enabled:Boolean = false;
      
      public static var labelArr:Array = ["main","pet","more"];
      
      private static var obj:Object = {
         "main":new DpsCountDataGroup(),
         "pet":new DpsCountDataGroup(),
         "more":new DpsCountDataGroup()
      };
      
      private static var colorArr:Array = ["FFFF00","00FF00","00FFFF","DE66FF","FF5F5F"];
      
      private static var colorIndex:int = 0;
      
      private static var beforeArr:Array = [];
      
      private static var now_t:int = 30;
      
      private static var armsDps:ArmsDpsTestDiy = new ArmsDpsTestDiy();
      
      private static var zeroDiy:TestDiy = new TestDiy();
      
      private static var nowDiy:TestDiy = zeroDiy;
      
      public function DpsCountCtrl()
      {
         super();
      }
      
      public static function openOrClose() : void
      {
         if(enabled)
         {
            enabled = false;
         }
         else
         {
            enabled = true;
            nowDiy = armsDps;
         }
         nowDiy.openOrClose(enabled);
      }
      
      public static function getMain() : DpsCountDataGroup
      {
         return obj["main"];
      }
      
      public static function noSuperB() : Boolean
      {
         if(enabled)
         {
            return nowDiy.noSuperB;
         }
         return false;
      }
      
      public static function startLevel() : void
      {
         if(enabled)
         {
            nowDiy.startLevel();
         }
      }
      
      public static function bodyAdd(b0:IO_NormalBody) : void
      {
         if(enabled)
         {
            nowDiy.bodyAdd(b0);
         }
      }
      
      public static function bodyDie(b0:IO_NormalBody, b1:IO_NormalBody, h0:HurtData) : void
      {
         if(enabled)
         {
            nowDiy.bodyDie(b0,b1,h0);
         }
      }
      
      public static function toHurt(b0:IO_NormalBody, b1:IO_NormalBody, h0:HurtData) : void
      {
         var type0:String = null;
         if(enabled)
         {
            if(b1 is IO_NormalBody)
            {
               type0 = panIsWeMainB(b1);
               if(type0 != "")
               {
                  obj[type0].act.addValue(h0.hurtRatio);
               }
            }
            nowDiy.toHurt(b0,b1,h0);
         }
      }
      
      public static function overGamingClear() : void
      {
         var newObj0:Object = null;
         var n:* = undefined;
         var da0:DpsCountDataGroup = null;
         if(enabled)
         {
            newObj0 = {};
            if(Boolean(obj["main"].act.haveDataB()))
            {
               beforeArr.unshift(obj);
            }
            colorIndex = (colorIndex + 1) % colorArr.length;
            for(n in obj)
            {
               da0 = new DpsCountDataGroup();
               newObj0[n] = da0;
               da0.act.allColor = colorArr[colorIndex];
            }
            obj = newObj0;
            if(beforeArr.length > 4)
            {
               beforeArr.length = 4;
            }
            nowDiy.overGamingClear();
         }
      }
      
      public static function overLevel(from0:String) : void
      {
         if(enabled)
         {
            nowDiy.overLevel(from0);
         }
      }
      
      public static function getImageDataArray(label0:String, num0:int = 1, valueB0:Boolean = true, arrangeB0:Boolean = true) : Array
      {
         var obj0:Object = null;
         var dg0:DpsCountDataGroup = null;
         var dataArr0:Array = [];
         var index0:int = 0;
         var arr0:Array = [obj[label0]];
         for each(obj0 in beforeArr)
         {
            arr0.push(obj0[label0]);
         }
         arr0.length = num0;
         for each(dg0 in arr0)
         {
            dataArr0 = dataArr0.concat(dg0.getImageDataArray(index0 == 0 && valueB0,arrangeB0));
            index0++;
         }
         return dataArr0;
      }
      
      private static function panIsWeMainB(b0:IO_NormalBody) : String
      {
         var dat0:NormalBodyData = null;
         var body0:IO_NormalBody = null;
         if(!b0)
         {
            return "";
         }
         dat0 = b0.getData();
         var bodyArr0:Array = [b0,dat0.vehicleFatherBody,dat0.summoned.fatherBody,b0.getTransCtrl().getFatherBody()];
         for each(body0 in bodyArr0)
         {
            if(Boolean(body0))
            {
               dat0 = body0.getData();
               if(dat0.camp == "we")
               {
                  if(dat0.isWeMainPlayerB())
                  {
                     return "main";
                  }
                  if(dat0.isSaveDataPlayerB())
                  {
                     return "more";
                  }
                  if(dat0.isPetB())
                  {
                     return "pet";
                  }
               }
            }
         }
         return "";
      }
      
      public static function FTimer() : void
      {
         var dg0:DpsCountDataGroup = null;
         if(enabled)
         {
            if(now_t >= 30)
            {
               now_t = 0;
               for each(dg0 in obj)
               {
                  dg0.FTimerSecond();
               }
               Gaming.uiGroup.dpsCountBox.FTimerSecond();
            }
            else
            {
               ++now_t;
            }
            nowDiy.FTimer();
         }
      }
      
      public static function allFTimer() : void
      {
         if(enabled)
         {
            nowDiy.allFTimer();
         }
      }
   }
}

