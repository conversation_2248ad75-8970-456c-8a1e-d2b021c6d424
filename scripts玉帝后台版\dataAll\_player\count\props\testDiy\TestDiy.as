package dataAll._player.count.props.testDiy
{
   import dataAll.body.attack.HurtData;
   import dataAll.level.define.mapRect.MapRect;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyData;
   import gameAll.hero.HeroBody;
   import gameAll.level.Levels;
   import gameAll.level.data.OverLevelShow;
   import w_test.cheating.AICheating;
   
   public class TestDiy
   {
      
      public var frameRate:int = 60;
      
      public var heroAIB:Boolean = true;
      
      public var heroAIOne:String = "armsDpsTest";
      
      public var closeEnemyAIB:Boolean = true;
      
      public var enemyBirthRect:String = "r2";
      
      public var noSuperB:Boolean = true;
      
      protected var bossLive:Number = -1;
      
      public function TestDiy()
      {
         super();
      }
      
      protected static function doOverLevel() : void
      {
         Gaming.LG.overLevel(OverLevelShow.ORDER);
      }
      
      protected static function doRestartLevel() : void
      {
         Gaming.LG.restartLevel();
      }
      
      protected function get nowLevel() : Levels
      {
         return Gaming.LG.nowLevel;
      }
      
      public function openOrClose(enabled0:Boolean) : void
      {
         if(enabled0)
         {
            if(this.heroAIB)
            {
               AICheating.openHeroAI();
            }
            Gaming.ME.stage.frameRate = this.frameRate;
         }
         else
         {
            AICheating.closeHeroAI();
            Gaming.ME.stage.frameRate = 30;
         }
         AICheating.setEnemyAI(this.closeEnemyAIB ? !enabled0 : true);
      }
      
      public function startLevel() : void
      {
         this.bossLive = 0;
      }
      
      public function bodyAdd(b0:IO_NormalBody) : void
      {
         var rect0:MapRect = null;
         var x0:int = 0;
         var y0:int = 0;
         var dat0:NormalBodyData = b0.getData();
         if(Boolean(dat0.unitDefine))
         {
            if(dat0.isEnemy())
            {
               if(this.enemyBirthRect != "")
               {
                  rect0 = Gaming.LG.nowLevel.getRect(this.enemyBirthRect);
                  x0 = rect0.x + Math.random() * rect0.width;
                  y0 = rect0.y + rect0.height / 2;
                  b0.setXY(x0,y0);
               }
            }
         }
         var hero0:HeroBody = b0 as HeroBody;
         if(Boolean(hero0))
         {
            if(hero0.dat.isMainPlayerB())
            {
               if(this.heroAIOne != "")
               {
                  hero0.ai.heroAttackAI.heroOne.setTo(this.heroAIOne);
               }
            }
         }
      }
      
      public function bodyDie(b0:IO_NormalBody, b1:IO_NormalBody, h0:HurtData) : void
      {
      }
      
      public function toHurt(b0:IO_NormalBody, b1:IO_NormalBody, h0:HurtData) : void
      {
         var hero0:HeroBody = b1 as HeroBody;
         if(Boolean(hero0))
         {
            if(hero0.dat.isMainPlayerB())
            {
               if(Boolean(b0.getData().isEnemy()) && Boolean(b0.getData().isBossB()))
               {
                  if(this.bossLive == -1)
                  {
                     this.bossLive = 0;
                  }
               }
            }
         }
      }
      
      public function overGamingClear() : void
      {
      }
      
      public function overLevel(from0:String) : void
      {
      }
      
      public function FTimer() : void
      {
         if(this.bossLive >= 0)
         {
            this.bossLive += 1 / 30;
         }
      }
      
      public function allFTimer() : void
      {
      }
   }
}

