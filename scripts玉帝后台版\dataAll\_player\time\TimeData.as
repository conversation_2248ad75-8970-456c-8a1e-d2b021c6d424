package dataAll._player.time
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import dataAll._player.PlayerData;
   import dataAll.equip.EquipPropertyData;
   import dataAll.equip.add.IO_EquipAddGetter;
   import dataAll.level.LevelDiffGetting;
   import dataAll.ui.StateIconData;
   
   public class TimeData implements IO_EquipAddGetter
   {
      
      private static const START_OPEN:StringDate = new StringDate("2020-5-4");
      
      public var playerData:PlayerData;
      
      private var save:TimeSave;
      
      private var readTimeDate:StringDate = new StringDate();
      
      private var readTimeDateClass:Date = null;
      
      private var tempMeData:EquipPropertyData = new EquipPropertyData();
      
      public var weekend:Boolean = false;
      
      public var su25:Boolean = false;
      
      private var wilderId:int = 0;
      
      private var wilderTimeDate:StringDate = null;
      
      public function TimeData()
      {
         super();
      }
      
      public function inData_bySave(s0:TimeSave) : void
      {
         this.save = s0;
      }
      
      public function isNowActiveB() : *
      {
         return this.su25;
      }
      
      public function setNowReadTime(str0:String) : Boolean
      {
         this.save.prevReadTime = this.save.nowReadTime;
         this.save.nowReadTime = str0;
         var c0:Number = this.save.getDayGap();
         var timeDa0:StringDate = new StringDate(str0);
         this.readTimeDate = timeDa0;
         this.readTimeDateClass = timeDa0.getDateClass();
         this.countWilderId(timeDa0);
         this.weekend = this.readTimeDateClass.day == 6 || this.readTimeDateClass.day == 0;
         LevelDiffGetting.useNewDemonB = timeDa0.reductionOneStr("2025-5-5") >= 0;
         if(timeDa0.betweenIn("2025-7-1","2025-8-31 23:59:59") == 0)
         {
            this.su25 = true;
         }
         else
         {
            this.su25 = false;
         }
         return c0 >= 1;
      }
      
      public function panNewWeekB(str0:String) : Boolean
      {
         var cday0:int = -TimeSave.weekStartTime.reductionOneStr(str0);
         var weekIndex0:int = int(cday0 / 7);
         if(weekIndex0 > this.save.weekIndex)
         {
            this.save.weekIndex = weekIndex0;
            return true;
         }
         return false;
      }
      
      public function panNewWeek6(str0:String) : Boolean
      {
         var cday0:int = -TimeSave.week6Time.reductionOneStr(str0);
         var week60:int = int(cday0 / 7);
         if(week60 > this.save.week6)
         {
            this.save.week6 = week60;
            return true;
         }
         return false;
      }
      
      public function getWeekIndex() : int
      {
         return this.save.weekIndex;
      }
      
      public function newDayCtrl() : void
      {
      }
      
      public function clearAllTime() : void
      {
         this.save.clearAllTime();
      }
      
      public function getTimeTip() : String
      {
         return "昨天：" + this.save.prevReadTime + "\n今天：" + this.save.nowReadTime;
      }
      
      public function getReadTimeDate() : StringDate
      {
         return this.readTimeDate;
      }
      
      public function getReadTimeDateClass() : Date
      {
         return this.readTimeDateClass;
      }
      
      public function getReadTime() : String
      {
         return this.save.nowReadTime;
      }
      
      public function getDayGap() : int
      {
         return this.save.getDayGap();
      }
      
      private function countWilderId(readTime0:StringDate) : void
      {
         var maxId0:int = Gaming.defineGroup.wilder.getMaxId();
         var day0:int = START_OPEN.compareDate(readTime0);
         var week0:int = Math.floor(day0 / 7);
         var index0:int = week0 % maxId0;
         this.wilderId = index0 + 1;
         var da0:StringDate = START_OPEN.copy();
         da0.addDay(week0 * 7);
         this.wilderTimeDate = da0;
         var start0:StringDate = da0.copy();
         start0.addDay(-index0);
         var f0:int = 0;
      }
      
      public function getWilderOpenId() : int
      {
         return this.wilderId;
      }
      
      public function getWilderOpenTime() : StringDate
      {
         return this.wilderTimeDate;
      }
      
      public function addDoubleTime(name0:String, v0:Number) : void
      {
         this.save[name0] += v0;
         this.fleshData();
      }
      
      public function getDoubleTime(name0:String) : Number
      {
         return this.save[name0];
      }
      
      public function getDoubleTimeTip() : String
      {
         var name0:String = null;
         var cn0:String = null;
         var v0:Number = NaN;
         var str0:String = "";
         var arr_len0:int = int(TimeSave.doubleArr.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            name0 = TimeSave.doubleArr[i];
            cn0 = TimeSave.doubleCnArr[i];
            v0 = this.getDoubleTime(name0);
            if(v0 > 0)
            {
               str0 += (i == 0 ? "" : "\n") + "<i1>|" + cn0 + "：";
               str0 += "<green " + ComMethod.getTimeStr(v0) + "/>";
            }
         }
         return str0;
      }
      
      public function haveDoubleTimeB() : Boolean
      {
         return this.save.doubleExpTime > 0;
      }
      
      public function getProAddObj() : Object
      {
         return this.getAddData();
      }
      
      public function getProAddMax(pro0:String) : Number
      {
         return 0;
      }
      
      public function getAddData() : Object
      {
         var obj0:Object = {};
         var armsAdd0:Number = this.getArmsDropAdd();
         var equipAdd0:Number = this.getEquipDropAdd();
         var materAdd0:Number = this.getMaterialsDropAdd();
         obj0.orredArmsDropPro = armsAdd0;
         obj0.rareArmsDropPro = armsAdd0;
         obj0.blackArmsDropPro = armsAdd0;
         obj0.orredEquipDropPro = equipAdd0;
         obj0.rareEquipDropPro = equipAdd0;
         obj0.blackEquipDropPro = equipAdd0;
         obj0.bloodStoneDropPro = materAdd0;
         obj0.godStoneDropPro = materAdd0;
         obj0.converStoneDropPro = materAdd0;
         obj0.lifeCatalystDropPro = materAdd0;
         obj0.taxStampDropPro = materAdd0;
         return obj0;
      }
      
      public function getTestProArr() : Array
      {
         var n:* = undefined;
         var arr0:Array = [];
         var obj0:Object = this.getAddData();
         for(n in obj0)
         {
            arr0.push(n);
         }
         return arr0;
      }
      
      private function getArmsDropAdd() : Number
      {
         if(this.save.doubleArmsDropTime > 0)
         {
            return 1;
         }
         return 0;
      }
      
      private function getEquipDropAdd() : Number
      {
         if(this.save.doubleEquipDropTime > 0)
         {
            return 1;
         }
         return 0;
      }
      
      private function getMaterialsDropAdd() : Number
      {
         if(this.save.doubleMaterialsDropTime > 0)
         {
            return 1;
         }
         return 0;
      }
      
      public function getPlayerStateDataArr() : Array
      {
         var pro0:String = null;
         var v0:Number = NaN;
         var da0:StateIconData = null;
         var arr0:Array = [];
         var proArr0:Array = TimeSave.doubleArr;
         for each(pro0 in proArr0)
         {
            v0 = Number(this.save[pro0]);
            if(v0 > 0)
            {
               da0 = new StateIconData();
               da0.name = pro0;
               da0.iconUrl = "ThingsIcon/" + pro0 + "16";
               da0.time = v0;
               arr0.push(da0);
            }
         }
         return arr0;
      }
      
      private function fleshData() : void
      {
         this.playerData.equip.fleshMergeData();
      }
      
      public function FTimerSecond() : void
      {
         var pro0:String = null;
         var v0:Number = NaN;
         var proArr0:Array = TimeSave.doubleArr;
         var changeB0:Boolean = false;
         for each(pro0 in proArr0)
         {
            v0 = Number(this.save[pro0]);
            if(v0 >= 1)
            {
               v0--;
            }
            else if(v0 != 0)
            {
               v0 = 0;
               changeB0 = true;
            }
            this.save[pro0] = v0;
         }
         if(changeB0)
         {
            this.fleshData();
         }
      }
   }
}

