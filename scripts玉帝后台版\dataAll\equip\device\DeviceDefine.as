package dataAll.equip.device
{
   import com.sounto.utils.ClassProperty;
   import dataAll.equip.creator.EquipPropertyDataCreator;
   import dataAll.equip.creator.EquipSkillCreator;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.define.LvEquipDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.define.HeroSkillDefine;
   
   public class DeviceDefine extends LvEquipDefine
   {
      
      public static var pro_arr:Array = [];
      
      public static var MAX_LEVEL:int = 9;
      
      public var skill:String = "";
      
      public var rareB:Boolean = false;
      
      public function DeviceDefine()
      {
         super();
         this.rareMul = 2;
         this.maxLevel = 0;
         this.mustMul = 1;
         this.canNum = 0;
      }
      
      public function get maxLevel() : Number
      {
         return CF.getAttribute("maxLevel");
      }
      
      public function set maxLevel(v0:Number) : void
      {
         CF.setAttribute("maxLevel",v0);
      }
      
      public function get rareMul() : Number
      {
         return CF.getAttribute("rareMul");
      }
      
      public function set rareMul(v0:Number) : void
      {
         CF.setAttribute("rareMul",v0);
      }
      
      public function get mustMul() : Number
      {
         return CF.getAttribute("mustMul");
      }
      
      public function set mustMul(v0:Number) : void
      {
         CF.setAttribute("mustMul",v0);
      }
      
      public function get canNum() : Number
      {
         return CF.getAttribute("canNum");
      }
      
      public function set canNum(v0:Number) : void
      {
         CF.setAttribute("canNum",v0);
      }
      
      override public function inData_byXML(xml0:XML, father0:String) : void
      {
         father = father0;
         name = xml0.@name;
         cnName = xml0.@cnName;
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         if(iconLabel == "")
         {
            iconLabel = "ThingsIcon/" + name;
         }
         if(baseLabel == "")
         {
            baseLabel = name;
         }
         type = EquipType.DEVICE;
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData(this,obj0,pro_arr);
      }
      
      public function clone() : DeviceDefine
      {
         var d0:DeviceDefine = new DeviceDefine();
         d0.inData_byObj(this);
         return d0;
      }
      
      override public function getTrueCnName() : String
      {
         return cnName + lv + "级";
      }
      
      public function getMaxLevel() : int
      {
         var skillD0:HeroSkillDefine = null;
         if(this.maxLevel == 0)
         {
            skillD0 = Gaming.defineGroup.skill.getDefine(this.skill) as HeroSkillDefine;
            if(Boolean(skillD0))
            {
               return skillD0.getMaxLevel();
            }
            return MAX_LEVEL;
         }
         return this.maxLevel;
      }
      
      override public function getAddObj() : Object
      {
         var max0:Number = NaN;
         var obj0:Object = {};
         var lifeMul0:Number = 0.01 + (lv - 1) * 0.03;
         if(lv >= 6)
         {
            lifeMul0 = (lv - 1) * 0.1 - 0.3;
         }
         if(this.rareB)
         {
            if(lv == 1)
            {
               lifeMul0 = 0.03;
            }
            lifeMul0 *= this.rareMul;
            max0 = 0.8;
            if(baseLabel == "blackHoleDevicer")
            {
               max0 = 0.9;
            }
            if(lifeMul0 > max0)
            {
               lifeMul0 = max0;
            }
            obj0["dpsMul"] = lifeMul0;
         }
         obj0["lifeMul"] = lifeMul0;
         return obj0;
      }
      
      public function getSkillDefine() : HeroSkillDefine
      {
         return Gaming.defineGroup.skill.getDefine(this.skill) as HeroSkillDefine;
      }
      
      public function getNextSkillDefine() : HeroSkillDefine
      {
         if(lv >= this.getMaxLevel())
         {
            return null;
         }
         return this.getSkillDefine().getNextDefine();
      }
      
      public function getUpgradeMustName() : String
      {
         return baseLabel + "_1";
      }
      
      public function getUpgradeName() : String
      {
         return baseLabel + "_" + (lv + 1);
      }
      
      override public function getGoodsTip() : String
      {
         return this.getGatherTip();
      }
      
      public function getGatherTip() : String
      {
         var str0:String = "";
         str0 += "<i1>|<blue <b>提升人物：</b>/>";
         str0 += "\n" + EquipPropertyDataCreator.getText_byObj(this.getAddObj());
         str0 += "\n<i1>|<blue <b>装置技能：</b>/>";
         str0 += "\n" + EquipSkillCreator.getAllSkillTip([this.skill]);
         if(description != "")
         {
            str0 += "\n\n" + description;
         }
         return str0;
      }
      
      override public function getSortIndex() : int
      {
         var index0:int = index;
         var d0:DeviceDefine = Gaming.defineGroup.device.getDefine(this.getUpgradeMustName());
         if(Boolean(d0))
         {
            index0 = d0.index;
         }
         return int(index0 * 1000 + 99 - lv);
      }
      
      override public function canResolveB() : Boolean
      {
         if(lv >= 2)
         {
            if(baseLabel == "timeCapsule")
            {
               return false;
            }
            return true;
         }
         return super.canResolveB();
      }
      
      override public function getResolveGift() : GiftAddDefineGroup
      {
         var all0:GiftAddDefineGroup = null;
         var i:int = 0;
         var must0:MustDefine = null;
         var gift0:GiftAddDefineGroup = null;
         if(lv >= 2)
         {
            all0 = new GiftAddDefineGroup();
            for(i = 2; i <= lv; i++)
            {
               must0 = DeviceDataCreator.getUpgradeThingsMust(this,i,"bag");
               gift0 = new GiftAddDefineGroup();
               gift0.inMustDefineOnlyThings(must0);
               all0.merge(gift0);
            }
            return all0;
         }
         return super.getResolveGift();
      }
      
      override public function dealBtnListCn(label0:String) : String
      {
         if(lv >= 2 && label0 == "resolve")
         {
            return "分解";
         }
         return "";
      }
   }
}

