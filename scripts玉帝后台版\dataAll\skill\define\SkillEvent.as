package dataAll.skill.define
{
   public class SkillEvent
   {
      
      public static const no:String = "no";
      
      public static const interval:String = "interval";
      
      public static const loopClick:String = "loopClick";
      
      public static const swapArms:String = "swapArms";
      
      public static const hit:String = "hit";
      
      public static const allHit:String = "allHit";
      
      public static const underHit:String = "underHit";
      
      public static const underSkillHit:String = "underSkillHit";
      
      public static const underAllHit:String = "underAllHit";
      
      public static const underAllHit_before:String = "underAllHit_before";
      
      public static const ros:String = "ros";
      
      public static const underRos:String = "underRos";
      
      public static const hurt:String = "hurt";
      
      public static const underHurt:String = "underHurt";
      
      public static const underAllHurt:String = "underAllHurt";
      
      public static const underHurtBefore:String = "underHurtBefore";
      
      public static const killTarget:String = "killTarget";
      
      public static const uavYing:String = "uavYing";
      
      public static const beforeDie:String = "beforeDie";
      
      public static const allDie:String = "allDie";
      
      public static const dieEvent:String = "dieEvent";
      
      public static const die:String = "die";
      
      public static const dieAfterHurt:String = "dieAfterHurt";
      
      public static const killNoDieArr:Array = [dieEvent,dieAfterHurt];
      
      public static const beforeAttack:String = "beforeAttack";
      
      public static const playAttackLabel:String = "playAttackLabel";
      
      public static const heroShoot:String = "heroShoot";
      
      public static const reloadKey2:String = "reloadKey2";
      
      public static const carShoot:String = "carShoot";
      
      public static const add:String = "add";
      
      public static const addAndCon:String = "addAndCon";
      
      public static const bodyAdd:String = "bodyAdd";
      
      public static const useSkill:String = "useSkill";
      
      public static const skillCombo:String = "skillCombo";
      
      public static const underEnemySkill:String = "underEnemySkill";
      
      public static const avtiveSkillCdOver:String = "avtiveSkillCdOver";
      
      public static const moveCombo:String = "moveCombo";
      
      public static const airDownKey:String = "airDownKey";
      
      public static const heroJump:String = "heroJump";
      
      public static const heroSquat:String = "heroSquat";
      
      public static const vehicleOver:String = "vehicleOver";
      
      public static const useWeapon:String = "useWeapon";
      
      public static const replaceWeapon:String = "replaceWeapon";
      
      public static const replaceDevice:String = "replaceDevice";
      
      public static const enemyHeroAttack:String = "enemyHeroAttack";
      
      public static const frame1:String = "frame1";
      
      public function SkillEvent()
      {
         super();
      }
   }
}

