package dataAll._app.setting.key
{
   import com.common.text.TextWay;
   import dataAll._data.ConstantDefine;
   
   public class KeyActionDefineGroup
   {
      
      public var obj:Object = {};
      
      public var arr:Array = [];
      
      public var keyCnObj:Object = {};
      
      public var noKeyObj:Object = {};
      
      public function KeyActionDefineGroup()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         var n:* = undefined;
         var d0:KeyActionDefine = null;
         var thingsXML0:* = xml0.key;
         for(n in thingsXML0)
         {
            d0 = new KeyActionDefine();
            d0.inData_byXML(thingsXML0[n]);
            this.obj[d0.name] = d0;
            this.arr.push(d0);
         }
         this.addSkill();
         this.keyCnObj = this.getValueObj(xml0.cn);
         this.noKeyObj = this.getValueObj(xml0.no);
      }
      
      private function addSkill() : void
      {
         var d0:KeyActionDefine = null;
         var keyIndex0:int = 0;
         for(var i:int = 1; i <= ConstantDefine.maxSkillNum; i++)
         {
            d0 = new KeyActionDefine();
            d0.name = "skill" + i;
            d0.cnName = "技能位" + i;
            keyIndex0 = i;
            if(keyIndex0 == 10)
            {
               keyIndex0 = 0;
            }
            d0.single = ["NUMBER_" + keyIndex0];
            d0.p2 = d0.single.concat([]);
            d0.p1 = ["NUMPAD_" + keyIndex0];
            d0.changeToKeyCode();
            this.obj[d0.name] = d0;
            this.arr.push(d0);
         }
      }
      
      public function getKeyObj(playerType0:String) : Object
      {
         var d0:KeyActionDefine = null;
         var keyArr0:Array = null;
         var obj2:Object = {};
         for each(d0 in this.obj)
         {
            keyArr0 = d0[playerType0];
            obj2[d0.name] = keyArr0;
         }
         return obj2;
      }
      
      public function getDefine(name0:String) : KeyActionDefine
      {
         return this.obj[name0];
      }
      
      private function getValueObj(str0:String) : Object
      {
         var str2:String = null;
         var v_arr0:Array = null;
         var v0:int = 0;
         var cn0:String = null;
         var obj0:Object = {};
         var arr0:Array = str0.split(";");
         for each(str2 in arr0)
         {
            if(Boolean(str2))
            {
               str2 = TextWay.toHan2(str2);
               v_arr0 = str2.split(":");
               v0 = int(v_arr0[0]);
               cn0 = v_arr0[1];
               obj0[v0] = cn0;
            }
         }
         return obj0;
      }
      
      public function getCnByKetCode(v0:int) : String
      {
         if(this.keyCnObj.hasOwnProperty(v0))
         {
            return this.keyCnObj[v0];
         }
         return String.fromCharCode(v0);
      }
      
      public function isCanB(v0:int) : Boolean
      {
         return !this.noKeyObj.hasOwnProperty(v0);
      }
   }
}

