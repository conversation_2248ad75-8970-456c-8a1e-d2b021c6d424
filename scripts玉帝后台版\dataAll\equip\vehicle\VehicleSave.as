package dataAll.equip.vehicle
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.save.EquipSave;
   import dataAll.skill.save.HeroSkillSaveGroup;
   
   public class VehicleSave extends EquipSave
   {
      
      private static const ZERO:VehicleSave = new VehicleSave();
      
      public static var pro_arr:Array = null;
      
      public static var mePro_arr:Array = null;
      
      public var skill:HeroSkillSaveGroup = new HeroSkillSaveGroup();
      
      public function VehicleSave()
      {
         super();
         this.lifeMulAddLv = 0;
         this.mainMulAddLv = 0;
         this.subMulAddLv = 0;
         this.attackMulAddLv = 0;
      }
      
      override public function getProArr() : Array
      {
         return pro_arr;
      }
      
      override public function getZero() : EquipSave
      {
         return ZERO;
      }
      
      public function get lifeMulAddLv() : Number
      {
         return CF.getAttribute("lifeMulAdd");
      }
      
      public function set lifeMulAddLv(v0:Number) : void
      {
         CF.setAttribute("lifeMulAdd",v0);
      }
      
      public function get mainMulAddLv() : Number
      {
         return CF.getAttribute("mainMulAdd");
      }
      
      public function set mainMulAddLv(v0:Number) : void
      {
         CF.setAttribute("mainMulAdd",v0);
      }
      
      public function get subMulAddLv() : Number
      {
         return CF.getAttribute("subMulAdd");
      }
      
      public function set subMulAddLv(v0:Number) : void
      {
         CF.setAttribute("subMulAdd",v0);
      }
      
      public function get attackMulAddLv() : Number
      {
         return CF.getAttribute("attackMulAdd");
      }
      
      public function set attackMulAddLv(v0:Number) : void
      {
         CF.setAttribute("attackMulAdd",v0);
      }
      
      public function get upLv() : Number
      {
         return CF.getAttribute("upLv");
      }
      
      public function set upLv(v0:Number) : void
      {
         CF.setAttribute("upLv",v0);
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObj(obj0);
         ClassProperty.inData_bySaveObj(this,obj0,mePro_arr);
         this.skill.inData_byObj(obj0["skill"]);
         this.newSave();
      }
      
      private function newSave() : void
      {
         this.skill.lockLen = 99;
      }
      
      public function inEvoData_byObj(obj0:Object) : void
      {
         super.inData_byObj(obj0);
         this.skill.inData_byObj(obj0["skill"]);
      }
      
      override public function inDataByDefine(d0:EquipDefine) : void
      {
         super.inDataByDefine(d0);
         this.newSave();
      }
      
      public function getMulAdd(label0:String) : Number
      {
         return ComMethod.toFixed(this.getMulAddLv(label0) * VehicleDataCreator.getStrengthenOneAdd(label0),1);
      }
      
      public function getMulAddLv(label0:String) : Number
      {
         return this[label0 + "AddLv"];
      }
      
      public function setMulAddLv(label0:String, v0:Number) : void
      {
         this[label0 + "AddLv"] = v0;
      }
      
      public function getAllAddLv() : int
      {
         return this.lifeMulAddLv + this.mainMulAddLv + this.subMulAddLv + this.attackMulAddLv;
      }
      
      public function strengthenOne(label0:String) : void
      {
         var max0:int = VehicleDataCreator.getStrengthenMaxLv(label0,this.getVehicleDefine());
         var now0:int = this.getMulAddLv(label0);
         if(now0 < max0)
         {
            ++this[label0 + "AddLv"];
         }
      }
      
      override public function getLevelTipString() : String
      {
         var s0:String = String(getTrueLevel());
         if(this.upLv > 0)
         {
            s0 += "+" + this.upLv;
         }
         return s0;
      }
      
      public function isDiggersComposeB() : Boolean
      {
         if(shopB == false)
         {
            if(name == "Diggers")
            {
               return true;
            }
         }
         return false;
      }
      
      override public function getTrueObj() : Object
      {
         if(this.isDiggersComposeB())
         {
            return {};
         }
         return this.getDefine().getAddObj();
      }
      
      override public function getDataClass() : EquipData
      {
         var d0:VehicleDefine = this.getVehicleDefine();
         if(d0.defineType == VehicleDefineType.SHOOT)
         {
            return new ShootVehicleData();
         }
         return new VehicleData();
      }
      
      override public function getDefine() : EquipDefine
      {
         var d0:EquipDefine = Gaming.defineGroup.vehicle.getDefine(imgName);
         if(!(d0 is EquipDefine))
         {
            INIT.showError("找不到定义：EquipDefine" + imgName);
         }
         return d0;
      }
      
      public function getVehicleDefine() : VehicleDefine
      {
         return Gaming.defineGroup.vehicle.getDefine(imgName);
      }
      
      public function getShootVehicleDefine() : ShootVehicleDefine
      {
         return Gaming.defineGroup.vehicle.getDefine(imgName) as ShootVehicleDefine;
      }
      
      override public function clone() : EquipSave
      {
         var obj0:Object = ClassProperty.copyObj(this);
         var s0:EquipSave = new VehicleSave();
         s0.inData_byObj(obj0);
         return s0;
      }
   }
}

