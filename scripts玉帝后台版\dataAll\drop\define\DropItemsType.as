package dataAll.drop.define
{
   public class DropItemsType
   {
      
      public static const effect:String = "effect";
      
      public static const things:String = "things";
      
      public static const arms:String = "arms";
      
      public static const equip:String = "equip";
      
      public static const gene:String = "gene";
      
      public static const addCharger:String = "addCharger";
      
      public static const addPumpkin:String = "addPumpkin";
      
      public static const snakeEffectArr:Array = ["invinCanned","hurtDrug","hammerMineEffect","hurtMineEffect"];
      
      public static const snakeLevelArr:Array = ["invinCanned","hurtDrug"];
      
      public static const giftBaseObj:Object = {
         "anniCoin":"addAniverCoin",
         "tenCoin":"addTenCoin",
         "partsCoin":"addPartsCoin"
      };
      
      public function DropItemsType()
      {
         super();
      }
      
      public static function getByBaseGiftName(name0:String) : String
      {
         var obj0:Object = giftBaseObj;
         if(obj0.hasOwnProperty(name0))
         {
            return obj0[name0];
         }
         return "";
      }
      
      public static function getByEffectDrop(name0:String) : String
      {
         var n:* = undefined;
         for(n in giftBaseObj)
         {
            if(giftBaseObj[n] == name0)
            {
               return n as String;
            }
         }
         return "";
      }
   }
}

