package dataAll.things
{
   import com.common.text.TextWay;
   import com.sounto.utils.TextMethod;
   import dataAll._app.parts.PartsCreator;
   import dataAll._app.parts.PartsDataGroup;
   import dataAll._app.parts.define.PartsConst;
   import dataAll._player.PlayerData;
   import dataAll._player.base.PlayerMainData;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.NormalItemsData;
   import dataAll.items.save.ItemsSave;
   import dataAll.things.define.ThingsDefine;
   import dataAll.things.define.ThingsEffectDefine;
   import dataAll.things.define.ThingsName;
   import dataAll.things.save.ThingsSave;
   import gameAll.more.DoubleCtrl;
   
   public class ThingsData extends NormalItemsData implements IO_ItemsData
   {
      
      public var save:ThingsSave;
      
      public function ThingsData()
      {
         super();
      }
      
      public function inData_bySave(s0:ThingsSave, pd0:NormalPlayerData, dg0:ThingsDataGroup) : void
      {
         this.save = s0;
         setPlayerData(pd0);
         setFatherData(dg0);
      }
      
      public function clone() : ThingsData
      {
         var da0:ThingsData = null;
         da0 = new ThingsData();
         da0.save = this.save.clone();
         da0.placeType = placeType;
         da0.playerData = playerData;
         return da0;
      }
      
      public function normalClone() : IO_ItemsData
      {
         return this.clone();
      }
      
      override public function getSave() : ItemsSave
      {
         return this.save;
      }
      
      public function getCnType() : String
      {
         return "物品";
      }
      
      public function getIconImgUrl(maxWidth0:int = 0, maxHeight0:int = 0) : String
      {
         return this.save.getDefine().iconUrl;
      }
      
      public function getCnName() : String
      {
         return this.save.getDefine().cnName;
      }
      
      public function getSellPrice() : Number
      {
         return this.save.getSellPrice("things") * this.save.nowNum;
      }
      
      public function getWearLevel() : int
      {
         return this.save.getTrueLevel();
      }
      
      public function getTypeId() : String
      {
         return "03";
      }
      
      public function getChildTypeId() : String
      {
         return "01";
      }
      
      override public function isCanNumSwapB() : Boolean
      {
         return true;
      }
      
      override public function isCanOverlayB() : Boolean
      {
         return !this.save.getDefine().noOverlayB;
      }
      
      override public function getNowNum() : int
      {
         return this.save.nowNum;
      }
      
      override public function setNowNum(num0:int) : void
      {
         this.save.nowNum = num0;
      }
      
      override public function addNowNum(num0:int, otherDa0:IO_ItemsData = null) : void
      {
         addNowTrueNum(num0,otherDa0);
      }
      
      public function getAffterUseSaveType() : String
      {
         return this.save.getDefine().getAffterUseSaveType();
      }
      
      public function getLevelCanUseNum() : int
      {
         var can0:int = 0;
         var num0:int = this.getNowNum();
         if(Boolean(playerData))
         {
            can0 = playerData.thingsBag.getLevelCanUseNum(this.save.name);
            if(num0 > can0)
            {
               num0 = can0;
            }
         }
         return num0;
      }
      
      override public function canRefiningB() : Boolean
      {
         if(this.save.isOnceGetB())
         {
            return false;
         }
         return this.save.getDefine().canRefiningB();
      }
      
      public function getMaxPartsLevel() : int
      {
         if(this.save.isPartsNormalB())
         {
            return PartsConst.getMaxPartsLevel();
         }
         return this.save.getDefine().maxLevel;
      }
      
      public function getPartsFatherArmsData() : ArmsData
      {
         var dg0:PartsDataGroup = null;
         if(Boolean(fatherData))
         {
            dg0 = fatherData as PartsDataGroup;
            if(Boolean(dg0))
            {
               return dg0.nowArmsData;
            }
         }
         return null;
      }
      
      override public function toOneSortId(str0:String) : void
      {
         tempSortId = "";
         if(str0 == "parts")
         {
            this.toPartsSortId();
         }
      }
      
      public function toPartsSortId() : void
      {
         var d0:ThingsDefine = this.save.getDefine();
         tempSortId = TextWay.toNum(d0.index + 1 + "",3) + this.save.getLevelSortId();
      }
      
      public function getGatherTip(cDa0:ThingsData = null) : String
      {
         var levelLimit0:int = 0;
         var dayLimit0:int = 0;
         var keyCn0:String = null;
         var str0:String = "";
         var d0:ThingsDefine = this.save.getDefine();
         if(d0.canSellB)
         {
         }
         if(d0.isPartsB())
         {
            str0 += PartsCreator.getPartsGatherText(d0,this);
         }
         else
         {
            str0 += d0.getGatherTip(playerData);
         }
         if(playerData is PlayerData)
         {
            if(d0.isPropsB())
            {
               levelLimit0 = playerData.thingsBag.getLevelCanUseNum(d0.name);
               if(ThingsEffectDefine.haveLimitPan(levelLimit0))
               {
                  str0 += "\n\n关卡中还可以使用：" + TextMethod.redZeroOrGreen(levelLimit0) + "次";
               }
               dayLimit0 = playerData.thingsBag.getDayCanUseNum(d0.name);
               if(ThingsEffectDefine.haveLimitPan(dayLimit0))
               {
                  str0 += "\n\n今天还可以使用：" + TextMethod.redZeroOrGreen(dayLimit0) + "次";
               }
               keyCn0 = DoubleCtrl.getPropsKeyName(d0.name);
               if(keyCn0 != "")
               {
                  str0 = "<purple [<b>" + keyCn0 + "键</b>使用]/>" + str0;
               }
            }
            if(d0.name == ThingsName.bossSumCard)
            {
               str0 += "\n\n本周已使用次数：";
               str0 += "\n<gray 正常关卡：/>" + TextMethod.colorSurplusNum(playerData.main.save.bs,PlayerMainData.getBossSumMax());
               str0 += "\n<gray 修罗及星谷地图：/>" + TextMethod.colorSurplusNum(playerData.main.save.dembs,PlayerMainData.getDemBossSumMax());
            }
         }
         return str0;
      }
      
      public function getGatherLvText() : String
      {
         if(this.save.getDefine().isPartsB())
         {
            return this.save.getTrueLevel() + "";
         }
         return "";
      }
   }
}

