package dataAll._app.partner.ability
{
   import com.sounto.utils.TextMethod;
   import dataAll._app.partner.PartnerData;
   
   public class PartnerAbilityData
   {
      
      public var def:PartnerAbilityDefine;
      
      public var partnerData:PartnerData;
      
      public function PartnerAbilityData()
      {
         super();
      }
      
      public function inData(d0:PartnerAbilityDefine, partnerData0:PartnerData) : void
      {
         this.def = d0;
         this.partnerData = partnerData0;
      }
      
      public function getPointAddMax() : Number
      {
         var base0:Number = this.partnerData.getBasePoint(this.def.name);
         return Math.round(base0 * 0.7) + 80;
      }
      
      public function getPointAddMaxCan() : int
      {
         var now0:int = this.getPointAdd();
         var surplus0:int = this.partnerData.getPointSurplus();
         var max0:Number = this.getPointAddMax();
         var add0:Number = max0 - now0;
         if(add0 > surplus0)
         {
            add0 = surplus0;
         }
         return add0 + now0;
      }
      
      public function getPointAdd() : Number
      {
         return this.partnerData.save.getPoint(this.def.name);
      }
      
      public function getPoint() : Number
      {
         var name0:String = this.def.name;
         var base0:Number = this.partnerData.getBasePoint(name0);
         var add0:Number = this.getPointAdd();
         return base0 + add0;
      }
      
      public function getWearPointStr() : String
      {
         var name0:String = this.def.name;
         var base0:Number = this.partnerData.getBasePoint(name0);
         var add0:Number = this.getPointAdd();
         var str0:String = base0 + "";
         if(add0 > 0)
         {
            str0 += TextMethod.color("+" + add0,"#00FF00");
         }
         return str0;
      }
      
      public function addPoint() : void
      {
         this.partnerData.save.addPoint(this.def.name,1);
      }
      
      public function delPoint() : void
      {
         this.partnerData.save.addPoint(this.def.name,-1);
      }
      
      public function setPoint(v0:Number) : void
      {
         this.partnerData.save.setPoint(this.def.name,v0);
      }
   }
}

