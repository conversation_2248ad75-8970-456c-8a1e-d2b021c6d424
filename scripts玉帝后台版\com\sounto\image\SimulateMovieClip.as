package com.sounto.image
{
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import gameAll.effect.simple.IO_SimpleEffect;
   
   public class SimulateMovieClip extends Sprite implements IO_SimpleEffect
   {
      
      public var enabled:Boolean = true;
      
      public var type:String = "";
      
      public var label:String = "";
      
      public var labelType:String = "";
      
      public var father:String = "";
      
      public var isPlaying:Boolean = true;
      
      protected var _currentFrame:int = 0;
      
      protected var _totalFrames:int = 0;
      
      public var speed:int = 1;
      
      protected var now_s:int = 0;
      
      protected var lockB:Boolean = false;
      
      public function SimulateMovieClip()
      {
         super();
      }
      
      public function get currentFrame() : int
      {
         return this._currentFrame;
      }
      
      public function get totalFrames() : int
      {
         return this._totalFrames;
      }
      
      public function toDie() : void
      {
      }
      
      public function setMustScaleX(v0:Number) : void
      {
         scaleX = v0;
      }
      
      public function setMustScaleY(v0:Number) : void
      {
         scaleY = v0;
      }
      
      public function stop() : void
      {
         this.isPlaying = false;
      }
      
      public function play() : void
      {
         if(this.lockB)
         {
            return;
         }
         this.isPlaying = true;
      }
      
      public function lockFrame(f0:int) : void
      {
         this.lockB = false;
         this.gotoAndStop(f0);
         this.lockB = true;
      }
      
      public function gotoAndPlay(num:int) : *
      {
         if(this.lockB)
         {
            return;
         }
         var num0:int = num;
         if(num < 0)
         {
            num = 1;
         }
         else if(num > this._totalFrames)
         {
            num = this._totalFrames;
         }
         this._currentFrame = num0;
         this.isPlaying = true;
      }
      
      public function gotoAndStop(num:int) : *
      {
         if(this.lockB)
         {
            return;
         }
         var num0:int = num;
         if(num < 0)
         {
            num = 1;
         }
         else if(num > this._totalFrames)
         {
            num = this._totalFrames;
         }
         this._currentFrame = num0;
         this.isPlaying = false;
      }
      
      public function pause() : void
      {
         this.enabled = false;
      }
      
      public function resume() : void
      {
         this.enabled = true;
      }
      
      public function changeEvent() : void
      {
      }
      
      public function addRideSp(sp0:DisplayObject) : void
      {
      }
      
      public function addInPartSp(part0:String, sp0:DisplayObject) : void
      {
      }
      
      public function getSpInPart(part0:String, name0:String) : Sprite
      {
         return null;
      }
      
      public function FTimer(timeStopB0:Boolean = false) : void
      {
         if(this.isPlaying && this.enabled && timeStopB0 == false)
         {
            if(this.now_s % this.speed == 0)
            {
               this.now_s = 0;
               this.changeEvent();
               if(this.lockB == false)
               {
                  if(this._currentFrame >= this._totalFrames)
                  {
                     this._currentFrame = 1;
                  }
                  else
                  {
                     ++this._currentFrame;
                  }
               }
            }
            ++this.now_s;
         }
      }
      
      public function setRotation(v0:Number) : void
      {
         rotation = v0;
      }
      
      public function setX(v0:Number) : void
      {
         x = v0;
      }
      
      public function setY(v0:Number) : void
      {
         y = v0;
      }
      
      public function getX() : Number
      {
         return x;
      }
      
      public function getY() : Number
      {
         return y;
      }
      
      public function getRotation() : Number
      {
         return rotation;
      }
      
      public function getWidth() : Number
      {
         return width;
      }
      
      public function getHeight() : Number
      {
         return height;
      }
   }
}

