package dataAll._app.partner.ability
{
   public class PartnerAbility
   {
      
      public static const attack:String = "attack";
      
      public static const defence:String = "defence";
      
      public function PartnerAbility()
      {
         super();
      }
      
      public static function countAttack(dpsMul0:Number) : Number
      {
         return dpsMul0 * 100;
      }
      
      public static function getDpsMul(attack0:Number) : Number
      {
         return attack0 / 100;
      }
      
      public static function countDefence(underHurtMul0:Number) : Number
      {
         if(underHurtMul0 > 0)
         {
            return 1 / underHurtMul0 * 30;
         }
         return 0;
      }
      
      public static function getUnderHurtMul(defence0:Number) : Number
      {
         if(defence0 > 0)
         {
            return 1 / defence0 * 30;
         }
         return 1;
      }
   }
}

