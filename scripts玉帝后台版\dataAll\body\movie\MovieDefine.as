package dataAll.body.movie
{
   import com.sounto.math.IDRect;
   import dataAll.body.attack.BodyAttackDefine;
   
   public class MovieDefine
   {
      
      public static var headPointNameObj:Object = {
         "eye_right":0,
         "eye_left":0,
         "mouth":0
      };
      
      public static var bodyPointNameObj:Object = {"aircraft":0};
      
      public var label:String;
      
      public var labelType:String = "";
      
      public var father:String;
      
      public var arr:Array = [];
      
      public var specialPointObj:Object = {};
      
      public var attackMergeRect:IDRect = null;
      
      public var nowAttackDefine:BodyAttackDefine = null;
      
      public function MovieDefine()
      {
         super();
      }
      
      public function dealSoundUrl(name0:String) : void
      {
         var n:* = undefined;
         var f0:MovieFrameDefine = null;
         var arr0:Array = null;
         var type0:String = null;
         var i:* = undefined;
         var str0:String = null;
         for(n in this.arr)
         {
            f0 = this.arr[n];
            arr0 = f0.soundLabelArr;
            if(<PERSON><PERSON>an(arr0) && arr0.length > 0)
            {
               type0 = arr0[0];
               if(type0 != "shake")
               {
                  for(i in arr0)
                  {
                     str0 = arr0[i];
                     if(str0.indexOf("/") == -1)
                     {
                        arr0[i] = name0 + "/" + str0;
                     }
                  }
               }
            }
         }
      }
      
      public function getSoundUrlArr() : Array
      {
         var n:* = undefined;
         var f0:MovieFrameDefine = null;
         var arr0:Array = null;
         var type0:String = null;
         var arr1:Array = [];
         for(n in this.arr)
         {
            f0 = this.arr[n];
            arr0 = f0.soundLabelArr;
            if(Boolean(arr0) && arr0.length > 0)
            {
               type0 = arr0[0];
               if(type0 != "shake")
               {
                  arr1 = arr0.concat(arr1);
               }
            }
         }
         return arr1;
      }
   }
}

