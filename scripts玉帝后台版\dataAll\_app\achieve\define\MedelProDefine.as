package dataAll._app.achieve.define
{
   import com.sounto.utils.ClassProperty;
   
   public class MedelProDefine
   {
      
      public static var pro_arr:Array = [];
      
      public var name:String = "";
      
      public var v:Number = 0;
      
      public function MedelProDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML) : void
      {
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
      }
   }
}

