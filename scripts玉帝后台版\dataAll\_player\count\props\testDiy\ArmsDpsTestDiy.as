package dataAll._player.count.props.testDiy
{
   import UI.test.SaveTestBox;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._player.PlayerData;
   import dataAll._player.count.props.DpsCountCtrl;
   import dataAll.arms.ArmsData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.body.attack.HurtData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.level.LevelDiffGetting;
   import dataAll.level.define.unit.UnitType;
   import dataAll.level.modeDiy.ModeDiyDefine;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyData;
   import gameAll.hero.HeroBody;
   import gameAll.level.PlayMode;
   import gameAll.level.data.OverLevelShow;
   
   public class ArmsDpsTestDiy extends TestDiy
   {
      
      private static var MAX:int = 10;
      
      public static var diff:int = 11;
      
      private static var mapIndex:int = 0;
      
      private static var index:int = 0;
      
      private static var firstArms:ArmsData = null;
      
      private static var armsArr:Array = null;
      
      private var textArr:Array = [];
      
      private var next_t:Number = -1;
      
      protected var enemyDieNum:int = 0;
      
      protected var enemyDieMax:int = 40;
      
      protected var firstHurtTime:Number = -0.0001;
      
      public function ArmsDpsTestDiy()
      {
         super();
         closeEnemyAIB = false;
         noSuperB = false;
      }
      
      private static function get PD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      public static function setData(v0:int, max0:int = 0) : void
      {
         index = v0;
         if(max0 > 0)
         {
            MAX = max0;
         }
         armsArr = null;
         firstArms = null;
      }
      
      public static function startMapTest(diff0:int, firstIndex0:int = 0) : void
      {
         diff = diff0;
         armsArr = null;
         mapIndex = firstIndex0;
         gotoMapByIndex();
      }
      
      private static function gotoMapByIndex() : void
      {
         var d0:WorldMapDefine = null;
         var str0:String = null;
         var arr0:Array = Gaming.defineGroup.worldMap.getShowMapArr();
         if(mapIndex >= 0 && mapIndex < arr0.length)
         {
            d0 = arr0[mapIndex];
            if(d0.getDemonDiy() == ModeDiyDefine.CLOSE)
            {
               overMapTest();
               return;
            }
            Gaming.LG.chooseLevel(d0.name,diff,PlayMode.NORMAL,MapMode.DEMON);
            str0 = mapIndex + 1 + "：" + d0.cnName + "  " + LevelDiffGetting.getCnName(diff,MapMode.DEMON) + "===============================";
            SaveTestBox.addText(str0);
         }
         else
         {
            overMapTest();
         }
      }
      
      private static function nextMap() : void
      {
         var arr0:Array = Gaming.defineGroup.worldMap.getShowMapArr();
         if(mapIndex < arr0.length - 1)
         {
            ++mapIndex;
            gotoMapByIndex();
         }
         else
         {
            overMapTest();
         }
      }
      
      private static function overMapTest() : void
      {
         var str0:String = ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>";
         SaveTestBox.addText(str0);
         doOverLevel();
      }
      
      override public function openOrClose(enabled0:Boolean) : void
      {
         super.openOrClose(enabled0);
      }
      
      override public function startLevel() : void
      {
         super.startLevel();
         this.next_t = -1;
         this.enemyDieNum = 0;
         this.firstHurtTime = -0.0001;
         if(index == 0)
         {
            this.setArmsByIndex();
         }
      }
      
      override public function bodyAdd(b0:IO_NormalBody) : void
      {
         super.bodyAdd(b0);
         var dat0:NormalBodyData = b0.getData();
         var hero0:HeroBody = b0 as HeroBody;
         if(Boolean(hero0))
         {
            if(hero0.dat.isMainPlayerB())
            {
               hero0.dat.stateD.out_underHitB = false;
               hero0.dat.stateD.out_noUnderHurtB = true;
               hero0.skill.addSkill_byNameArr(["State_SpellImmunity"]);
               hero0.mot.toMove(-100,0);
            }
         }
         if(dat0.isEnemy())
         {
            if(!Gaming.LG.isDemonB())
            {
               b0.getSkill().addSkill_byNameArr(["noEleUnder"]);
            }
         }
         Gaming.uiGroup.gameWorldUI.bossLifeBox.fleshSkillShow();
      }
      
      override public function bodyDie(b0:IO_NormalBody, b1:IO_NormalBody, h0:HurtData) : void
      {
         var count0:String = null;
         var s0:String = null;
         var hero0:HeroBody = null;
         var arms0:ArmsData = null;
         super.bodyDie(b0,b1,h0);
         var dat0:NormalBodyData = b0.getData();
         if(dat0.isEnemy())
         {
            count0 = "";
            if(Gaming.LG.isDemonB())
            {
               if(dat0.isBossB())
               {
                  count0 = Number(nowLevel.dat.tempLevelTime - this.firstHurtTime).toFixed(1);
               }
            }
            else if(dat0.isBossB())
            {
               count0 = Number(bossLive).toFixed(1);
            }
            else if(dat0.unitType == UnitType.NORMAL)
            {
               ++this.enemyDieNum;
               if(this.enemyDieNum >= this.enemyDieMax)
               {
                  count0 = Number(nowLevel.dat.levelTime - this.firstHurtTime).toFixed(1);
               }
            }
            if(count0 != "")
            {
               s0 = "";
               hero0 = Gaming.PG.ctrlHero;
               arms0 = hero0.dat.armsData.nowData;
               s0 += arms0.getCnName();
               s0 += " " + count0;
               s0 += " " + NumberMethod.toWan(DpsCountCtrl.getMain().act.getAverage());
               SaveTestBox.addText(s0);
               Gaming.uiGroup.alertBox.textInput.showTextInput("第" + (index + 1) + "把武器",s0,this.next);
               if(this.next_t == -1)
               {
                  this.next_t = 0;
               }
            }
         }
      }
      
      override public function toHurt(b0:IO_NormalBody, b1:IO_NormalBody, h0:HurtData) : void
      {
         super.toHurt(b0,b1,h0);
         var hero0:HeroBody = b1 as HeroBody;
         if(Boolean(hero0))
         {
            if(hero0.dat.isMainPlayerB())
            {
               if(this.firstHurtTime < 0)
               {
                  this.firstHurtTime = nowLevel.dat.tempLevelTime;
               }
            }
         }
      }
      
      protected function overArmsTest() : void
      {
         index = 0;
         firstArms = null;
         doOverLevel();
         var str0:String = index + 1 + "次测试完毕================";
         SaveTestBox.addText(str0);
         if(Gaming.LG.isDemonB())
         {
            nextMap();
         }
      }
      
      private function getArmsArr() : Array
      {
         var darr0:Array = null;
         var darr2:Array = null;
         if(!armsArr)
         {
            darr0 = PD.arms.getSiteDataArray();
            darr2 = PD.armsBag.getSiteDataArray().concat([]);
            darr2.splice(MAX - darr0.length);
            armsArr = darr0.concat(darr2);
         }
         return armsArr;
      }
      
      protected function next(obj0:* = null) : void
      {
         this.backArms();
         if(index >= MAX - 1)
         {
            this.overArmsTest();
         }
         else
         {
            ++index;
            this.setArmsByIndex();
            doRestartLevel();
         }
      }
      
      protected function backArms() : void
      {
         var darr0:Array = null;
         var da0:ArmsData = null;
         if(Boolean(firstArms))
         {
            darr0 = this.getArmsArr();
            if(index >= 0 && index < darr0.length)
            {
               da0 = darr0[index];
               if(firstArms != da0)
               {
                  this.swapTo(da0,firstArms);
                  firstArms = null;
               }
            }
            else
            {
               firstArms = null;
            }
         }
      }
      
      protected function setArmsByIndex() : void
      {
         var da0:ArmsData = null;
         var first0:ArmsData = null;
         var da2:ArmsData = null;
         var darr0:Array = this.getArmsArr();
         if(index >= 0 && index < darr0.length)
         {
            da0 = darr0[index];
            first0 = darr0[0];
            if(index > 0)
            {
               this.swapTo(da0,first0);
               firstArms = first0;
            }
            else
            {
               firstArms = null;
            }
            da0.save.firstChoiceB = true;
            PD.hero.arms.setArms(da0);
            for each(da2 in PD.arms.dataArr)
            {
               if(da0 != da2)
               {
                  da2.save.firstChoiceB = false;
               }
               INIT.TRACE(da2.cnName);
            }
         }
      }
      
      protected function swapTo(da1:ArmsData, da2:ArmsData) : void
      {
         var g1:ArmsDataGroup = da1.getPlaceType() == ItemsDataGroup.PLACE_BAG ? PD.armsBag : PD.arms;
         var g2:ArmsDataGroup = da2.getPlaceType() == ItemsDataGroup.PLACE_BAG ? PD.armsBag : PD.arms;
         ArmsDataGroup.armsSwapTo(g1,g2,da1.save.site,da2.save.site);
      }
      
      override public function overLevel(from0:String) : void
      {
         super.overLevel(from0);
         if(from0 == OverLevelShow.UI_CLICK)
         {
            this.overArmsTest();
            overMapTest();
         }
      }
      
      override public function FTimer() : void
      {
         super.FTimer();
      }
      
      override public function allFTimer() : void
      {
         if(this.next_t >= 0)
         {
            this.next_t += 1 / 30;
            if(this.next_t >= 1)
            {
               this.next_t = -1;
               Gaming.uiGroup.alertBox.hide();
               this.next();
            }
         }
      }
   }
}

