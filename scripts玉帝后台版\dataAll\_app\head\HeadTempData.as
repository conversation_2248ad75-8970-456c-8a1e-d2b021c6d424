package dataAll._app.head
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.head.define.HeadConditionDefine;
   import dataAll._app.head.define.HeadDefine;
   
   public class HeadTempData
   {
      
      public var completeB:Boolean = false;
      
      public var compareStr:String = "";
      
      public var nowLifeDay:int = 0;
      
      public var isNowB:Boolean = false;
      
      public var lockB:Boolean = false;
      
      public var define:HeadDefine;
      
      private var oneSave:HeadOneSave;
      
      public function HeadTempData()
      {
         super();
      }
      
      public function inDataAffterCheck(d0:HeadDefine, s0:HeadOneSave, headSave0:HeadSave, heroLv0:int, nowTimeStr0:String) : void
      {
         if(s0 is HeadOneSave)
         {
            this.completeB = true;
            this.oneSave = s0;
            this.nowLifeDay = this.oneSave.getLifeDay(nowTimeStr0);
         }
         else
         {
            this.completeB = false;
         }
         this.define = d0;
         this.isNowB = d0.name == headSave0.nowHead;
         this.lockB = heroLv0 < d0.unlockLv;
      }
      
      public function getLifeTitleStr() : String
      {
         if(this.completeB)
         {
            return "剩余寿命";
         }
         return "寿命";
      }
      
      public function getLifeValueStr() : String
      {
         if(this.completeB)
         {
            if(this.define.haveLifeB())
            {
               return this.nowLifeDay + "天";
            }
            return "永久";
         }
         if(this.define.haveLifeB())
         {
            return this.define.life + "天";
         }
         return "永久";
      }
      
      public function inNumCompare(now0:Number, d0:HeadDefine) : void
      {
         var c0:HeadConditionDefine = d0.condition;
         var must0:Number = c0.must;
         this.completeB = now0 >= must0;
         this.compareStr = ComMethod.mustColor(now0,must0,false,"#00FF00","#FF3F00",true);
      }
      
      public function getConditionStr() : String
      {
         var str0:String = "";
         var c0:HeadConditionDefine = this.define.condition;
         str0 += c0.info;
         if(!this.completeB && c0.progressInfo != "" && this.compareStr != "")
         {
            str0 += ComMethod.color("\n进度：" + c0.progressInfo.replace("[compare]",this.compareStr),"#00FFFF");
         }
         return str0;
      }
   }
}

