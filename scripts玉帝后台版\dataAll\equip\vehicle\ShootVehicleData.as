package dataAll.equip.vehicle
{
   import dataAll._player.more.NormalPlayerData;
   import dataAll.bullet.BulletDefine;
   import dataAll.equip.EquipData;
   import dataAll.equip.save.EquipSave;
   import dataAll.items.ItemsDataGroup;
   
   public class ShootVehicleData extends VehicleData
   {
      
      protected var shootDefine:ShootVehicleDefine;
      
      protected var mainDps:Number = 1;
      
      protected var subDps:Number = 1;
      
      public function ShootVehicleData()
      {
         super();
      }
      
      override public function inData_bySave(s0:EquipSave, pd0:NormalPlayerData, dg0:ItemsDataGroup = null) : void
      {
         super.inData_bySave(s0,pd0,dg0);
         this.shootDefine = vehicleDefine as ShootVehicleDefine;
      }
      
      override public function setDps(v0:Number) : void
      {
         super.setDps(v0);
         this.mainDps = v0;
         this.subDps = v0;
      }
      
      public function getMainMul() : Number
      {
         return getMulByLabel("mainMul");
      }
      
      public function getSubMul() : Number
      {
         return getMulByLabel("subMul");
      }
      
      override public function getDpsByType(type0:String) : Number
      {
         return this[type0 + "Dps"];
      }
      
      override public function setDpsByType(type0:String, value0:Number) : void
      {
         this[type0 + "Dps"] = value0;
      }
      
      override public function clone() : EquipData
      {
         var da0:ShootVehicleData = super.clone() as ShootVehicleData;
         da0.shootDefine = this.shootDefine;
         return da0;
      }
      
      public function limitMainHurt() : Number
      {
         var maxDps0:Number = getLimitDps();
         var bulletD0:BulletDefine = this.shootDefine.main.getBulletDefine();
         var hurt0:Number = bulletD0.hurtRatio;
         return maxDps0 * hurt0 * this.getMainMul();
      }
      
      public function limitSubHurt() : Number
      {
         var maxDps0:Number = getLimitDps();
         var bulletD0:BulletDefine = this.shootDefine.sub.getBulletDefine();
         var hurt0:Number = bulletD0.hurtRatio;
         return maxDps0 * hurt0 * this.getSubMul();
      }
   }
}

