package dataAll.drop
{
   import dataAll._player.base.PlayerBaseData;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.level.define.unit.OneUnitOrderDefine;
   import gameAll.body.IO_NormalBody;
   
   public class BodyDropData
   {
      
      public static var pro_arr:Array = [];
      
      public var x:int = 0;
      
      public var y:int = 0;
      
      public var camp:String = "";
      
      public var expValue:Number = 0;
      
      public var coinValue:Number = 0;
      
      public var isTrueBossB:Boolean = false;
      
      public var bodyLevel:int = 0;
      
      public var maxLife:Number = 0;
      
      public var unitType:String = "";
      
      public var define:NormalBodyDefine;
      
      public var unitDefine:OneUnitOrderDefine;
      
      public var getDropUnitType:String = "";
      
      public var getDropB:Boolean = true;
      
      public var getDropLevel:int = 0;
      
      public var BB:IO_NormalBody;
      
      public function BodyDropData()
      {
         super();
      }
      
      public function setDropLevel(lv0:int) : void
      {
         var max0:int = PlayerBaseData.ENEMY_LEVEL;
         if(lv0 > max0)
         {
            lv0 = max0;
         }
         this.getDropLevel = lv0;
         this.bodyLevel = lv0;
      }
      
      public function inDataByObj(obj0:Object) : void
      {
         var pro0:String = null;
         for each(pro0 in pro_arr)
         {
            if(obj0.hasOwnProperty(pro0))
            {
               if(obj0[pro0] is Function)
               {
                  this[pro0] = obj0[pro0]();
               }
               else
               {
                  this[pro0] = obj0[pro0];
               }
            }
         }
         this.setDropLevel(this.bodyLevel);
      }
   }
}

