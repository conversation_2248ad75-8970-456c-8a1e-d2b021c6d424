package dataAll._app.login
{
   import com.adobe.crypto.MD5;
   import com.common.text.TextWay;
   import com.sounto.cf.CodeCF;
   import dataAll._app.top.TopBarData;
   import dataAll._data.ConstantDefine;
   
   public class LoginData4399
   {
      
      private static const zuobiNameMd5Arr:Array = ["1594368271","cs501260120","q3581","sounto101","8959390uso","buchun002","393659081","l245201536","cocduyan5","sudi920"];
      
      private static const adminMd5Arr:Array = ["sountoone","sounto101","sounto102","sounto103","sounto104","jasonjoyxi","luokey1217","cocduyan5"];
      
      private var _uid:String = "";
      
      private var _name:String = "local";
      
      public var nickName:String = "爆枪小战士";
      
      private var saveArr:Array = null;
      
      public var save:SaveBaseData4399;
      
      public function LoginData4399()
      {
         super();
         this.name = "local";
         this.uid = "0";
      }
      
      public static function staticInit() : void
      {
         var n:* = undefined;
         var m:* = undefined;
         for(n in zuobiNameMd5Arr)
         {
            zuobiNameMd5Arr[n] = MD5.hash(zuobiNameMd5Arr[n]);
         }
         for(m in adminMd5Arr)
         {
            adminMd5Arr[m] = MD5.hash(adminMd5Arr[m]);
         }
      }
      
      public function set name(str0:String) : void
      {
         this._name = TextWay.toCode32(str0);
      }
      
      public function get name() : String
      {
         return TextWay.getText32(this._name);
      }
      
      public function set uid(str0:String) : void
      {
         this._uid = TextWay.toCode32(str0);
      }
      
      public function get uid() : String
      {
         return TextWay.getText32(this._uid);
      }
      
      public function haveRoleInList(role0:String) : SaveBaseData4399
      {
         var s0:SaveBaseData4399 = null;
         for each(s0 in this.saveArr)
         {
            if(s0.index != this.save.index)
            {
               if(s0.title.indexOf(role0) > 0)
               {
                  return s0;
               }
            }
         }
         return null;
      }
      
      public function getCreateTime() : String
      {
         if(Boolean(this.save))
         {
            return this.save.create_time;
         }
         return "";
      }
      
      public function zuobiNamePan() : Boolean
      {
         return true;
      }
      
      public function adminPan() : Boolean
      {
         var str0:String = MD5.hash(this.name);
         return adminMd5Arr.indexOf(str0) >= 0;
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         this.uid = obj0["uid"];
         this.name = obj0["name"];
         if(!obj0["nickName"] || obj0["nickName"] == "")
         {
            this.nickName = ConstantDefine.playerName;
         }
         else
         {
            this.nickName = obj0["nickName"];
         }
      }
      
      public function getArenaCode() : String
      {
         return TopBarData.getArenaCode(this.uid,this.save.index,this.name);
      }
      
      public function isLoginByJS() : Boolean
      {
         var uid0:String = Gaming.api.save.getUidByCookie();
         var str0:String = Gaming.api.save.getUserNameByCookie();
         if(this.name == "local")
         {
            return true;
         }
         if(String(uid0) == this.uid && str0 == this.name)
         {
            return true;
         }
         return false;
      }
      
      public function setNowSave(s0:SaveBaseData4399) : void
      {
         this.save = s0;
      }
      
      public function inSaveArr(arr0:Array) : void
      {
         this.saveArr = arr0;
      }
      
      public function newSave(index0:int) : void
      {
         this.save = new SaveBaseData4399();
         this.save.index = index0;
      }
      
      public function panUidOrUname(uid0:String, uname0:String) : Boolean
      {
         return this.uid == uid0 || uname0 == this.name;
      }
      
      public function uidZeroB() : Boolean
      {
         return this.uid == "" || this.uid == "0" || this.uid.length <= 5;
      }
      
      public function getUid() : String
      {
         return this.uid;
      }
      
      public function getSaveIndex() : int
      {
         if(Boolean(this.save))
         {
            return this.save.getIndex();
         }
         return 0;
      }
      
      public function getNam() : String
      {
         return this.name;
      }
      
      public function getNic() : String
      {
         return this.nickName;
      }
      
      public function getAre() : String
      {
         return this.getArenaCode();
      }
      
      public function getNew() : String
      {
         var t0:String = CodeCF.toCode32("getUid");
         var t2:String = CodeCF.toCode32("getIndex");
         var t3:String = CodeCF.toCode32("uil0st");
         var a0:String = String(CodeCF.getText32("00370035003k002l00390034"));
         var b0:String = String(CodeCF.getText32("00370035003k0029003e00340035003o"));
         var i0:String = "0";
         if(Boolean(this.save))
         {
            i0 = this.save[b0]();
         }
         return MD5.hash(CodeCF.toCode32(this[a0]() + "*" + i0));
      }
   }
}

