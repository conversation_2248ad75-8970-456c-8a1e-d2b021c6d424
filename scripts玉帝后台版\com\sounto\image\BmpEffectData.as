package com.sounto.image
{
   import dataAll.bullet.BulletDefine;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.geom.Matrix;
   import flash.geom.Rectangle;
   import gameAll.bullet.image.IO_BulletImage;
   import gameAll.effect.simple.IO_SimpleEffect;
   
   public class BmpEffectData implements IO_SimpleEffect, IO_BulletImage
   {
      
      public static const DIETYPE_NO:String = "";
      
      public static const DIETYPE_NORMAL:String = "normal";
      
      public static const DIETYPE_TIME:String = "time";
      
      public static const DIETYPE_DIE:String = "die";
      
      public static const DIETYPE_TIME_DIE:String = "timeDie";
      
      public static const DIETYPE_LAST:String = "last";
      
      public static const DIETYPE_RAN_STOP:String = "ranStop";
      
      public static const DIETYPE_RAN_PLAY:String = "ranPlay";
      
      public static const DIETYPE_STOP:String = "stop";
      
      private static const instantDieArr:Array = [DIETYPE_NORMAL,DIETYPE_TIME,DIETYPE_RAN_PLAY];
      
      public var bmpArr:Array = null;
      
      public var rectArr:Array = null;
      
      public var label:String = "";
      
      public var father:String = "";
      
      public var overPanB:Boolean = false;
      
      public var x:int = 0;
      
      public var y:int = 0;
      
      public var visible:Boolean = true;
      
      public var isPlaying:Boolean = true;
      
      public var currentFrame:int = 0;
      
      public var totalFrames:int = 0;
      
      private var ra:Number = 0;
      
      private var ra_index:int = 0;
      
      private var raNum:int = 1;
      
      public var speed:int = 1;
      
      private var now_s:int = 0;
      
      public var nowBmp:BitmapData;
      
      public var nowRect:Rectangle;
      
      private var dieType:String = "normal";
      
      public var die:int = 0;
      
      private var keepB:Boolean = false;
      
      public var extraObj:Object = null;
      
      public function BmpEffectData()
      {
         super();
      }
      
      public static function getInstantDieType(type0:String) : String
      {
         if(instantDieArr.indexOf(type0) == -1)
         {
            type0 = DIETYPE_NORMAL;
         }
         return type0;
      }
      
      public function toDie() : void
      {
         this.die = 2;
      }
      
      public function canDelB() : Boolean
      {
         var delB0:Boolean = false;
         if(this.dieType == DIETYPE_NORMAL || this.dieType == DIETYPE_TIME || this.dieType == "")
         {
            if(this.currentFrame >= this.totalFrames)
            {
               delB0 = true;
            }
         }
         else if(this.die >= 2)
         {
            delB0 = true;
         }
         return delB0;
      }
      
      public function getKeepB() : Boolean
      {
         return this.keepB;
      }
      
      public function setDieType(type0:String) : void
      {
         var xx0:int = 0;
         if(type0 == "")
         {
            xx0 = 0;
         }
         this.dieType = type0;
         if(type0 == DIETYPE_RAN_STOP)
         {
            this.stopRandom();
         }
         else if(type0 == DIETYPE_RAN_PLAY)
         {
            this.playRandom();
         }
         else if(type0 == DIETYPE_STOP)
         {
            this.gotoAndStop(1);
         }
      }
      
      public function setDieTypeInstant(type0:String) : void
      {
         this.setDieType(getInstantDieType(type0));
      }
      
      public function getDieType() : String
      {
         return this.dieType;
      }
      
      public function Switch(mc:MovieClip, rotateNum:int = 1, keepB0:Boolean = false) : *
      {
         var n:int = 0;
         var i:int = 0;
         var rect0:Rectangle = null;
         var drawTarget:* = undefined;
         var mix:Matrix = null;
         var bmp0:BitmapData = null;
         var ra0:Number = NaN;
         this.keepB = keepB0;
         this.bmpArr = [];
         this.rectArr = [];
         this.totalFrames = mc.totalFrames;
         this.currentFrame = 1;
         this.raNum = rotateNum;
         var sp_con:Sprite = new Sprite();
         var sp_con2:Sprite = new Sprite();
         var bmp_con:Bitmap = new Bitmap(null,"auto",true);
         sp_con.addChild(sp_con2);
         sp_con2.addChild(bmp_con);
         for(n = 0; n <= this.totalFrames - 1; n++)
         {
            mc.gotoAndStop(n + 1);
            this.bmpArr[n] = [];
            this.rectArr[n] = [];
            for(i = 0; i < rotateNum; i++)
            {
               drawTarget = mc;
               if(i > 0)
               {
                  ra0 = i * 360 / rotateNum;
                  sp_con2.rotation = ra0;
                  drawTarget = sp_con;
                  rect0 = sp_con.getRect(sp_con);
               }
               else
               {
                  rect0 = mc.getRect(mc);
               }
               if(rect0.width == 0)
               {
                  rect0.width = 1;
               }
               if(rect0.height == 0)
               {
                  rect0.height = 1;
               }
               rect0.x = this.getInt(rect0.x);
               rect0.y = this.getInt(rect0.y);
               rect0.width = this.getInt(rect0.width);
               rect0.height = this.getInt(rect0.height);
               mix = new Matrix();
               mix.tx = -rect0.x;
               mix.ty = -rect0.y;
               bmp0 = new BitmapData(rect0.width,rect0.height,true,0);
               bmp0.draw(drawTarget,mix,null,null,null,true);
               this.bmpArr[n][i] = bmp0;
               this.rectArr[n][i] = rect0;
               if(i == 0 && rotateNum > 1)
               {
                  bmp_con.bitmapData = bmp0;
                  bmp_con.smoothing = true;
                  bmp_con.x = rect0.x;
                  bmp_con.y = rect0.y;
               }
            }
         }
      }
      
      private function getInt(num:Number) : int
      {
         var num0:int = int(num);
         var _num:int = num0;
         if(num0 > 0)
         {
            if(num0 < num)
            {
               _num = num0 + 1;
            }
         }
         else if(num0 > num)
         {
            _num = num0 - 1;
         }
         return _num;
      }
      
      public function copy() : BmpEffectData
      {
         var newBmp:BmpEffectData = new BmpEffectData();
         newBmp.bmpArr = this.bmpArr;
         newBmp.rectArr = this.rectArr;
         newBmp.currentFrame = 1;
         newBmp.totalFrames = this.totalFrames;
         newBmp.raNum = this.raNum;
         newBmp.father = this.father;
         newBmp.label = this.label;
         newBmp.overPanB = this.overPanB;
         newBmp.keepB = this.keepB;
         newBmp.extraObj = this.extraObj;
         return newBmp;
      }
      
      public function getRaNum() : Number
      {
         return this.raNum;
      }
      
      public function stop() : void
      {
         this.isPlaying = false;
      }
      
      public function play() : void
      {
         this.isPlaying = true;
      }
      
      public function gotoAndPlay(num:int) : *
      {
         this.setFrames(num);
         this.isPlaying = true;
      }
      
      public function gotoAndStop(num:int) : *
      {
         this.setFrames(num);
         this.isPlaying = false;
      }
      
      public function setFrames(num:int) : *
      {
         if(num < 1)
         {
            num = 1;
         }
         else if(num > this.totalFrames)
         {
            num = this.totalFrames;
         }
         this.currentFrame = num;
      }
      
      public function playRandom() : *
      {
         var f0:int = Math.random() * this.totalFrames + 1;
         this.gotoAndPlay(f0);
      }
      
      public function stopRandom() : *
      {
         var f0:int = Math.random() * this.totalFrames + 1;
         this.gotoAndStop(f0);
      }
      
      public function get rotation() : Number
      {
         return this.ra;
      }
      
      public function set rotation(value:Number) : void
      {
         this.ra = value % 360;
         if(this.ra < 0)
         {
            this.ra += 360;
         }
         if(this.raNum > 1)
         {
            this.ra_index = int((this.ra + 180 / this.raNum) / 360 * this.raNum) % this.raNum;
         }
         else
         {
            this.ra_index = 0;
         }
      }
      
      public function setRa(v0:Number) : void
      {
         this.rotation = v0 / Math.PI * 180;
      }
      
      public function getRa() : Number
      {
         return this.rotation / 180 * Math.PI;
      }
      
      public function showFirst() : void
      {
         this.nowBmp = this.bmpArr[this.currentFrame - 1][this.ra_index];
         this.nowRect = this.rectArr[this.currentFrame - 1][this.ra_index];
      }
      
      public function getFirstBmp() : BitmapData
      {
         if(Boolean(this.bmpArr))
         {
            return this.bmpArr[0][0];
         }
         return null;
      }
      
      public function FTimer(timeStopB0:Boolean) : void
      {
         if(this.isPlaying && this.dieType != DIETYPE_RAN_STOP && this.dieType != DIETYPE_STOP)
         {
            if(timeStopB0)
            {
               if(this.dieType != DIETYPE_TIME && this.dieType != DIETYPE_TIME_DIE)
               {
                  return;
               }
            }
            if(this.now_s % this.speed == 0)
            {
               this.now_s = 0;
               if(this.bmpArr.length > 0)
               {
                  if(this.currentFrame >= this.totalFrames)
                  {
                     if(this.dieType == DIETYPE_LAST)
                     {
                        this.currentFrame = this.totalFrames;
                     }
                     else
                     {
                        this.currentFrame = 1;
                     }
                  }
                  else
                  {
                     ++this.currentFrame;
                  }
               }
            }
            ++this.now_s;
         }
      }
      
      public function setVisible(bb0:Boolean) : void
      {
         this.visible = bb0;
      }
      
      public function inBulletDefine(d0:BulletDefine, mul0:Number = 1) : void
      {
      }
      
      public function setLength(v0:Number) : void
      {
      }
      
      public function setRotation(v0:Number) : void
      {
         this.rotation = v0;
      }
      
      public function setX(v0:Number) : void
      {
         this.x = v0;
      }
      
      public function setY(v0:Number) : void
      {
         this.y = v0;
      }
      
      public function getX() : Number
      {
         return this.x;
      }
      
      public function getY() : Number
      {
         return this.y;
      }
      
      public function getRotation() : Number
      {
         return this.rotation;
      }
      
      public function getWidth() : Number
      {
         if(Boolean(this.nowRect))
         {
            return this.nowRect.width;
         }
         return 0;
      }
      
      public function getHeight() : Number
      {
         if(Boolean(this.nowRect))
         {
            return this.nowRect.height;
         }
         return 0;
      }
   }
}

