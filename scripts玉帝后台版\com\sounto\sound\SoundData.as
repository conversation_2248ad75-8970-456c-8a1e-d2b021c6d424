package com.sounto.sound
{
   import flash.media.Sound;
   import flash.media.SoundChannel;
   import flash.media.SoundTransform;
   
   public class SoundData
   {
      
      public var father:String = "";
      
      public var label:String = "";
      
      private var s:Sound = null;
      
      private var volumeMul:Number = 1;
      
      private var tempSoundTransform:SoundTransform = new SoundTransform();
      
      private var SC:SoundChannel = null;
      
      public function SoundData(s0:Sound)
      {
         super();
         this.s = s0;
      }
      
      public function play(loopNum0:int = 1, v0:Number = 1) : *
      {
         this.tempSoundTransform.volume = v0;
         this.SC = this.s.play(0,loopNum0,this.tempSoundTransform);
      }
      
      public function playLevelLoop(v0:Number = 1) : void
      {
         this.play(9999999999,v0);
         Gaming.soundGroup.addLevelLoop(this);
      }
      
      public function playEffect(loopNum0:int = 1, st0:SoundTransform = null) : *
      {
         this.SC = this.s.play(0,loopNum0,st0);
      }
      
      public function setVolumeMul(v0:Number) : void
      {
         this.volumeMul = v0;
      }
      
      public function setNowVolume(v0:Number) : void
      {
         if(this.SC is SoundChannel)
         {
            this.SC.soundTransform = new SoundTransform(v0 * this.volumeMul);
         }
      }
      
      public function getNowVolume() : Number
      {
         if(this.SC is SoundChannel)
         {
            return this.SC.soundTransform.volume / this.volumeMul;
         }
         return 1;
      }
      
      public function havePlayB() : Boolean
      {
         return this.SC is SoundChannel;
      }
      
      public function stop() : void
      {
         if(this.SC is SoundChannel)
         {
            this.SC.stop();
            this.SC = null;
         }
      }
      
      public function clear() : *
      {
         this.s = null;
         this.tempSoundTransform = null;
      }
   }
}

