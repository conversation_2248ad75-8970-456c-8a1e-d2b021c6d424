package dataAll._app.edit
{
   import com.sounto.utils.ArrayMethod;
   import dataAll._player.PlayerData;
   
   public class EditDataGroup
   {
      
      protected var outB:Boolean = false;
      
      protected var playerData:PlayerData;
      
      protected var dataClass:Class = EditData;
      
      protected var editSave:EditSaveGroup;
      
      protected var arr:Array = [];
      
      public function EditDataGroup()
      {
         super();
      }
      
      public function setPlayerData(pd0:PlayerData, outB0:Boolean) : void
      {
         this.playerData = pd0;
         this.outB = outB0;
      }
      
      public function inData_bySave(sg0:EditSaveGroup) : void
      {
         var s0:EditSave = null;
         var da0:EditData = null;
         this.editSave = sg0;
         this.arr.length = 0;
         for each(s0 in sg0.arr)
         {
            da0 = new this.dataClass();
            da0.inData_bySave(s0,this.outB);
            this.arr.push(da0);
         }
      }
      
      public function addSave(s0:EditSave) : EditData
      {
         var da0:EditData = new this.dataClass();
         this.editSave.addSave(s0);
         da0.inData_bySave(s0,this.outB);
         this.addData(da0);
         return da0;
      }
      
      public function addData(da0:EditData) : void
      {
         this.arr.push(da0);
      }
      
      public function removeData(da0:EditData) : void
      {
         ArrayMethod.remove(this.arr,da0);
         this.editSave.removeSave(da0.getEditSave());
      }
      
      public function getDataById(id0:String) : EditData
      {
         var da0:EditData = null;
         if(id0 == "")
         {
            return null;
         }
         for each(da0 in this.arr)
         {
            if(da0.id == id0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function findDataIndex(da0:EditData) : int
      {
         return this.arr.indexOf(da0);
      }
   }
}

