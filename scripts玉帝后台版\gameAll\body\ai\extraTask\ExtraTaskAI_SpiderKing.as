package gameAll.body.ai.extraTask
{
   import gameAll.body.IO_NormalBody;
   import gameAll.body.skill.SkillData;
   import gameAll.skill.SkillEffectData;
   
   public class ExtraTaskAI_SpiderKing extends ExtraTaskAI
   {
      
      public function ExtraTaskAI_SpiderKing()
      {
         super();
      }
      
      override public function bodyAddBySkill(se0:SkillEffectData, b0:IO_NormalBody) : void
      {
         if(se0.define.name == "summonedSpider_spiderKing_extra")
         {
            b0.getData().setNewMaxLife(_dat.maxLife * se0.define.obj["lifeMul"]);
            b0.getData().setDpsFactor(_dat.dpsFactor * se0.define.obj["dpsMul"]);
         }
      }
      
      override public function bodyUseSkill(se0:SkillEffectData) : void
      {
         var da0:SkillData = null;
         var productB0:IO_NormalBody = se0.producer;
         if(productB0 is IO_NormalBody)
         {
            if(productB0.getData().camp != _dat.camp && BB.getDie() == 0)
            {
               da0 = _skill.getSkill("acidRain_SpiderKing");
               if(da0 is SkillData)
               {
                  if(da0.canUseB)
                  {
                     BB.getSkillCtrl().doSkillImmediately(da0.name);
                  }
               }
            }
         }
      }
   }
}

