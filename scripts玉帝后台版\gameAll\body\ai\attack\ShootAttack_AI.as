package gameAll.body.ai.attack
{
   import dataAll.body.img.BodyImageLabel;
   import dataAll.bullet.BulletDefine;
   import dataAll.skill.define.SkillEvent;
   import flash.geom.Rectangle;
   import gameAll.arms.ArmsLauncher;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.ai.Attack_AI;
   import gameAll.body.ai.one.OneAiData;
   import gameAll.body.ai.one.PlayerAiData;
   import gameAll.body.ai.one.ShootAiData;
   import gameAll.body.data.NormalBodyData;
   import gameAll.body.motion.GroundMotionState;
   import gameAll.body.motion.NormalGroundMotion;
   import gameAll.bullet.IO_BulletLauncher;
   
   public class ShootAttack_AI extends Attack_AI
   {
      
      public var shootOne:ShootAiData;
      
      protected var ctrlBullet:IO_BulletLauncher;
      
      protected var minGap:int = 100;
      
      private var nowShootHeadB:Boolean = false;
      
      private var changeArmsState:String = "first";
      
      private var jump_t:Number = 0;
      
      private var jump_max:Number = 0.3 + Math.random() * 1.2;
      
      public function ShootAttack_AI(_BB:IO_NormalBody)
      {
         super(_BB);
         this.ctrlBullet = BB.getCtrlBullet();
         this.shootOne = one as ShootAiData;
      }
      
      override protected function getOneAiDataClass(_BB:IO_NormalBody, ai0:Attack_AI) : OneAiData
      {
         return new ShootAiData();
      }
      
      override protected function attackTimer() : void
      {
         var mot2:NormalGroundMotion = null;
         var targetRect0:Rectangle = null;
         var mx0:int = 0;
         var my0:int = 0;
         var follow_mx0:int = 0;
         var follow_my0:int = 0;
         var len2:Number = NaN;
         var ra2:Number = NaN;
         var armsD:BulletDefine = null;
         var shootB:Boolean = false;
         var shootLen0:int = 0;
         var canShootB:Boolean = false;
         var lostAttackB0:Boolean = false;
         var x0:int = mot.x;
         var y0:int = mot.y - mot.hitRect.height / 2;
         if(img.nowLabelType == BodyImageLabel.Attack && !dat.isVehicleB())
         {
            return;
         }
         if(ai_t < AI_T)
         {
            ++ai_t;
         }
         else
         {
            this.changeArmsPan();
            mot2 = targetBody.getMot();
            targetRect0 = targetBody.getAttack().getBodyRectHave();
            if(this.nowShootHeadB && targetBody.getDefine().headHurtMul > 1)
            {
               targetRect0 = targetBody.getAttack().getHeadRect();
            }
            mx0 = targetRect0.x + targetRect0.width / 2;
            my0 = targetRect0.y + targetRect0.height * 2 / 3;
            follow_mx0 = mot2.getBeFollowMx();
            follow_my0 = mot2.getBeFollowMy();
            len2 = Math.sqrt((mx0 - x0) * (mx0 - x0) + (my0 - y0) * (my0 - y0));
            followSet_len = len2;
            ra2 = Math.atan2(y0 - my0,x0 - mx0);
            armsD = this.ctrlBullet.getNowBulletDefine();
            this.setFollowPoint(mx0,my0,true);
            shootB = false;
            followAI.setEnabled(true);
            shootLen0 = armsD.getAIShootRange(this.shootOne.precisionB) * def.shootLenMul - normalAI.OffsetIndex * 30;
            if(shootLen0 < 130)
            {
               shootLen0 = 130;
            }
            if(dat.stateD.penetrationGap >= 9999 && mot.state == GroundMotionState.stand)
            {
               shootLen0 = armsD.getAIShootRange() + 100;
            }
            nerveState = this.shootNervePan(len2,shootLen0);
            if(len2 <= shootLen0)
            {
               canShootB = this.isCanShootB(mx0,my0,x0,y0,armsD);
               if(this.isCanShootB2() && (len2 <= 150 || canShootB))
               {
                  shootB = true;
               }
               if(canShootB)
               {
                  if(len2 > this.minGap && nerveState == 1)
                  {
                     this.stopFollow();
                  }
               }
               else
               {
                  this.setFollowPoint(follow_mx0,follow_my0);
               }
            }
            if(state != NOING)
            {
               if(state == START)
               {
                  state = FOLLOWING;
                  followAI.startFollow();
               }
               else if(state == FOLLOWING)
               {
                  this.ctrlBullet.setShootPoint(mx0,my0);
                  lostAttackB0 = dat.stateD.lostAttackB;
                  if(shootB && !lostAttackB0)
                  {
                     state = SHOOT;
                  }
                  else if(normalAI.warningEnabled)
                  {
                     changeTargetPan(shootLen0);
                  }
               }
               else if(state == SHOOT)
               {
                  Gaming.TG.skill.eventTrigger(SkillEvent.beforeAttack,BB,targetBody);
                  this.ctrlBullet.mouseDown();
                  state = SHOOTING;
                  this.ctrlBullet.setShootPoint(mx0,my0);
                  if(this.shootOne.shootHeadPro > 0)
                  {
                     this.nowShootHeadB = Math.random() <= this.shootOne.shootHeadPro;
                  }
               }
               else if(state == SHOOTING)
               {
                  this.ctrlBullet.setShootPoint(mx0,my0);
                  if(overShootPan())
                  {
                     state = SHOOTOVER;
                  }
               }
               else if(state == SHOOTOVER)
               {
                  this.ctrlBullet.stopLoop();
                  waveDirect = x0 > mx0 ? 1 : -1;
                  if(shootB)
                  {
                     state = SHOOT;
                  }
                  else
                  {
                     state = START;
                  }
               }
            }
            if(state == SHOOT || state == SHOOTING || state == SHOOTOVER)
            {
               this.randomJump();
            }
         }
      }
      
      protected function isCanShootB2() : Boolean
      {
         if(this.shootOne.noFoggyB && Boolean(targetBody))
         {
            if(Boolean(targetBody.getSkill().getSkill("FoggyDefence")))
            {
               return false;
            }
         }
         return true;
      }
      
      protected function isCanShootB(mx0:int, my0:int, x0:int, y0:int, armsD:BulletDefine) : Boolean
      {
         return ArmsLauncher.canShootB(mx0,my0,x0,y0,armsD,followSet_len,dat.stateD.penetrationGap,this.shootOne.precisionB);
      }
      
      protected function setFollowPoint(x0:int, y0:int, waveB0:Boolean = false) : void
      {
         if(waveB0)
         {
            x0 += (waveState - 0.5) * 2 * this.minGap;
         }
         followAI.setTargetPoint(x0,y0);
      }
      
      protected function stopFollow() : void
      {
         followAI.stopFollow();
      }
      
      protected function shootNervePan(len0:int, shootLen0:*) : int
      {
         var armsD:BulletDefine = null;
         var aiShootRange0:Number = NaN;
         var keepGap0:Number = NaN;
         var t_dat0:NormalBodyData = null;
         var bb1:Boolean = this.getEacapeB();
         var bb2:Boolean = !this.ctrlBullet.haveCapacityB();
         var bb3:Boolean = false;
         if(one.keepAwayMoreEnemyB)
         {
            armsD = this.ctrlBullet.getNowBulletDefine();
            aiShootRange0 = armsD.getAIShootRange(this.shootOne.precisionB);
            keepGap0 = one.keepGap;
            if(keepGap0 > aiShootRange0 - 80)
            {
               keepGap0 = aiShootRange0 - 80;
            }
            bb3 = Gaming.BG.filter.getEnemyNum_Range(dat.camp,mot.x,mot.y,keepGap0,false) > one.keepNum;
         }
         var bb4:Boolean = false;
         if(targetBody is IO_NormalBody)
         {
            t_dat0 = targetBody.getData();
            if(one.awayAttackBuffB && t_dat0.stateD.isAttackBuffB() && !dat.stateD.isAttackBuffB())
            {
               bb4 = true;
            }
         }
         if(one is PlayerAiData == false)
         {
            if(one.gotoVeryAwayLifeMul > 0)
            {
               if(dat.getLifePer() < one.gotoVeryAwayLifeMul)
               {
                  one.gotoVeryAway();
               }
               else
               {
                  one.setTo(one.name);
               }
            }
         }
         var mustB0:Boolean = dat.stateD.mustEscapeB;
         isNowEscapeB = bb1 || bb4 || mustB0;
         if(bb1 || bb2 || bb3 || bb4 || mustB0)
         {
            escapeFollowSet();
            if(bb2)
            {
               this.ctrlBullet.changeCharger();
            }
            return -1;
         }
         return 1;
      }
      
      private function getEacapeB() : Boolean
      {
         var bb1:Boolean = false;
         if(one.eacapeEnabled)
         {
            if(dat.getLifePer() < one.eacapeLifeMul)
            {
               bb1 = true;
               if(one.compareEnemyLifeMul > 0)
               {
                  if(targetBody is IO_NormalBody)
                  {
                     if(targetBody.getData().getLifePer() < one.eacapeLifeMul * one.compareEnemyLifeMul)
                     {
                        bb1 = false;
                     }
                  }
               }
            }
         }
         return bb1;
      }
      
      private function randomJump() : void
      {
         if(one.jumpPro > 0)
         {
            this.jump_t += 1 / 30;
            if(this.jump_t > this.jump_max)
            {
               this.jump_t = 0;
               this.jump_max = 0.3 + Math.random() * 1.2;
               if(Math.random() < one.jumpPro)
               {
                  act.toJump();
               }
            }
         }
      }
      
      protected function changeArmsPan() : void
      {
         if(this.shootOne.changeArmsB == false)
         {
            return;
         }
         if(this.ctrlBullet.aiSwapArmsPan())
         {
            if(state == SHOOTOVER)
            {
               if(Math.random() < 0.5)
               {
                  this.randomChangeArms();
                  this.changeArmsState = ChangeArmsState.over;
               }
            }
         }
         else
         {
            if(this.ctrlBullet.isEverChargerB())
            {
               if(wave_t >= waveT - 1.5)
               {
                  wave_t += 2;
                  this.randomChangeArms();
               }
            }
            if(this.changeArmsState == ChangeArmsState.over)
            {
               this.changeArmsState = ChangeArmsState._continue;
            }
         }
      }
      
      protected function randomChangeArms() : void
      {
         this.ctrlBullet.nextArms();
      }
   }
}

