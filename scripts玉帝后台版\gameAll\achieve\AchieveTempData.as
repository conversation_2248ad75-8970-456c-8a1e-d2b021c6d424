package gameAll.achieve
{
   import com.sounto.cf.NiuBiCF;
   
   public class AchieveTempData
   {
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public function AchieveTempData()
      {
         super();
      }
      
      public function get blackArmsNum() : Number
      {
         return this.CF.getAttribute("blackArmsNum");
      }
      
      public function set blackArmsNum(v0:Number) : void
      {
         this.CF.setAttribute("blackArmsNum",v0);
      }
      
      public function get blackArmsEvo8Num() : Number
      {
         return this.CF.getAttribute("blackArmsEvo8Num");
      }
      
      public function set blackArmsEvo8Num(v0:Number) : void
      {
         this.CF.setAttribute("blackArmsEvo8Num",v0);
      }
      
      public function get darkgoldArmsNum() : Number
      {
         return this.CF.getAttribute("darkgoldArmsNum");
      }
      
      public function set darkgoldArmsNum(v0:Number) : void
      {
         this.CF.setAttribute("darkgoldArmsNum",v0);
      }
      
      public function get purgoldArmsNum() : Number
      {
         return this.CF.getAttribute("purgoldArmsNum");
      }
      
      public function set purgoldArmsNum(v0:Number) : void
      {
         this.CF.setAttribute("purgoldArmsNum",v0);
      }
      
      public function get yagoldArmsNum() : Number
      {
         return this.CF.getAttribute("yagoldArmsNum");
      }
      
      public function set yagoldArmsNum(v0:Number) : void
      {
         this.CF.setAttribute("yagoldArmsNum",v0);
      }
      
      public function get eleArms45Num() : Number
      {
         return this.CF.getAttribute("eleArms45Num");
      }
      
      public function set eleArms45Num(v0:Number) : void
      {
         this.CF.setAttribute("eleArms45Num",v0);
      }
      
      public function get blackEquipNum() : Number
      {
         return this.CF.getAttribute("blackEquipNum");
      }
      
      public function set blackEquipNum(v0:Number) : void
      {
         this.CF.setAttribute("blackEquipNum",v0);
      }
      
      public function get blackEquipEvo7Num() : Number
      {
         return this.CF.getAttribute("blackEquipEvo7Num");
      }
      
      public function set blackEquipEvo7Num(v0:Number) : void
      {
         this.CF.setAttribute("blackEquipEvo7Num",v0);
      }
      
      public function get vehicleNum() : Number
      {
         return this.CF.getAttribute("vehicleNum");
      }
      
      public function set vehicleNum(v0:Number) : void
      {
         this.CF.setAttribute("vehicleNum",v0);
      }
      
      public function get vehicle3() : Number
      {
         return this.CF.getAttribute("vehicle3");
      }
      
      public function set vehicle3(v0:Number) : void
      {
         this.CF.setAttribute("vehicle3",v0);
      }
      
      public function get deviceNum() : Number
      {
         return this.CF.getAttribute("deviceNum");
      }
      
      public function set deviceNum(v0:Number) : void
      {
         this.CF.setAttribute("deviceNum",v0);
      }
      
      public function get weaponNum() : Number
      {
         return this.CF.getAttribute("weaponNum");
      }
      
      public function set weaponNum(v0:Number) : void
      {
         this.CF.setAttribute("weaponNum",v0);
      }
      
      public function get skill13Num() : Number
      {
         return this.CF.getAttribute("skill13Num");
      }
      
      public function set skill13Num(v0:Number) : void
      {
         this.CF.setAttribute("skill13Num",v0);
      }
      
      public function get skill14() : Number
      {
         return this.CF.getAttribute("skill14");
      }
      
      public function set skill14(v0:Number) : void
      {
         this.CF.setAttribute("skill14",v0);
      }
      
      public function get petType() : Number
      {
         return this.CF.getAttribute("petType");
      }
      
      public function set petType(v0:Number) : void
      {
         this.CF.setAttribute("petType",v0);
      }
      
      public function get petStren() : Number
      {
         return this.CF.getAttribute("petStren");
      }
      
      public function set petStren(v0:Number) : void
      {
         this.CF.setAttribute("petStren",v0);
      }
      
      public function get bossCard5() : Number
      {
         return this.CF.getAttribute("bossCard5");
      }
      
      public function set bossCard5(v0:Number) : void
      {
         this.CF.setAttribute("bossCard5",v0);
      }
      
      public function get bossCard6() : Number
      {
         return this.CF.getAttribute("bossCard6");
      }
      
      public function set bossCard6(v0:Number) : void
      {
         this.CF.setAttribute("bossCard6",v0);
      }
      
      public function get bossCard7() : Number
      {
         return this.CF.getAttribute("bossCard7");
      }
      
      public function set bossCard7(v0:Number) : void
      {
         this.CF.setAttribute("bossCard7",v0);
      }
      
      public function get demonSingle() : Number
      {
         return this.CF.getAttribute("demonSingle");
      }
      
      public function set demonSingle(v0:Number) : void
      {
         this.CF.setAttribute("demonSingle",v0);
      }
      
      public function get demonSingle8() : Number
      {
         return this.CF.getAttribute("demonSingle8");
      }
      
      public function set demonSingle8(v0:Number) : void
      {
         this.CF.setAttribute("demonSingle8",v0);
      }
   }
}

