package gameAll.achieve
{
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.achieve.AchieveData;
   import dataAll._app.achieve.define.AchieveDefine;
   import dataAll._app.edit.card.BossCardData;
   import dataAll._app.task.TaskData;
   import dataAll._app.worldMap.define.WorldMapDefine;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll._player.more.NormalPlayerData;
   import dataAll.arms.ArmsData;
   import dataAll.body.attack.HurtData;
   import dataAll.equip.EquipData;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipType;
   import dataAll.equip.device.DeviceDefine;
   import dataAll.equip.vehicle.VehicleDefine;
   import dataAll.equip.weapon.WeaponDefine;
   import dataAll.level.define.unit.UnitType;
   import dataAll.level.modeDiy.ModeDiyDefine;
   import dataAll.pet.PetData;
   import dataAll.skill.HeroSkillData;
   import gameAll.body.IO_NormalBody;
   import gameAll.body.data.NormalBodyData;
   import gameAll.level.PlayMode;
   import gameAll.level.data.LevelData;
   import gameAll.scene.weather.WeatherData;
   
   public class AchieveTrigger
   {
      
      private static var check:AchieveConditionCheck = new AchieveConditionCheck();
      
      public static var tempData:AchieveTempData = new AchieveTempData();
      
      public function AchieveTrigger()
      {
         super();
      }
      
      private static function normalTrigger(trigger0:String) : void
      {
         var bb0:Boolean = false;
         var da0:AchieveData = null;
         var condition0:String = null;
         var arr0:Array = Gaming.PG.da.achieve.getArrByTrigger(trigger0);
         if(arr0 is Array)
         {
            bb0 = false;
            for each(da0 in arr0)
            {
               condition0 = da0.def.condition.type;
               if(check.hasOwnProperty(condition0))
               {
                  if(Boolean(check[condition0](da0)))
                  {
                     bb0 = true;
                  }
               }
            }
            if(bb0)
            {
               Gaming.PG.da.achieve.fleshAffterComplete();
            }
         }
      }
      
      private static function numberTrigger(trigger0:String, num0:Number) : void
      {
         var bb0:Boolean = false;
         var da0:AchieveData = null;
         var condition0:String = null;
         var arr0:Array = Gaming.PG.da.achieve.getArrByTrigger(trigger0);
         if(arr0 is Array)
         {
            bb0 = false;
            for each(da0 in arr0)
            {
               condition0 = da0.def.condition.type;
               if(check.hasOwnProperty(condition0))
               {
                  if(Boolean(check[condition0](da0,num0)))
                  {
                     bb0 = true;
                  }
               }
            }
            if(bb0)
            {
               Gaming.PG.da.achieve.fleshAffterComplete();
            }
         }
      }
      
      public static function openUI() : void
      {
         demon();
         bosscard();
         petType();
         skill13Num();
         balckArmsNum();
         blackEquipNum();
         allEquipPan();
         normalTrigger("openUI");
      }
      
      private static function demon() : void
      {
         var s0:WorldMapSave = null;
         var time0:Number = NaN;
         var time8:Number = NaN;
         var d0:int = 0;
         var d8:int = 0;
         var arr0:Array = Gaming.PG.da.worldMap.getDemonSaveArr();
         for each(s0 in arr0)
         {
            if(s0.getDemonDiyName() != ModeDiyDefine.DUEL)
            {
               time0 = s0.getFastWinTime();
               time8 = s0.getDemonWinTime(7);
               if(time0 > 0 && time0 < 31)
               {
                  d0++;
               }
               if(time8 > 0 && time8 < 31)
               {
                  d8++;
               }
            }
         }
         tempData.demonSingle = d0;
         tempData.demonSingle8 = d8;
      }
      
      private static function bosscard() : void
      {
         var da0:BossCardData = null;
         var star0:int = 0;
         tempData.bossCard5 = 0;
         tempData.bossCard6 = 0;
         tempData.bossCard7 = 0;
         var arr0:Array = Gaming.PG.da.bossCard.getDataArr();
         for each(da0 in arr0)
         {
            if(da0.isGiftID() == false)
            {
               star0 = da0.getStar();
               if(star0 == 5)
               {
                  ++tempData.bossCard5;
               }
               else if(star0 == 6)
               {
                  ++tempData.bossCard6;
               }
               else if(star0 == 7)
               {
                  ++tempData.bossCard7;
               }
            }
         }
      }
      
      private static function skill13Num() : void
      {
         var pd0:NormalPlayerData = null;
         var lv0:int = 0;
         var da0:HeroSkillData = null;
         var num0:int = 0;
         var num14:int = 0;
         var pdarr0:Array = Gaming.PG.da.getPlayerDataArr();
         for each(pd0 in pdarr0)
         {
            lv0 = 0;
            for each(da0 in pd0.skill.dataArr)
            {
               lv0 = da0.save.lv;
               if(lv0 >= 13)
               {
                  num0++;
               }
               if(lv0 >= 14)
               {
                  num14++;
               }
            }
            for each(da0 in pd0.skillBag.dataArr)
            {
               lv0 = da0.save.lv;
               if(lv0 >= 13)
               {
                  num0++;
               }
               if(lv0 >= 14)
               {
                  num14++;
               }
            }
         }
         tempData.skill13Num = num0;
         tempData.skill14 = num14;
      }
      
      private static function petType() : void
      {
         var da0:PetData = null;
         var base0:String = null;
         var ms0:int = 0;
         var arr0:Array = Gaming.PG.da.pet.arr;
         var nameArr0:Array = [];
         var maxStren0:int = 0;
         for each(da0 in arr0)
         {
            base0 = da0.getGeneDefine().getBaseName();
            ArrayMethod.addNoRepeatInArr(nameArr0,base0);
            ms0 = da0.base.getMaxStrenLv();
            if(maxStren0 < ms0)
            {
               maxStren0 = ms0;
            }
         }
         tempData.petType = nameArr0.length;
         tempData.petStren = maxStren0;
      }
      
      private static function balckArmsNum() : void
      {
         var da0:ArmsData = null;
         var color0:String = null;
         var colorIndex0:int = 0;
         var arr0:Array = Gaming.PG.da.getArmsDataArr(true,true,true);
         var num0:int = 0;
         var evo8:int = 0;
         var ele45:int = 0;
         var blackIndex0:int = EquipColor.getIndex(EquipColor.BLACK);
         var darkgoldIndex0:int = EquipColor.getIndex(EquipColor.DARKGOLD);
         var darkgoldArr0:Array = [];
         var purgoldIndex0:int = EquipColor.getIndex(EquipColor.PURGOLD);
         var purgoldArr0:Array = [];
         var yagoldIndex0:int = EquipColor.getIndex(EquipColor.YAGOLD);
         var yagoldArr0:Array = [];
         for each(da0 in arr0)
         {
            color0 = da0.getColor();
            colorIndex0 = EquipColor.getIndex(color0);
            if(colorIndex0 >= blackIndex0)
            {
               num0++;
               if(da0.save.evoLv >= 9)
               {
                  evo8++;
               }
               if(colorIndex0 >= darkgoldIndex0)
               {
                  ArrayMethod.addNoRepeatInArr(darkgoldArr0,da0.save.name);
               }
               if(colorIndex0 >= purgoldIndex0)
               {
                  ArrayMethod.addNoRepeatInArr(purgoldArr0,da0.save.name);
               }
               if(colorIndex0 >= yagoldIndex0)
               {
                  ArrayMethod.addNoRepeatInArr(yagoldArr0,da0.save.name);
               }
            }
            if(da0.getEleHurtMul() >= 0.45)
            {
               ele45++;
            }
         }
         tempData.blackArmsNum = num0;
         tempData.blackArmsEvo8Num = evo8;
         tempData.darkgoldArmsNum = darkgoldArr0.length;
         tempData.purgoldArmsNum = purgoldArr0.length;
         tempData.yagoldArmsNum = yagoldArr0.length;
         tempData.eleArms45Num = ele45;
      }
      
      private static function blackEquipNum() : void
      {
         var da0:EquipData = null;
         var arr0:Array = Gaming.PG.da.equip.dataArr;
         var num0:int = 0;
         var evo7:int = 0;
         for each(da0 in arr0)
         {
            if(da0.isNormalEquipB())
            {
               if(EquipColor.moreBlackB(da0.getColor()))
               {
                  if(da0.save.getDefine().isCanEvoB())
                  {
                     num0++;
                     if(da0.save.evoLv >= 8)
                     {
                        evo7++;
                     }
                  }
               }
            }
         }
         tempData.blackEquipNum = num0;
         tempData.blackEquipEvo7Num = evo7;
      }
      
      private static function allEquipPan() : void
      {
         var da0:EquipData = null;
         var type0:String = null;
         var vehicleD0:VehicleDefine = null;
         var vname0:String = null;
         var arr0:Array = Gaming.PG.da.getEquipDataArr(true,true,true);
         var vehicleArr0:Array = [];
         var v3Arr0:Array = [];
         var weaponArr0:Array = [];
         var deviceArr0:Array = [];
         for each(da0 in arr0)
         {
            type0 = da0.save.getChildType();
            if(type0 == EquipType.VEHICLE)
            {
               vehicleD0 = da0.save.getDefine() as VehicleDefine;
               vname0 = vehicleD0.getBaseDefine().name;
               ArrayMethod.addNoRepeatInArr(vehicleArr0,vname0);
               if(vehicleD0.evolutionLv >= 3)
               {
                  ArrayMethod.addNoRepeatInArr(v3Arr0,vname0);
               }
            }
            else if(type0 == EquipType.WEAPON)
            {
               ArrayMethod.addNoRepeatInArr(weaponArr0,(da0.save.getDefine() as WeaponDefine).baseLabel);
            }
            else if(type0 == EquipType.DEVICE)
            {
               ArrayMethod.addNoRepeatInArr(deviceArr0,(da0.save.getDefine() as DeviceDefine).baseLabel);
            }
         }
         tempData.vehicleNum = vehicleArr0.length;
         tempData.vehicle3 = v3Arr0.length;
         tempData.deviceNum = deviceArr0.length;
         tempData.weaponNum = weaponArr0.length;
      }
      
      public static function overLevel(model0:String, mapDefine0:WorldMapDefine, levelDat0:LevelData) : void
      {
         if(levelDat0.winB)
         {
            if(model0 == PlayMode.NORMAL && !levelDat0.nowTaskData)
            {
               oneAffterLevel("normalLevelWin",model0,mapDefine0,levelDat0);
            }
         }
      }
      
      private static function oneAffterLevel(trigger0:String, model0:String, mapDefine0:WorldMapDefine, levelDat0:LevelData) : void
      {
         var bb0:Boolean = false;
         var da0:AchieveData = null;
         var condition0:String = null;
         var levelB0:Boolean = false;
         var arr0:Array = Gaming.PG.da.achieve.getArrByTrigger(trigger0);
         if(arr0 is Array)
         {
            bb0 = false;
            for each(da0 in arr0)
            {
               condition0 = da0.def.condition.type;
               if(check.hasOwnProperty(condition0))
               {
                  levelB0 = check.levelPan(da0,model0,mapDefine0,levelDat0);
                  if(levelB0)
                  {
                     if(Boolean(check[condition0](da0)))
                     {
                        bb0 = true;
                     }
                  }
               }
            }
            if(bb0)
            {
               Gaming.PG.da.achieve.fleshAffterComplete();
            }
         }
      }
      
      public static function bodyDie(b0:IO_NormalBody, b1:IO_NormalBody, h0:HurtData) : void
      {
         var dat0:NormalBodyData = b0.getData();
         if(dat0.unitType == UnitType.BOSS)
         {
            if(Gaming.LG.isNormalLevelB())
            {
               oneAffterBodyDie("bossDie",b0,b1,h0);
            }
         }
         else if(dat0.isWeMainPlayerB())
         {
            oneAffterBodyDie("heroDie",b0,b1,h0);
         }
      }
      
      private static function nowHaveBossCardB() : Boolean
      {
         var propsNum0:int = Gaming.PG.da.thingsBag.getLevelUseNum("bossSumCard");
         if(propsNum0 > 0)
         {
            return true;
         }
         if(Gaming.PG.da.bossCard.addCardB)
         {
            return true;
         }
         return false;
      }
      
      private static function oneAffterBodyDie(trigger0:String, b0:IO_NormalBody, b1:IO_NormalBody, h0:HurtData) : void
      {
         var bb0:Boolean = false;
         var da0:AchieveData = null;
         var d0:AchieveDefine = null;
         var condition0:String = null;
         var enemy0:String = null;
         var levelB0:Boolean = false;
         var bodyB0:Boolean = false;
         var arr0:Array = Gaming.PG.da.achieve.getArrByTrigger(trigger0);
         var model0:String = Gaming.LG.mode;
         var mapDefine0:WorldMapDefine = Gaming.LG.getMapDefNull();
         var levelDat0:LevelData = Gaming.LG.nowLevel.dat;
         if(arr0 is Array)
         {
            bb0 = false;
            for each(da0 in arr0)
            {
               d0 = da0.def;
               if(d0.father == "seconds15")
               {
                  if(nowHaveBossCardB())
                  {
                     continue;
                  }
               }
               condition0 = d0.condition.type;
               enemy0 = d0.condition.enemy;
               if(check.hasOwnProperty(condition0))
               {
                  levelB0 = check.levelPan(da0,model0,mapDefine0,levelDat0);
                  bodyB0 = true;
                  if(enemy0 != "")
                  {
                     bodyB0 = b0.getDefine().cnName == enemy0;
                  }
                  if(levelB0 && bodyB0)
                  {
                     if(Boolean(check[condition0](da0,b0,b1,h0)))
                     {
                        bb0 = true;
                     }
                  }
               }
            }
            if(bb0)
            {
               Gaming.PG.da.achieve.fleshAffterComplete();
            }
         }
      }
      
      public static function dropOrange(b0:IO_NormalBody, num0:int) : void
      {
         var bb0:Boolean = false;
         var da0:AchieveData = null;
         var condition0:String = null;
         var arr0:Array = Gaming.PG.da.achieve.getArrByTrigger("dropOrange");
         if(arr0 is Array)
         {
            bb0 = false;
            for each(da0 in arr0)
            {
               condition0 = da0.def.condition.type;
               if(check.hasOwnProperty(condition0))
               {
                  if(Boolean(check[condition0](da0,b0,num0)))
                  {
                     bb0 = true;
                  }
               }
            }
            if(bb0)
            {
               Gaming.PG.da.achieve.fleshAffterComplete();
            }
         }
      }
      
      public static function taskComplete(taskDa0:TaskData) : void
      {
         var bb0:Boolean = false;
         var da0:AchieveData = null;
         var condition0:String = null;
         var arr0:Array = Gaming.PG.da.achieve.getArrByTrigger("taskComplete");
         if(arr0 is Array)
         {
            bb0 = false;
            for each(da0 in arr0)
            {
               condition0 = da0.def.condition.type;
               if(check.hasOwnProperty(condition0))
               {
                  if(Boolean(check[condition0](da0,taskDa0)))
                  {
                     bb0 = true;
                  }
               }
            }
            if(bb0)
            {
               Gaming.PG.da.achieve.fleshAffterComplete();
            }
         }
      }
      
      public static function addWeather(w0:WeatherData) : void
      {
         var bb0:Boolean = false;
         var da0:AchieveData = null;
         var type0:String = null;
         var arr0:Array = Gaming.PG.da.achieve.getArrByTrigger("addWeather");
         if(arr0 is Array)
         {
            bb0 = false;
            for each(da0 in arr0)
            {
               if(da0.isCompleteB() == false)
               {
                  type0 = da0.def.condition.type;
                  if(w0.define.name == type0)
                  {
                     if(w0.getMulLv() >= da0.def.condition.value)
                     {
                        bb0 = true;
                        da0.complete();
                     }
                  }
               }
            }
            if(bb0)
            {
               Gaming.PG.da.achieve.fleshAffterComplete();
            }
         }
      }
   }
}

