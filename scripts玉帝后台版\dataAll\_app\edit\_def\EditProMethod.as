package dataAll._app.edit._def
{
   import com.sounto.image.BlendModeType;
   import dataAll._app.edit.list.EditListAgent;
   import dataAll._base.IO_Define;
   import dataAll.arms.define.ArmsType;
   import dataAll.bullet.BulletDefine;
   import dataAll.bullet.BulletLineType;
   import dataAll.equip.define.EquipColor;
   
   public class EditProMethod
   {
      
      public static const sound:String = "sound";
      
      public static const skill:String = "skill";
      
      public static const boss:String = "boss";
      
      public static const gift:String = "gift";
      
      public static const armsCn:String = "armsCn";
      
      public static const num:String = "num";
      
      public static const boo:String = "boo";
      
      public static const color:String = "color";
      
      public static const bulletLineType:String = "bulletLineType";
      
      public static const blendMode:String = "blendMode";
      
      public static const bulletHitType:String = "bulletHitType";
      
      public static const armsType:String = "armsType";
      
      public static const itemsColor:String = "itemsColor";
      
      private static const editObj:Object = {};
      
      public function EditProMethod()
      {
         super();
      }
      
      public static function getChildDefine(method:String, name0:String) : IO_Define
      {
         if(method == skill)
         {
            return Gaming.defineGroup.skill.getDefine(name0);
         }
         return null;
      }
      
      public static function getCn(method0:String, name0:String) : String
      {
         if(method0 == skill)
         {
            return Gaming.defineGroup.skill.getCn(name0);
         }
         return getUIValueStringM(method0,name0);
      }
      
      public static function getUIValueStringM(method0:String, s0:String) : String
      {
         var arr0:Array = null;
         var f0:int = 0;
         var cnArr0:Array = null;
         var cn0:String = null;
         if(method0 != "")
         {
            arr0 = getArrByStringM(method0);
            if(Boolean(arr0))
            {
               f0 = int(arr0.indexOf(s0));
               if(f0 >= 0)
               {
                  cnArr0 = getCnArrByStringM(method0);
                  return cnArr0[f0];
               }
               return "";
            }
         }
         return null;
      }
      
      public static function getArrByStringM(method0:String) : Array
      {
         if(method0 == bulletLineType)
         {
            return BulletLineType.arr;
         }
         if(method0 == blendMode)
         {
            return BlendModeType.arr;
         }
         if(method0 == bulletHitType)
         {
            return BulletDefine.HIT_ARR;
         }
         if(method0 == armsType)
         {
            return ArmsType.TYPE_ARR;
         }
         if(method0 == itemsColor)
         {
            return EquipColor.TYPE_ARR;
         }
         return null;
      }
      
      public static function getCnArrByStringM(method0:String) : Array
      {
         if(method0 == bulletLineType)
         {
            return BulletLineType.cnArr;
         }
         if(method0 == blendMode)
         {
            return BlendModeType.cnArr;
         }
         if(method0 == bulletHitType)
         {
            return BulletDefine.HIT_CN_ARR;
         }
         if(method0 == armsType)
         {
            return ArmsType.getCnArrStatic();
         }
         if(method0 == itemsColor)
         {
            return EquipColor.CN_ARR2;
         }
         return null;
      }
      
      public static function getEditAgentByStringMethod(method0:String, now0:String, fatherCn0:String) : EditListAgent
      {
         var father0:String = null;
         var nameArr0:Array = null;
         var cnArr0:Array = null;
         var index0:int = 0;
         var name0:String = null;
         var cn0:String = null;
         var a0:EditListAgent = editObj[method0];
         if(a0 == null)
         {
            father0 = method0;
            nameArr0 = getArrByStringM(method0);
            cnArr0 = getCnArrByStringM(method0);
            if(Boolean(nameArr0))
            {
               a0 = new EditListAgent();
               a0.info = "选择" + fatherCn0;
               a0.inTitle([father0],[fatherCn0]);
               index0 = 0;
               for each(name0 in nameArr0)
               {
                  cn0 = cnArr0[index0];
                  a0.addNormalLast(name0,cn0,father0);
                  index0++;
               }
            }
         }
         if(Boolean(a0))
         {
            a0.clearFun();
            a0.setNoLinkArr([now0]);
            a0.createAllText();
         }
         return a0;
      }
   }
}

