package dataAll._app.achieve
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.achieve.define.AchieveFatherDefine;
   
   public class AchieveLabelShowData
   {
      
      private var fatherDef:AchieveFatherDefine = null;
      
      public var num:int = 0;
      
      public var complete:int = 0;
      
      public var per:Number = 0;
      
      public var newB:Boolean = false;
      
      public var text:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var dataArr:Array = null;
      
      public function AchieveLabelShowData(num0:int, complete0:int)
      {
         super();
         this.num = num0;
         this.complete = complete0;
         this.per = this.complete / this.num;
         this.text = this.complete + "/" + this.num;
         if(this.complete == this.num)
         {
            this.text = ComMethod.color(this.text,"#00FF00");
         }
      }
      
      public function inFatherName(name0:String) : void
      {
         this.fatherDef = Gaming.defineGroup.achieve.getFatherDefine(name0);
         this.name = this.fatherDef.name;
         this.cnName = this.fatherDef.cnName;
      }
      
      public function inMedelData(f0:AchieveFatherDefine, dataArr0:Array) : void
      {
         this.fatherDef = f0;
         this.name = f0.name;
         this.cnName = f0.cnName;
         this.dataArr = dataArr0;
      }
      
      public function getLabelCn() : String
      {
         var f0:AchieveFatherDefine = this.fatherDef;
         if(Boolean(f0))
         {
            return f0.getLabelCn();
         }
         return this.cnName;
      }
   }
}

