package dataAll._player.count
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.IntegerCF;
   import com.sounto.oldUtils.NumberCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.level.define.unit.UnitType;
   
   public class NormalPlayerCountSave
   {
      
      public static var pro_arr:Array = [];
      
      protected var V:Number = Math.random() / 5 + 0.01;
      
      protected var CF:NiuBiCF = new NiuBiCF();
      
      protected var nCF:NumberCF = new NumberCF();
      
      protected var iCF:IntegerCF = new IntegerCF();
      
      private var _bulletNum:Number = 0;
      
      public function NormalPlayerCountSave()
      {
         super();
         this.killNum = 0;
         this.killBossNum = 0;
         this.bulletNum = 0;
         this.dieNum = 0;
      }
      
      public function set killNum(v0:Number) : void
      {
         this.CF.setAttribute("killNum",v0);
      }
      
      public function get killNum() : Number
      {
         return Number(this.CF.getAttribute("killNum"));
      }
      
      public function set dieNum(v0:Number) : void
      {
         this.CF.setAttribute("dieNum",v0);
      }
      
      public function get dieNum() : Number
      {
         return Number(this.CF.getAttribute("dieNum"));
      }
      
      public function set killBossNum(v0:Number) : void
      {
         this.CF.setAttribute("killBossNum",v0);
      }
      
      public function get killBossNum() : Number
      {
         return Number(this.CF.getAttribute("killBossNum"));
      }
      
      public function set bulletNum(v0:Number) : void
      {
         this._bulletNum = v0 / this.V;
      }
      
      public function get bulletNum() : Number
      {
         return Math.round(this._bulletNum * this.V);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function addTrueKillEnemyNum(num0:int, unitType0:*, headB0:Boolean = false) : void
      {
         this.killNum += num0;
         if(unitType0 == UnitType.BOSS)
         {
            this.killBossNum += num0;
         }
      }
      
      public function addDieNum(v0:int) : void
      {
         this.dieNum += v0;
      }
   }
}

