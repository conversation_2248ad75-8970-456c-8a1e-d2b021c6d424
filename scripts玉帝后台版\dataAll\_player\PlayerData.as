package dataAll._player
{
   import UI.count.CountCtrl;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ObjectMethod;
   import dataAll._app.achieve.AchieveDataGroup;
   import dataAll._app.active.ActiveData;
   import dataAll._app.arena.ArenaData;
   import dataAll._app.ask.AskData;
   import dataAll._app.blackMarket.BlackMarketData;
   import dataAll._app.city.CityData;
   import dataAll._app.edit.arms.ArmsTorDataGroup;
   import dataAll._app.edit.boss.BossEditDataGroup;
   import dataAll._app.edit.card.BossCardDataGroup;
   import dataAll._app.food.FoodData;
   import dataAll._app.goods.GoodsDataGroup;
   import dataAll._app.goods.define.PriceType;
   import dataAll._app.head.HeadData;
   import dataAll._app.love.LoveData;
   import dataAll._app.parts.PartsDataGroup;
   import dataAll._app.peak.PeakData;
   import dataAll._app.post.PostData;
   import dataAll._app.setting.key.SettingKeySave;
   import dataAll._app.space.SpaceData;
   import dataAll._app.task.TaskDataGroup;
   import dataAll._app.top.define.TopBarDefineGroup;
   import dataAll._app.top.player.PlayerTopUploadData;
   import dataAll._app.tower.TowerData;
   import dataAll._app.union.UnionData;
   import dataAll._app.vip.VipData;
   import dataAll._app.wilder.WilderDataGroup;
   import dataAll._app.worldMap.WorldMapDataGroup;
   import dataAll._player.base.PlayerMainData;
   import dataAll._player.define.MainPlayerType;
   import dataAll._player.define.PlayerType;
   import dataAll._player.more.MoreData;
   import dataAll._player.more.MoreDataGroup;
   import dataAll._player.more.MoreWay;
   import dataAll._player.more.NormalPlayerData;
   import dataAll._player.more.NormalPlayerSave;
   import dataAll._player.more.SimulateMoreData;
   import dataAll._player.realName.RealNameAgent;
   import dataAll._player.role.RoleName;
   import dataAll._player.state.PlayerStateData;
   import dataAll._player.supple.PlayerDataSupple;
   import dataAll._player.time.TimeData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.body.define.HeroDefine;
   import dataAll.drop.DropData;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.add.IO_EquipAddGetter;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.vehicle.VehicleData;
   import dataAll.gift.GiftData;
   import dataAll.gift.define.GiftType;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.level.TempLevel;
   import dataAll.level.modeDiy.ModeDiyDefine;
   import dataAll.must.define.MustDefine;
   import dataAll.must.define.MustSimpleThingsDefine;
   import dataAll.pet.PetData;
   import dataAll.pet.PetDataGroup;
   import dataAll.pet.gene.GeneDataGroup;
   import dataAll.test.ZuoBiPaner;
   import dataAll.things.ThingsDataGroup;
   import dataAll.things.define.ThingsDefine;
   import dataAll.things.define.ThingsEffectDefine;
   import gameAll.body.IO_NormalBody;
   import gameAll.drop.BodyDropCtrl;
   import gameAll.drop.bodyDrop.FoolDayBodyDrop;
   import gameAll.hero.HeroBody;
   import gameAll.level.Levels;
   import gameAll.level.data.LevelData;
   
   public class PlayerData extends NormalPlayerData
   {
      
      public var main:PlayerMainData = new PlayerMainData();
      
      public var armsBag:ArmsDataGroup = new ArmsDataGroup();
      
      public var armsHouse:ArmsDataGroup = new ArmsDataGroup();
      
      public var equipBag:EquipDataGroup = new EquipDataGroup();
      
      public var equipHouse:EquipDataGroup = new EquipDataGroup();
      
      public var thingsBag:ThingsDataGroup = new ThingsDataGroup();
      
      public var partsBag:PartsDataGroup = new PartsDataGroup();
      
      public var more:MoreDataGroup = new MoreDataGroup();
      
      public var moreBag:MoreDataGroup = new MoreDataGroup();
      
      public var geneBag:GeneDataGroup = new GeneDataGroup();
      
      public var worldMap:WorldMapDataGroup = new WorldMapDataGroup();
      
      public var task:TaskDataGroup = new TaskDataGroup();
      
      public var goods:GoodsDataGroup = new GoodsDataGroup();
      
      public var time:TimeData = new TimeData();
      
      public var realName:RealNameAgent = new RealNameAgent();
      
      public var state:PlayerStateData = new PlayerStateData();
      
      public var vip:VipData = new VipData();
      
      public var arena:ArenaData = new ArenaData();
      
      public var blackMarket:BlackMarketData = new BlackMarketData();
      
      public var achieve:AchieveDataGroup = new AchieveDataGroup();
      
      public var pet:PetDataGroup = new PetDataGroup();
      
      public var ask:AskData = new AskData();
      
      public var union:UnionData = new UnionData();
      
      public var drop:DropData = new DropData();
      
      public var active:ActiveData = new ActiveData();
      
      public var head:HeadData = new HeadData();
      
      public var post:PostData = new PostData();
      
      public var wilder:WilderDataGroup = new WilderDataGroup();
      
      public var peak:PeakData = new PeakData();
      
      public var city:CityData = new CityData();
      
      public var food:FoodData = new FoodData();
      
      public var bossEdit:BossEditDataGroup = new BossEditDataGroup();
      
      public var armsTor:ArmsTorDataGroup = new ArmsTorDataGroup();
      
      public var bossCard:BossCardDataGroup = new BossCardDataGroup();
      
      public var space:SpaceData = new SpaceData();
      
      public var tower:TowerData = new TowerData();
      
      public var gift:GiftData = new GiftData();
      
      public var player:PlayerUserData = new PlayerUserData();
      
      public var moreWay:MoreWay = new MoreWay();
      
      public var zuobiPaner:ZuoBiPaner = new ZuoBiPaner();
      
      private var firstSaveB:Boolean = true;
      
      private var allNameObj:Object = null;
      
      public function PlayerData()
      {
         super();
         this.zuobiPaner.PD = this;
         equip.keepBodyEquipShowB = false;
         this.armsBag.placeType = ItemsDataGroup.PLACE_BAG;
         this.equipBag.placeType = ItemsDataGroup.PLACE_BAG;
         this.moreBag.placeType = ItemsDataGroup.PLACE_BAG;
         this.armsHouse.placeType = ItemsDataGroup.PLACE_HOUSE;
         this.equipHouse.placeType = ItemsDataGroup.PLACE_HOUSE;
         bagNameArr = bagNameArr.concat(["armsBag","equipBag","thingsBag","partsBag","more","moreBag","geneBag"]);
      }
      
      public function TTPlayerData() : void
      {
         var pd0:NormalPlayerData = null;
         var arr0:Array = this.getPlayerDataArr();
         for each(pd0 in arr0)
         {
            pd0.testPlayerData();
         }
      }
      
      override protected function getIGArr() : Array
      {
         return [arms,equip,skill,skillBag,this.armsBag,this.armsHouse,this.equipBag,this.equipHouse,this.thingsBag,this.partsBag,this.more,this.moreBag];
      }
      
      public function getErrorStr() : String
      {
         return this.main.getErrorStr();
      }
      
      public function setNewP1NameUI(name0:String) : Boolean
      {
         var bb0:Boolean = false;
         var now0:String = this.main.save.role;
         if(name0 != now0)
         {
            bb0 = this.setNewP1Name(name0);
            if(bb0)
            {
               if(this.main.save.before == "")
               {
                  this.main.save.before = now0;
               }
               ++this.main.save.swapNum;
               this.main.save.addHavedP1(name0);
            }
         }
         return bb0;
      }
      
      public function getBeforeRole() : String
      {
         if(this.main.save.before == "")
         {
            return getRoleName();
         }
         return this.main.save.before;
      }
      
      public function setNewP1NameNewSave(name0:String) : Boolean
      {
         var mda0:MoreData = null;
         var bb0:Boolean = false;
         if(name0 != getRoleName())
         {
            mda0 = this.getMoreDataByName(name0);
            if(mda0 == null)
            {
               mda0 = this.addMoreByUnitName(name0,true);
            }
            if(Boolean(mda0))
            {
               bb0 = this.setNewP1(mda0.DATA);
               if(bb0)
               {
                  this.moreWay.initPartnerNameArr(RoleName.getNewPartnerArr(name0));
                  this.moreWay.allPartnerFight();
               }
            }
         }
         return false;
      }
      
      public function setNewP1Name(name0:String) : Boolean
      {
         var mda0:MoreData = null;
         if(name0 != getRoleName())
         {
            mda0 = this.getMoreDataByName(name0);
            if(Boolean(mda0))
            {
               return this.setNewP1(mda0.DATA);
            }
         }
         return false;
      }
      
      private function setNewP1(pd0:NormalPlayerData) : Boolean
      {
         var beforePlayerName0:String = null;
         var obj0:Object = null;
         var saveObj0:Object = null;
         if(pd0 != this)
         {
            heroData.swap(pd0.heroData);
            this.main.save.role = getRoleName();
            beforePlayerName0 = saveGroup.base.playerName;
            obj0 = getSwapObj();
            saveObj0 = saveGroup.getSwapObj();
            inSwapObj(pd0,pd0.getNormalSave());
            pd0.inSwapObj(obj0,saveObj0);
            this.moreWay.setMoreDataPlayerCtrl(heroData,true);
            saveGroup.base.playerName = pd0.base.save.playerName;
            pd0.base.save.playerName = beforePlayerName0;
            PlayerDataSupple.armsSitePan(this);
            this.fleshAllByEquip();
            pd0.fleshAllByEquip();
            return true;
         }
         return false;
      }
      
      override public function fleshReference(moreDa0:MoreData) : void
      {
         super.fleshReference(moreDa0);
         this.main.setPlayerData(this);
         this.armsBag.setPlayerData(this);
         this.armsHouse.setPlayerData(this);
         this.equipBag.setPlayerData(this);
         this.equipHouse.setPlayerData(this);
         this.thingsBag.setPlayerData(this);
         this.partsBag.setPlayerData(this);
         this.more.setPlayerData(this);
         this.moreBag.setPlayerData(this);
         this.geneBag.setPlayerData(this);
         this.pet.setPlayerData(this);
         this.union.setPlayerData(this);
         this.wilder.playerData = this;
         this.vip.playerData = this;
         this.worldMap.playerData = this;
         this.goods.playerData = this;
         this.task.playerData = this;
         this.blackMarket.setPlayerData(this);
         this.moreWay.PD = this;
         this.achieve.setPlayerData(this);
         this.arena.playerData = this;
         this.active.playerData = this;
         this.head.playerData = this;
         this.gift.playerData = this;
         this.post.playerData = this;
         this.time.playerData = this;
         this.city.setPlayerData(this);
         this.food.setPlayerData(this);
         this.bossEdit.setPlayerData(this,false);
         this.bossCard.setPlayerData(this);
         this.space.playerData = this;
      }
      
      public function getSaveTitle() : String
      {
         var s0:String = base.save.playerName + " lv." + base.save.level;
         if(this.task.isCompleteB("Hospital5_plot"))
         {
            s0 += getRoleName();
         }
         return s0;
      }
      
      override public function initSave(heroData0:MoreData, firstLv0:int = 0) : void
      {
         super.initSave(heroData0);
         this.main.addCoin(50 * 10);
         this.thingsBag.addDataByName("lifeBottle",3 * 2);
         this.thingsBag.addDataByName("caisson",3 * 2);
         this.addMoreByUnitName(RoleName.WenJie,false);
         this.firstSaveB = false;
      }
      
      public function saveYesEvent() : Boolean
      {
         if(!this.firstSaveB)
         {
            this.firstSaveB = true;
            return true;
         }
         return false;
      }
      
      override public function inData_bySave(s0:NormalPlayerSave) : void
      {
         var save0:PlayerSave = s0 as PlayerSave;
         if(mainPlayerType == PlayerType.me)
         {
            this.armsTor.inData_bySave(save0.armsTor);
         }
         super.inData_bySave(save0);
         this.main.inData_bySave(save0.main);
         this.armsBag.inData_bySaveGroup(save0.armsBag);
         this.equipBag.inData_bySaveGroup(save0.equipBag);
         this.thingsBag.inData_bySaveGroup(save0.thingsBag);
         this.partsBag.inData_bySaveGroup(save0.partsBag);
         this.more.inData_bySaveGroup(save0.more);
         this.moreBag.inData_bySaveGroup(save0.moreBag);
         this.pet.inData_bySaveGroup(save0.pet);
         this.vip.inData_bySave(save0.vip);
         this.arena.inData_bySave(save0.arena);
         this.head.inData_bySave(save0.head);
         this.post.inData_bySave(save0.post);
         this.peak.inData_bySave(save0.peak);
         if(mainPlayerType == PlayerType.me)
         {
            this.gift.inData_bySave(save0.gift);
            this.city.inData_bySave(save0.city);
            this.food.inData_bySave(save0.food);
            this.bossEdit.inData_bySave(save0.bossEdit);
            this.bossCard.inData_bySave(save0.bossCard);
            this.space.inData_bySave(save0.space);
            this.tower.inData_bySave(save0.tower);
            this.geneBag.inData_bySaveGroup(save0.geneBag);
            this.armsHouse.inData_bySaveGroup(save0.armsHouse);
            this.equipHouse.inData_bySaveGroup(save0.equipHouse);
            this.worldMap.inData_bySaveGroup(save0.worldMap);
            this.task.inData_bySaveGroup(save0.task);
            this.time.inData_bySave(save0.time);
            this.state.inData_bySave(save0.state);
            this.blackMarket.inData_bySave(save0.blackMarket);
            this.achieve.inData_bySaveGroup(save0.achieve);
            this.ask.inData_bySave(save0.ask);
            this.union.inData_bySave(save0.union);
            this.drop.inData_bySave(save0.drop);
            this.active.inData_bySave(save0.active);
            this.goods.inData_bySave(save0.goods);
            this.wilder.inData_bySaveGroup(save0.wilder);
            this.initSaveBySave(save0);
            this.goods.initData();
            save0.setting.fleshVolume();
            PlayerDataSupple.supple(this);
         }
      }
      
      protected function initSaveBySave(save0:PlayerSave) : void
      {
         var n:* = undefined;
         var pro0:String = null;
         var obj0:Object = null;
         var funName0:String = null;
         var arr0:Array = save0.getInputNoDataNameArr();
         for(n in arr0)
         {
            pro0 = arr0[n];
            if(this.hasOwnProperty(pro0))
            {
               obj0 = this[pro0];
               funName0 = pro0.indexOf("Bag") > 0 ? "initBagSave" : "initSave";
               if(obj0.hasOwnProperty(funName0))
               {
                  obj0[funName0]();
               }
            }
         }
      }
      
      override public function setMainPlayerType(type0:String) : void
      {
         super.setMainPlayerType(type0);
         this.more.setMainPlayerType(type0);
         this.moreBag.setMainPlayerType(type0);
      }
      
      override public function addExp(v0:Number) : Boolean
      {
         var overExp0:Number = NaN;
         var nowLv0:int = 0;
         var beforeLv0:int = level;
         var uplevelB0:Boolean = super.addExp(v0);
         if(mainPlayerType == "me")
         {
            overExp0 = base.getOverExp();
            if(overExp0 > 0)
            {
               this.peak.addExp(overExp0);
            }
            nowLv0 = level;
            if(nowLv0 > beforeLv0)
            {
               Gaming.TG.player.uplevel(beforeLv0,nowLv0);
            }
         }
         return uplevelB0;
      }
      
      public function getCrrency(name0:String) : Number
      {
         if(name0 == PriceType.COIN)
         {
            return this.main.save.coin;
         }
         if(name0 == PriceType.MONEY)
         {
            return this.main.money;
         }
         if(name0 == PriceType.SCORE)
         {
            return this.main.save.score;
         }
         if(name0 == PriceType.ANNI_COIN)
         {
            return this.main.save.anniCoin;
         }
         if(name0 == PriceType.TENCOIN)
         {
            return this.main.save.tenCoin;
         }
         if(name0 == PriceType.PARTSCOIN)
         {
            return this.main.save.partsC;
         }
         if(name0 == PriceType.PUMPKIN)
         {
            return this.main.getPumpkin();
         }
         if(PriceType.thingsArr.indexOf(name0) >= 0)
         {
            return this.thingsBag.getThingsNum(name0);
         }
         return 0;
      }
      
      public function compareCrrency(price0:Number, priceType0:String) : Boolean
      {
         var v0:Number = this.getCrrency(priceType0);
         return v0 >= price0;
      }
      
      public function useCrrency(price0:Number, priceType0:String) : void
      {
         if(priceType0 == PriceType.COIN)
         {
            this.main.useCoin(price0);
         }
         else if(priceType0 == PriceType.SCORE)
         {
            this.main.addScore(-price0);
         }
         else if(priceType0 == PriceType.ANNI_COIN)
         {
            this.main.addAnniCoin(-price0);
         }
         else if(priceType0 == PriceType.TENCOIN)
         {
            this.main.addTenCoin(-price0);
         }
         else if(priceType0 == PriceType.PARTSCOIN)
         {
            this.main.addPartsCoin(-price0);
         }
         else if(priceType0 == PriceType.PUMPKIN)
         {
            this.main.addPumpkin(-price0);
         }
         else if(PriceType.thingsArr.indexOf(priceType0) >= 0)
         {
            this.thingsBag.useThings(priceType0,price0,false,false);
            if(priceType0 == PriceType.ARENA_STAMP)
            {
               this.getSave().getCount().todayArenaStamp = this.getSave().getCount().todayArenaStamp + price0;
            }
            else if(priceType0 == PriceType.EXPLOIT_CARDS)
            {
               this.getSave().getCount().todayExploitCards = this.getSave().getCount().todayExploitCards + price0;
            }
         }
      }
      
      public function getTopUploadData(type_d0:TopBarDefineGroup, initScore0:Number = -1) : PlayerTopUploadData
      {
         var da0:PlayerTopUploadData = new PlayerTopUploadData();
         da0.inData_byPlayerData(type_d0,this,initScore0);
         return da0;
      }
      
      public function fleshAllNameObj() : void
      {
         this.allNameObj = this.getAllNameObj();
      }
      
      public function getNameNum(name0:String) : int
      {
         if(Boolean(this.allNameObj))
         {
            if(this.allNameObj.hasOwnProperty(name0))
            {
               return this.allNameObj[name0];
            }
         }
         return 0;
      }
      
      private function getAllNameObj() : Object
      {
         var obj0:Object = {};
         this.inBaseNameObjByArr(this.getArmsDataArr(true,true,true,true),obj0);
         return obj0;
      }
      
      private function inBaseNameObjByArr(arr0:Array, obj0:Object) : void
      {
         var da0:IO_ItemsData = null;
         var name0:String = null;
         var armsDa0:ArmsData = null;
         var yaName0:String = null;
         for each(da0 in arr0)
         {
            name0 = da0.getSave().name;
            ObjectMethod.addNum(obj0,name0,1);
            armsDa0 = da0 as ArmsData;
            if(Boolean(armsDa0))
            {
               if(armsDa0.color == EquipColor.YAGOLD)
               {
                  yaName0 = armsDa0.save.name + "Ya";
                  ObjectMethod.addNum(obj0,yaName0,1);
               }
            }
         }
      }
      
      public function repairInBagAndHouse(dataArr0:Array, type0:String) : void
      {
         var da2:IO_ItemsData = null;
         var bag0:ItemsDataGroup = this[type0 + "Bag"];
         var house0:ItemsDataGroup = this[type0 + "House"];
         for each(da2 in dataArr0)
         {
            if(bag0.spaceSiteOf() != -1)
            {
               bag0.addData(da2,true);
            }
            else if(house0.spaceSiteOf() != -1)
            {
               house0.addData(da2,true);
               da2.getSave().setInHouseTime(this.time.getReadTimeDate().getStr());
            }
            da2.newB = true;
         }
      }
      
      override public function getAllDataArrByItemsType(type0:String, houseB0:Boolean = false) : Array
      {
         var dg0:ItemsDataGroup = this[type0];
         var dg2:ItemsDataGroup = this[type0 + "Bag"];
         var dg3:ItemsDataGroup = null;
         if(houseB0)
         {
            dg3 = this[type0 + "House"];
         }
         var arr0:Array = [];
         if(Boolean(dg0))
         {
            arr0 = arr0.concat(dg0.dataArr);
         }
         if(Boolean(dg2))
         {
            arr0 = arr0.concat(dg2.dataArr);
         }
         if(Boolean(dg3))
         {
            arr0 = arr0.concat(dg3.dataArr);
         }
         if(arr0.length == 0)
         {
            return null;
         }
         return arr0;
      }
      
      override public function findArmsData(da0:ArmsData, houseB0:Boolean = false) : ArmsDataGroup
      {
         var dg0:ArmsDataGroup = arms.haveData(da0) ? arms : null;
         if(!(dg0 is ArmsDataGroup))
         {
            dg0 = this.armsBag.haveData(da0) ? this.armsBag : null;
         }
         if(!(dg0 is ArmsDataGroup))
         {
            dg0 = this.more.findArmsData(da0);
         }
         if(!(dg0 is ArmsDataGroup))
         {
            dg0 = this.moreBag.findArmsData(da0);
         }
         if(houseB0)
         {
            if(!(dg0 is ArmsDataGroup))
            {
               dg0 = this.armsHouse.haveData(da0) ? this.armsHouse : null;
            }
         }
         return dg0;
      }
      
      override public function findEquipData(da0:EquipData, houseB0:Boolean = false) : EquipDataGroup
      {
         var dg0:EquipDataGroup = equip.haveData(da0) ? equip : null;
         if(!(dg0 is EquipDataGroup))
         {
            dg0 = this.equipBag.haveData(da0) ? this.equipBag : null;
         }
         if(!(dg0 is EquipDataGroup))
         {
            dg0 = this.more.findEquipData(da0);
         }
         if(!(dg0 is EquipDataGroup))
         {
            dg0 = this.moreBag.findEquipData(da0);
         }
         if(houseB0)
         {
            if(!(dg0 is EquipDataGroup))
            {
               dg0 = this.equipHouse.haveData(da0) ? this.equipHouse : null;
            }
         }
         return dg0;
      }
      
      public function findItemsDataFist(da0:IO_ItemsData, houseB0:Boolean = false) : ItemsDataGroup
      {
         if(da0 is ArmsData)
         {
            return this.findArmsData(da0 as ArmsData,houseB0);
         }
         if(da0 is EquipData)
         {
            return this.findEquipData(da0 as EquipData,houseB0);
         }
         return null;
      }
      
      public function findEquipDataByName(name0:String, houseB0:Boolean = false) : EquipData
      {
         var da0:EquipData = null;
         var tda0:EquipData = null;
         var arr0:Array = this.getAllDataArrByItemsType("equip",true);
         for each(da0 in arr0)
         {
            if(da0.save.name == name0)
            {
               return da0;
            }
         }
         tda0 = this.more.findEquipDataByName(name0);
         if(Boolean(tda0))
         {
            return tda0;
         }
         return this.moreBag.findEquipDataByName(name0);
      }
      
      public function getBagSpaceByType(type0:String) : int
      {
         if(type0 == GiftType.bossCard)
         {
            return this.bossCard.getBagSurplus();
         }
         if(type0 == "no")
         {
            return 9999;
         }
         if(type0 == GiftType.base)
         {
            return 9999;
         }
         return this[type0 + "Bag"].getSpaceSiteNum();
      }
      
      public function getHouseSpaceByType(type0:String) : int
      {
         return this[type0 + "House"].getSpaceSiteNum();
      }
      
      public function getBagFillB() : Boolean
      {
         if(this.armsBag.getSpaceSiteNum() == 0)
         {
            return true;
         }
         if(this.equipBag.getSpaceSiteNum() == 0)
         {
            return true;
         }
         if(this.thingsBag.getSpaceSiteNum() == 0)
         {
            return true;
         }
         if(this.partsBag.getSpaceSiteNum() == 0)
         {
            return true;
         }
         if(this.geneBag.getSpaceSiteNum() == 0)
         {
            return true;
         }
         return false;
      }
      
      override public function getItemsDataGroupArr() : Array
      {
         var md0:MoreData = null;
         var arr0:Array = super.getItemsDataGroupArr();
         var moreArr0:Array = this.more.dataArr.concat(this.moreBag.dataArr);
         for each(md0 in moreArr0)
         {
            arr0 = arr0.concat(md0.DATA.getItemsDataGroupArr());
         }
         return arr0;
      }
      
      override public function getAllMustArenaStampNum() : Number
      {
         var dg0:ItemsDataGroup = null;
         var num0:Number = 0;
         var dgArr0:Array = [arms,equip,this.armsBag,this.equipBag,this.armsHouse,this.equipHouse,this.more,this.moreBag];
         for each(dg0 in dgArr0)
         {
            num0 += dg0.getAllMustArenaStampNum();
         }
         return num0;
      }
      
      override public function getAllItemsDataGroupArr() : Array
      {
         var arr0:Array = [arms,equip,this.armsBag,this.equipBag,this.armsHouse,this.equipHouse];
         arr0 = arr0.concat(this.more.getAllItemsDataGroupArr());
         return arr0.concat(this.moreBag.getAllItemsDataGroupArr());
      }
      
      public function findItemsDataByName(name0:String, class0:Class = null) : Array
      {
         var dg0:ItemsDataGroup = null;
         var da0:IO_ItemsData = null;
         var arr0:Array = [];
         var dgArr0:Array = this.getAllItemsDataGroupArr();
         for each(dg0 in dgArr0)
         {
            for each(da0 in dg0.dataArr)
            {
               if(da0.getSave().name == name0)
               {
                  if(!class0 || da0 is class0)
                  {
                     arr0.push(da0);
                  }
               }
            }
         }
         return arr0;
      }
      
      override public function findArenaGiftItemsData(str0:String) : IO_ItemsData
      {
         var dg0:ItemsDataGroup = null;
         var da0:IO_ItemsData = null;
         var dgArr0:Array = [arms,equip,this.armsBag,this.equipBag,this.armsHouse,this.equipHouse,this.more,this.moreBag];
         for each(dg0 in dgArr0)
         {
            da0 = dg0.findArenaGiftItemsData(str0);
            if(da0 is IO_ItemsData)
            {
               return da0;
            }
         }
         return null;
      }
      
      override public function getGunImageNameArr() : Array
      {
         return arms.saveGroup.getArmsImageArr().concat(this.armsBag.saveGroup.getArmsImageArr());
      }
      
      public function getPlayerGunImageNameArr(bagB0:Boolean = false, moreB0:Boolean = false, houseB0:Boolean = false) : Array
      {
         var arr0:Array = arms.saveGroup.getArmsImageArr().concat([]);
         if(bagB0)
         {
            arr0 = arr0.concat(this.armsBag.saveGroup.getArmsImageArr());
         }
         if(houseB0)
         {
            arr0 = arr0.concat(this.armsHouse.saveGroup.getArmsImageArr());
         }
         if(moreB0)
         {
            arr0 = arr0.concat(this.more.getAllPlayerGunImageNameArr());
            if(bagB0)
            {
               arr0 = arr0.concat(this.moreBag.getAllPlayerGunImageNameArr());
            }
         }
         return arr0;
      }
      
      public function getNowGunImageNameArr() : Array
      {
         if(mainPlayerType == MainPlayerType.ME)
         {
            return this.getPlayerGunImageNameArr(true,true);
         }
         if(mainPlayerType == MainPlayerType.OTHER)
         {
            return this.getPlayerGunImageNameArr(false,true);
         }
         return [];
      }
      
      override public function getArmsDataArr(bagB0:Boolean = false, moreB0:Boolean = false, moreBagB0:Boolean = false, houseB0:Boolean = false) : Array
      {
         var nowArr0:Array = arms.dataArr.concat([]);
         if(bagB0)
         {
            nowArr0 = nowArr0.concat(this.armsBag.dataArr);
         }
         if(houseB0)
         {
            nowArr0 = nowArr0.concat(this.armsHouse.dataArr);
         }
         if(moreB0)
         {
            nowArr0 = nowArr0.concat(this.more.getAllPlayerArmsDataArr());
         }
         if(moreBagB0)
         {
            nowArr0 = nowArr0.concat(this.moreBag.getAllPlayerArmsDataArr());
         }
         return nowArr0;
      }
      
      public function getNowArmsDataArr() : Array
      {
         if(mainPlayerType == MainPlayerType.ME)
         {
            return this.getArmsDataArr(true,true,true);
         }
         if(mainPlayerType == MainPlayerType.OTHER)
         {
            return this.getArmsDataArr(false,true,false);
         }
         return [];
      }
      
      public function getArmsData_MaxDpsByType(type0:String) : ArmsData
      {
         var arr0:Array = this.getArmsDataArr(true,true,true);
         return ArmsDataGroup.getData_MaxDpsByType(arr0,type0);
      }
      
      public function getArmsArrByColorMore(color0:String) : Array
      {
         var da0:ArmsData = null;
         var nr0:Array = [];
         var arr0:Array = this.getArmsDataArr(true,true,true,false);
         for each(da0 in arr0)
         {
            if(EquipColor.moreColorPan(da0.getColor(),color0))
            {
               nr0.push(da0);
            }
         }
         return nr0;
      }
      
      override public function getEquipDataArr(bagB0:Boolean = false, moreB0:Boolean = false, moreBagB0:Boolean = false, onlyNormalB0:Boolean = false, houseB0:Boolean = false) : Array
      {
         var nowArr2:Array = null;
         var da0:EquipData = null;
         var nowArr0:Array = equip.dataArr.concat([]);
         if(bagB0)
         {
            nowArr0 = nowArr0.concat(this.equipBag.dataArr);
         }
         if(houseB0)
         {
            nowArr0 = nowArr0.concat(this.equipHouse.dataArr);
         }
         if(moreB0)
         {
            nowArr0 = nowArr0.concat(this.more.getAllPlayerEquipDataArr());
         }
         if(moreBagB0)
         {
            nowArr0 = nowArr0.concat(this.moreBag.getAllPlayerEquipDataArr());
         }
         if(onlyNormalB0)
         {
            nowArr2 = [];
            for each(da0 in nowArr0)
            {
               if(da0.canRemakeB())
               {
                  nowArr2.push(da0);
               }
            }
            return nowArr2;
         }
         return nowArr0;
      }
      
      public function getAllUseVehicleDataArr() : Array
      {
         var da0:EquipData = null;
         var arr0:Array = this.getEquipDataArr(true,true,true,false);
         var arr2:Array = [];
         for each(da0 in arr0)
         {
            if(da0 is VehicleData)
            {
               arr2.push(da0);
            }
         }
         return arr2;
      }
      
      public function getEquipArrByFather(father0:String) : Array
      {
         var da0:EquipData = null;
         var nr0:Array = [];
         var arr0:Array = this.getEquipDataArr(true,true,true,false,false);
         for each(da0 in arr0)
         {
            if(da0.save.getDefine().father == father0)
            {
               nr0.push(da0);
            }
         }
         return nr0;
      }
      
      public function getEquipArrByColorMore(color0:String) : Array
      {
         var da0:EquipData = null;
         var nr0:Array = [];
         var arr0:Array = this.getEquipDataArr(true,true,true,false,false);
         for each(da0 in arr0)
         {
            if(EquipColor.moreColorPan(da0.getColor(),color0))
            {
               nr0.push(da0);
            }
         }
         return nr0;
      }
      
      public function getArenaDps(moreB0:Boolean) : Number
      {
         if(moreB0)
         {
            return getDps() + this.more.getAllDps();
         }
         return getDps();
      }
      
      public function getArenaLife(moreB0:Boolean) : Number
      {
         if(moreB0)
         {
            return base.getMaxLife() + this.more.getAllLife();
         }
         return base.getMaxLife();
      }
      
      public function fleshMaxDps() : void
      {
         var dps0:Number = getDps();
         this.main.fleshMaxDps(dps0);
      }
      
      override public function getDpsWholeAdd() : Number
      {
         return getHeroMerge().dpsWhole;
      }
      
      public function getThingsNum(name0:String) : Number
      {
         var things_d0:ThingsDefine = Gaming.defineGroup.things.getDefine(name0);
         if(things_d0.isPartsB())
         {
            return this.partsBag.getThingsNum(name0);
         }
         return this.thingsBag.getThingsNum(name0);
      }
      
      public function useThingsByMustDefine(d0:MustDefine) : void
      {
         var td0:MustSimpleThingsDefine = null;
         var name0:String = null;
         var mustNum0:int = 0;
         var things_d0:ThingsDefine = null;
         var arr0:Array = d0.getThingsArr();
         if(Boolean(arr0))
         {
            for each(td0 in arr0)
            {
               name0 = td0.name;
               mustNum0 = int(td0.num);
               if(d0.thingsType == MustDefine.THINGS)
               {
                  things_d0 = Gaming.defineGroup.things.getDefine(name0);
                  if(things_d0.isPartsB())
                  {
                     this.partsBag.useThings(name0,mustNum0,false,false);
                  }
                  else
                  {
                     this.thingsBag.useThings(name0,mustNum0,false,false);
                  }
               }
               else if(d0.thingsType == MustDefine.NUM_EQUIP)
               {
                  this.equipBag.useNumEquip(name0,mustNum0);
               }
            }
         }
      }
      
      public function getThingsLevelUseLimitNum(name0:String) : int
      {
         var v0:int = 0;
         var levelNum0:int = 0;
         var level0:Levels = null;
         var limit0:int = 0;
         var canVipB0:Boolean = false;
         var diyD0:ModeDiyDefine = null;
         var vipAdd0:int = 0;
         var d0:ThingsDefine = Gaming.defineGroup.things.getDefine(name0);
         if(d0 is ThingsDefine)
         {
            v0 = 0;
            if(Gaming.LG.canPropsB())
            {
               levelNum0 = -1;
               level0 = Gaming.LG.nowLevel;
               if(Boolean(level0))
               {
                  levelNum0 = level0.dat.getSetPropsNum(name0);
               }
               if(levelNum0 >= 0)
               {
                  v0 = levelNum0;
               }
               else
               {
                  limit0 = d0.effectD.getLevelUseLimitNum(Gaming.LG.mode);
                  if(ThingsEffectDefine.haveLimitPan(limit0))
                  {
                     canVipB0 = d0.effectD.canAddVipB(Gaming.LG.mode);
                     if(canVipB0)
                     {
                        vipAdd0 = this.vip.def.getPropsValueByName(name0);
                        limit0 += vipAdd0;
                     }
                     diyD0 = Gaming.LG.getModelDiyDefineNull();
                     if(Boolean(diyD0))
                     {
                        limit0 = diyD0.getPropsLimit(limit0,d0);
                     }
                     v0 = limit0;
                  }
                  else
                  {
                     v0 = ThingsEffectDefine.NO_LIMIT;
                  }
               }
            }
            return v0;
         }
         return 0;
      }
      
      override public function getAllSkillLabelArr(equipB0:Boolean = true, skillB0:Boolean = true, mapMode0:String = "regular", skillCnRange0:Array = null, skillNumLimit0:int = -1) : Array
      {
         var arr0:Array = super.getAllSkillLabelArr(equipB0,skillB0,mapMode0,skillCnRange0,skillNumLimit0);
         if(equipB0)
         {
            arr0 = arr0.concat(this.state.getSkillLabelArr(mapMode0));
            arr0 = arr0.concat(this.more.getMainSkillArr());
            arr0 = arr0.concat(this.union.getSkillLabelArr());
            arr0 = arr0.concat(this.peak.getSkillLabelArr());
         }
         return arr0;
      }
      
      public function event_AffterLoadSave() : void
      {
         if(mainPlayerType == "other")
         {
            this.vip.fleshBySave();
         }
         this.fleshAllByEquip();
         fillAllData();
         this.more.event_AffterLoadSave();
         this.moreBag.event_AffterLoadSave();
         if(mainPlayerType == "me")
         {
            this.task.overGamingClear();
         }
      }
      
      override public function fleshAllByEquip() : void
      {
         super.fleshAllByEquip();
      }
      
      override public function setHero(hero0:HeroBody, lg0:IO_PlayerLevelGetter) : void
      {
         super.setHero(hero0,lg0);
         this.armsBag.clearCapacity();
      }
      
      override public function newDayCtrl(timeStr0:String) : void
      {
         var timeDa0:StringDate = null;
         if(mainPlayerType == "me")
         {
            if(!Gaming.isLocal())
            {
               CountCtrl.sendCount4399();
            }
            timeDa0 = new StringDate(timeStr0);
            super.newDayCtrl(timeStr0);
            this.main.newDayCtrl(timeStr0);
            this.vip.newDayCtrl(timeStr0);
            this.worldMap.newDayCtrl(timeStr0);
            this.task.newDayCtrl(timeStr0);
            this.gift.newDayCtrl(timeStr0);
            this.city.newDayCtrl(timeDa0);
            this.food.newDayCtrl(timeDa0);
            this.getSave().getCount().newDayCtrl();
            this.arena.newDayCtrl(timeStr0);
            this.blackMarket.newDayCtrl();
            this.ask.newDayCtrl(timeStr0);
            this.time.newDayCtrl();
            this.union.newDayCtrl(timeDa0);
            this.drop.newDayCtrl();
            this.active.newDayCtrl();
            this.goods.newDayCtrl();
            this.bossEdit.newDayCtrl(timeDa0);
            this.bossCard.newDayCtrl(timeDa0);
            this.space.newDayCtrl(timeDa0);
            this.wilder.newDayCtrl(timeStr0);
            this.post.newDayCtrl(timeStr0);
            this.peak.newDayCtrl(timeStr0);
            this.pet.newDayCtrl(timeStr0);
            this.more.newDayCtrl(timeStr0);
            this.moreBag.newDayCtrl(timeStr0);
            this.itemsDataNewDayCtrl(timeStr0);
            this.getSave().headCount.newDayCtrl(timeStr0);
            this.getSave().getCount().newDayCtrl();
            this.head.newDayCtrl(timeStr0);
            TempLevel.clearData();
         }
      }
      
      private function itemsDataNewDayCtrl(timeStr0:String) : void
      {
         var dg0:ItemsDataGroup = null;
         var dgArr0:Array = this.getAllItemsDataGroupArr();
         for each(dg0 in dgArr0)
         {
            dg0.newDayCtrl(timeStr0);
         }
         this.thingsBag.newDayCtrl(timeStr0);
      }
      
      public function getFightBodyNameArr(lg0:IO_PlayerLevelGetter) : Array
      {
         var limitArr0:Array = null;
         var arr0:Array = [];
         if(Boolean(heroData))
         {
            arr0.push(heroData.def.name);
         }
         if(lg0.noAnyParnterB() == false)
         {
            limitArr0 = lg0.getLimitParnterNameArr();
            arr0 = arr0.concat(this.more.getNameArr(limitArr0));
            if(lg0.haveParnterB())
            {
               arr0 = arr0.concat(this.moreBag.getNameArr(limitArr0));
            }
         }
         if(lg0.canPetB())
         {
            arr0 = arr0.concat(this.pet.getFightAndSuppleNameArr());
         }
         return arr0;
      }
      
      public function getAllFightBodyArr(petB0:Boolean = true, sumBossB0:Boolean = false) : Array
      {
         var da0:MoreData = null;
         var arr0:Array = [];
         var moreArr0:Array = this.getMoreDataArr();
         for each(da0 in moreArr0)
         {
            if(Boolean(da0.DATA.hero))
            {
               arr0.push(da0.DATA.hero);
            }
         }
         if(petB0)
         {
            arr0 = arr0.concat(this.pet.getFightAndSuppleBodyArr());
            arr0 = arr0.concat(this.bossCard.getFightBodyArr());
         }
         if(sumBossB0)
         {
            arr0 = arr0.concat(this.bossEdit.getSummonBodyArr());
         }
         return arr0;
      }
      
      public function getLoveData(roleName0:String) : LoveData
      {
         var da0:LoveData = this.more.getLoveData(roleName0);
         if(!da0)
         {
            da0 = this.moreBag.getLoveData(roleName0);
         }
         return da0;
      }
      
      public function getGirlLoveNull() : LoveData
      {
         return this.getLoveData(RoleName.Girl);
      }
      
      public function overLevel(dat0:LevelData) : void
      {
         this.more.overLevel(dat0);
         this.moreBag.overLevel(dat0);
      }
      
      public function levelWin(lg0:IO_PlayerLevelGetter) : void
      {
         var levelDa0:LevelData = lg0.getLevelDataNull();
         if(Boolean(levelDa0) && levelDa0.winB)
         {
            base.nowCountSave.inData_byLevelData(levelDa0);
         }
         if(lg0.getTaskData() == null)
         {
            this.getSave().headCount.inCountSaveWhenWin(base.nowCountSave,levelDa0.winB);
         }
         this.worldMap.saveGroup.winOne(lg0.getNowWorldMapName(),lg0.getDiff(),lg0.getMapMode(),base.nowCountSave);
         if(lg0.isNormalLevelB())
         {
            ++this.getSave().getCount().normalLevelNum;
            this.getSave().gift.normalLevelWin();
         }
         this.space.levelWin(lg0);
      }
      
      public function getSayRoleCnObj() : Object
      {
         var role0:String = null;
         var cn0:String = null;
         var obj0:Object = {};
         var arr0:Array = RoleName.arr;
         for each(role0 in arr0)
         {
            cn0 = this.getRoleCnByName(role0);
            obj0["@" + role0] = cn0;
         }
         obj0["@playerName"] = getSayCn();
         return obj0;
      }
      
      public function getRoleCnByName(name0:String) : String
      {
         var d0:HeroDefine = Gaming.defineGroup.body.getHeroDefine(name0);
         return d0.getRoleCn();
      }
      
      public function getCpRole() : String
      {
         return RoleName.getCpRole(getRoleName());
      }
      
      public function isCpB(pd0:NormalPlayerData) : Boolean
      {
         if(pd0 != this)
         {
            if(this.getCpRole() == pd0.getRoleName())
            {
               return true;
            }
         }
         return false;
      }
      
      public function isDoubleB() : Boolean
      {
         return this.more.getPlayerCtrlNum() >= 1;
      }
      
      public function getDoubleInitSecRole() : String
      {
         if(this.main.save.role == RoleName.Striker)
         {
            return RoleName.WenJie;
         }
         return RoleName.Striker;
      }
      
      public function getPlayerDataArr() : Array
      {
         return [this].concat(this.more.getPlayerDataArr().concat(this.moreBag.getPlayerDataArr()));
      }
      
      public function getPlayerDataArrCondition(noParnterB0:Boolean) : Array
      {
         if(noParnterB0)
         {
            return [this];
         }
         return this.getPlayerDataArr();
      }
      
      public function getMoreDieNum() : int
      {
         return this.more.getDieNum() + this.moreBag.getDieNum();
      }
      
      public function getHeroArr() : Array
      {
         var arr0:Array = this.more.getHeroBodyArr();
         if(hero is HeroBody)
         {
            arr0.unshift(hero);
         }
         return arr0;
      }
      
      public function getSortMoreDataArr() : Array
      {
         return [this.more.heroData].concat(this.more.getSiteDataArray().concat(this.moreBag.getSiteDataArray()));
      }
      
      public function getUIMoreDataArr() : Array
      {
         return SimulateMoreData.getNewArr(this.getSortMoreDataArr());
      }
      
      public function getMoreDataArr() : Array
      {
         return [this.more.heroData].concat(this.more.dataArr.concat(this.moreBag.dataArr));
      }
      
      public function getMoreNum() : int
      {
         return this.more.dataArr.length + this.moreBag.dataArr.length;
      }
      
      public function getMoreDataByName(name0:String) : MoreData
      {
         var da0:MoreData = this.more.getDataBySaveName(name0) as MoreData;
         if(!(da0 is MoreData))
         {
            da0 = this.moreBag.getDataBySaveName(name0) as MoreData;
         }
         return da0;
      }
      
      public function addMoreByUnitName(name0:String, bagB0:Boolean = true) : MoreData
      {
         var da0:MoreData = null;
         if(this.more.spaceSiteOf() == -1)
         {
            bagB0 = true;
         }
         if(this.main.save.role != name0)
         {
            da0 = this.getMoreDataByName(name0);
            if(!(da0 is MoreData))
            {
               if(bagB0)
               {
                  return this.moreBag.addByUnitName(name0);
               }
               return this.more.addByUnitName(name0);
            }
         }
         return null;
      }
      
      public function rebirthAllMore() : void
      {
         var b0:HeroBody = null;
         var arr0:Array = this.getHeroArr();
         for each(b0 in arr0)
         {
            if(b0.getDie() > 0)
            {
               b0.getDieCtrl().rebirth();
            }
         }
      }
      
      public function addExtraExp(exp0:Number, b0:IO_NormalBody) : void
      {
         var da0:PetData = null;
         var pd0:NormalPlayerData = null;
         var arr0:Array = this.getPlayerDataArr();
         var arr_len0:int = int(arr0.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            pd0 = arr0[i];
            if(pd0.hero != b0)
            {
               if(pd0.hero is HeroBody)
               {
                  pd0.hero.dat.addExp(Math.ceil(exp0 * 0.5));
               }
               else
               {
                  pd0.addExp(Math.ceil(exp0 * 0.3));
               }
            }
         }
         var petArr0:Array = this.pet.getFightAndSupplePetDataArr();
         for each(da0 in petArr0)
         {
            if(da0.tempBody is IO_NormalBody && da0.tempBody != b0)
            {
               if(da0.tempBody.getDie() == 0)
               {
                  da0.tempBody.getData().addExp(exp0 * 0.5);
               }
            }
         }
      }
      
      public function addAllExp(exp0:Number) : void
      {
         var da0:PetData = null;
         var pd0:NormalPlayerData = null;
         var arr0:Array = this.getPlayerDataArr();
         var arr_len0:int = int(arr0.length);
         for(var i:int = 0; i < arr_len0; i++)
         {
            pd0 = arr0[i];
            pd0.addExp(exp0);
         }
         var petArr0:Array = this.pet.arr;
         for each(da0 in petArr0)
         {
            da0.base.addExp(exp0 * 0.5);
         }
      }
      
      public function addCoinInLevel(v0:Number) : Number
      {
         v0 *= 1 + getDropMerge().coinMul;
         v0 += getDropMerge().coin;
         v0 = Math.ceil(v0);
         this.main.addCoin(v0);
         return v0;
      }
      
      public function addAnniCoinInLevel(v0:Number) : void
      {
         this.main.addAnniCoin(v0);
      }
      
      override public function getEquipAddGetter(name0:String) : IO_EquipAddGetter
      {
         if(this.hasOwnProperty(name0))
         {
            return this[name0];
         }
         return null;
      }
      
      public function setNowReadTime(str0:String) : void
      {
         var newDayB0:Boolean = false;
         var weekB0:Boolean = false;
         var week6:Boolean = false;
         if(mainPlayerType == "me")
         {
            newDayB0 = this.time.setNowReadTime(str0);
            weekB0 = this.time.panNewWeekB(str0);
            week6 = this.time.panNewWeek6(str0);
            FoolDayBodyDrop.setNowReadTime(str0,newDayB0);
            this.more.setReadSaveTime(this.time);
            this.city.setReadSaveTime(this.time);
            this.worldMap.setReadSaveTime(this.time);
            this.gift.setNowReadTime(str0);
            if(newDayB0)
            {
               this.newDayCtrl(str0);
            }
            if(weekB0)
            {
               this.newWeek(str0);
            }
            if(week6)
            {
               this.newWeek6(str0);
            }
         }
      }
      
      public function newWeek(str0:String) : void
      {
         this.drop.newWeek();
         this.worldMap.newWeek(str0);
         this.main.newWeek();
         this.union.newWeek(str0);
         this.bossEdit.newWeek();
         this.bossCard.newWeek();
         this.space.newWeek(str0);
         this.task.newWeek();
         this.gift.newWeek(str0);
      }
      
      public function newWeek6(str0:String) : void
      {
         this.union.newWeek6(str0);
      }
      
      public function beforeStartLevel(pg0:IO_PlayerLevelGetter) : void
      {
      }
      
      public function startLevel(pg0:IO_PlayerLevelGetter) : void
      {
         base.nowCountSave.initData();
         this.thingsBag.clearAllUseNum();
         equip.startLevel(pg0);
         this.equipBag.startLevel(pg0);
         if(mainPlayerType == MainPlayerType.ME)
         {
            this.task.startLevel();
            this.more.startLevel();
            this.bossCard.startLevelEvent();
            this.space.startLevel();
         }
      }
      
      public function overGamingClear() : void
      {
         this.pet.overGamingClear();
         this.bossEdit.overGamingClear();
         this.bossCard.overGamingClear();
         this.space.overGamingClear();
         if(mainPlayerType == MainPlayerType.ME)
         {
            this.task.overGamingClear();
         }
         this.thingsBag.clearAllUseNum();
      }
      
      public function overLevelEvent(lg0:IO_PlayerLevelGetter) : void
      {
         this.bossEdit.overLevelEvent();
         this.union.overLevelEvent(lg0);
      }
      
      public function getMeKeySave() : SettingKeySave
      {
         return this.getSave().setting.key.getKeySave(getKeyPlayerType());
      }
      
      public function getPlayerStateDataArr(lg0:IO_PlayerLevelGetter) : Array
      {
         var arr0:Array = [];
         arr0 = arr0.concat(this.food.getPlayerStateDataArr(lg0));
         arr0 = arr0.concat(this.time.getPlayerStateDataArr());
         arr0 = arr0.concat(this.union.building.getPlayerStateDataArr());
         return arr0.concat(this.state.getPlayerStateDataArr());
      }
      
      public function getSurplusSweepingNum() : int
      {
         var num0:int = this.moreWay.get_sweepingNum() + this.post.getSweepingNum() + this.main.save.daySweeping;
         num0 += BodyDropCtrl.getActiveAdd_sweeping();
         num0 -= this.worldMap.saveGroup.sweepingNum;
         if(num0 < 0)
         {
            num0 = 0;
         }
         return num0;
      }
      
      public function getLotteryNum() : int
      {
         return 2 + this.post.getLotteryNum();
      }
      
      public function FTimerSecond(lg0:IO_PlayerLevelGetter) : void
      {
         var timeStopB0:Boolean = Gaming.targetInput.timeStopB;
         this.union.FTimerSecond(lg0);
         this.state.FTimerSecond(lg0,timeStopB0);
         this.food.FTimerSecond(lg0.isOnlyIng(),lg0.canFoodB(),timeStopB0);
      }
      
      override public function zuobiPan(otherPlayerB0:Boolean = false) : String
      {
         var str0:String = equip.zuobiPan();
         var str1:String = this.equipBag.zuobiPan();
         var str2:String = this.more.zuobiPan();
         var str3:String = this.moreBag.zuobiPan();
         var str4:String = this.thingsBag.zuobiPan();
         if(this.vip.def.must >= 5000)
         {
            str4 = "";
         }
         var str5:String = this.partsBag.zuobiPan();
         var str6:String = arms.zuobiPan();
         var str7:String = this.armsBag.zuobiPan();
         var str8:String = "";
         var str9:String = base.zuobiPan();
         if(this.vip.def.must >= 1000)
         {
            str9 = "";
         }
         var str10:String = this.wilder.zuobiPan();
         var str11:String = "";
         var str12:String = this.arena.zuobiPan();
         var str13:String = "";
         var str14:String = "";
         if(otherPlayerB0)
         {
            str4 = "";
            str9 = "";
            str11 = "";
         }
         else
         {
            str13 = this.zuobiPaner.zuobiPan();
         }
         if(this.main.save.score > 2000000)
         {
            this.main.save.score = 0;
         }
         if(this.arena.save.score > 2000000)
         {
            this.arena.save.score = 0;
         }
         if(str0 == "" && str1 == "" && str2 == "" && str3 == "" && str4 == "" && str5 == "" && str6 == "" && str7 == "" && str8 == "" && str9 == "" && str10 == "" && str11 == "" && str12 == "" && str13 == "" && str14 == "")
         {
            return "";
         }
         return str0 + "," + str1 + "," + str2 + "," + str3 + "," + str4 + "," + str5 + "," + str6 + "," + str7 + "," + str8 + "," + str9 + "," + str10 + "," + str11 + "," + str12 + "," + str13 + "," + str14;
      }
      
      public function getSave() : PlayerSave
      {
         return saveGroup as PlayerSave;
      }
      
      override public function checkNormalPlayerData() : void
      {
         super.checkNormalPlayerData();
         this.thingsBag.checkNormalPlayerData(this);
         this.equipBag.checkNormalPlayerData(this);
         this.armsBag.checkNormalPlayerData(this);
         this.partsBag.checkNormalPlayerData(this);
         this.geneBag.checkNormalPlayerData(this);
         this.more.checkNormalPlayerData(this);
         this.moreBag.checkNormalPlayerData(this);
      }
      
      public function haveGiftHomeArray() : Array
      {
         var arr0:Array = [];
         if(this.head.save.panHaveHead("specialSoldiers"))
         {
            arr0.push("specialSoldiers");
         }
         if(this.head.save.panHaveHead("superHero"))
         {
            arr0.push("superHero");
         }
         if(Boolean(this.findEquipDataByName("xiaoAi")))
         {
            arr0.push("xiaoAi");
         }
         if(Boolean(this.findEquipDataByName("xiaoBo")))
         {
            arr0.push("xiaoBo");
         }
         return arr0;
      }
   }
}

