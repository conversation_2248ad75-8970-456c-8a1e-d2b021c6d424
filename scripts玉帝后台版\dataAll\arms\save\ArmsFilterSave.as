package dataAll.arms.save
{
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import com.sounto.utils.ObjectMethod;
   import dataAll._app.edit._def.EditProDefine;
   import dataAll._app.edit._def.EditProGather;
   import dataAll.arms.define.ArmsColor;
   import dataAll.arms.define.ArmsPro;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.arms.define.ArmsType;
   import dataAll.equip.define.EquipColor;
   import dataAll.pro.ProType;
   
   public class ArmsFilterSave extends FilterSave
   {
      
      public static var pro_arr:Array = null;
      
      public var bb:Boolean = true;
      
      public function ArmsFilterSave()
      {
         super();
         setProValue(ArmsPro.color,[EquipColor.BLACK]);
         setProValue(ArmsPro.armsType,[ArmsType.laser]);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         o = ClassProperty.copyObj(obj0["o"]);
      }
      
      public function getTitle() : String
      {
         var type0:String = this.getType();
         if(type0 != "")
         {
            return ArmsType.getCn(type0);
         }
         return "无";
      }
      
      public function getType() : String
      {
         var typeArr0:Array = this.getTypeArr();
         if(typeArr0.length >= 1)
         {
            return typeArr0[0];
         }
         return "";
      }
      
      public function getTypeArr() : Array
      {
         return getProValueArr(ArmsPro.armsType);
      }
      
      public function getColorArr() : Array
      {
         return getProValueArr(ArmsPro.color);
      }
      
      public function getMaxColor() : String
      {
         return EquipColor.getMaxColor(this.getColorArr());
      }
      
      public function panArmsSave(s0:ArmsSave) : Boolean
      {
         var pro0:String = null;
         var d0:EditProDefine = null;
         var rangeB0:Boolean = false;
         var now0:Number = NaN;
         var min0:Number = NaN;
         var max0:Number = NaN;
         var arr0:Array = null;
         var now2:* = undefined;
         var nowArr0:Array = null;
         var mustB0:Boolean = false;
         var haveNum0:int = 0;
         var must0:String = null;
         INIT.tempTrace(s0.name + " ------------  ");
         if(this.getType() == "")
         {
            INIT.tempTrace("---武器类型为空");
            return false;
         }
         if(this.getMaxColor() == "")
         {
            INIT.tempTrace("---武器成色为空");
            return false;
         }
         var proArr0:Array = ArmsPro.filterArr;
         for each(pro0 in proArr0)
         {
            d0 = this.getProDef(pro0);
            rangeB0 = ArmsPro.filterRangeArr.indexOf(pro0) >= 0;
            if(rangeB0)
            {
               now0 = s0.getProValue(pro0) as Number;
               if(isNaN(now0))
               {
                  INIT.showError("数据" + pro0 + " NaN");
                  return false;
               }
               min0 = getProMin(pro0) - 0.0005;
               max0 = getProMax(pro0) + 0.0005;
               if(now0 < min0 || now0 > max0)
               {
                  INIT.tempTrace("---" + pro0 + " now:" + now0 + "  min:" + min0 + "  max:" + max0);
                  return false;
               }
            }
            else
            {
               arr0 = getProValueArr(pro0);
               now2 = s0.getProValue(pro0);
               if(now2 is String)
               {
                  if(arr0.indexOf(now2) == -1)
                  {
                     INIT.tempTrace("---" + pro0 + ":" + now2 + "不在数组中 " + arr0);
                     return false;
                  }
               }
               else if(now2 is Array)
               {
                  nowArr0 = now2 as Array;
                  mustB0 = this.getProArrayMustB(d0);
                  haveNum0 = 0;
                  for each(must0 in arr0)
                  {
                     if(mustB0)
                     {
                        if(ArrayMethod.indexOfStringInArr(nowArr0,must0) == false)
                        {
                           INIT.tempTrace("---" + pro0 + ":" + now2 + "没有技能 " + must0);
                           return false;
                        }
                     }
                     else if(ArrayMethod.indexOfStringInArr(nowArr0,must0) == true)
                     {
                        haveNum0++;
                     }
                  }
                  if(mustB0 == false)
                  {
                     if(haveNum0 <= 0)
                     {
                        INIT.tempTrace("---" + pro0 + ":" + now2 + "没有技能 " + arr0);
                        return false;
                     }
                  }
               }
            }
         }
         return true;
      }
      
      private function getArmsDefArr() : Array
      {
         var arr0:Array = null;
         var type0:String = this.getType();
         if(type0 != "")
         {
            return Gaming.defineGroup.bullet.rangeRanTypeObj[type0];
         }
         return null;
      }
      
      public function getTextLast() : String
      {
         var s0:String = "注意，弹容、换弹速度、射程等为未加成的基础属性，与武器面板属性有所出入，要查看具体武器的基础属性，请前往其零件装配界面。";
         return ComMethod.color("\n\n" + s0,"#FF6600",12);
      }
      
      override public function getDefineProMin(pro0:String) : Number
      {
         var ad0:ArmsRangeDefine = null;
         var range0:Array = null;
         var min2:Number = NaN;
         var min0:Number = NO_NUM;
         var arr0:Array = this.getArmsDefArr();
         if(Boolean(arr0))
         {
            for each(ad0 in arr0)
            {
               range0 = ad0.getRangeArr(pro0);
               min2 = Number(range0[0]);
               if(min0 == NO_NUM || min2 < min0)
               {
                  min0 = min2;
               }
            }
         }
         return min0;
      }
      
      override public function getDefineProMax(pro0:String) : Number
      {
         var ad0:ArmsRangeDefine = null;
         var range0:Array = null;
         var max2:Number = NaN;
         var max0:Number = NO_NUM;
         var arr0:Array = this.getArmsDefArr();
         if(Boolean(arr0))
         {
            for each(ad0 in arr0)
            {
               range0 = ad0.getRangeArr(pro0);
               max2 = Number(range0[1]);
               if(max0 == NO_NUM || max2 > max0)
               {
                  max0 = max2;
               }
            }
         }
         return max0;
      }
      
      public function getProDefArr() : Array
      {
         var proArr0:Array = ArmsPro.filterArr;
         return Gaming.defineGroup.editPro.getArrByNameArrGather(proArr0,EditProGather.arms);
      }
      
      public function getProDef(pro0:String) : EditProDefine
      {
         return Gaming.defineGroup.editPro.getNormalDefineByGather(EditProGather.arms,pro0) as EditProDefine;
      }
      
      public function getProChooseMax(pro0:String) : int
      {
         if(pro0 == ArmsPro.armsType)
         {
            return 1;
         }
         if(pro0 == ArmsPro.godSkillArr)
         {
            return ArmsColor.getRanGodSkillMax(this.getMaxColor());
         }
         return NO_NUM;
      }
      
      public function getProChooseMin(pro0:String) : int
      {
         if(pro0 == ArmsPro.armsType)
         {
            return 1;
         }
         if(pro0 == ArmsPro.color)
         {
            return 1;
         }
         return 0;
      }
      
      public function getProChooseArr(d0:EditProDefine) : Array
      {
         var pro0:String = d0.name;
         if(pro0 == ArmsPro.armsType)
         {
            return ArmsType.filterArr;
         }
         if(pro0 == ArmsPro.color)
         {
            return ArmsColor.filterArr;
         }
         if(pro0 == ArmsPro.godSkillArr)
         {
            return Gaming.defineGroup.skill.godArmsSkillNameArr;
         }
         return null;
      }
      
      public function getProType(d0:EditProDefine) : String
      {
         if(d0.type == ProType.STRING)
         {
            return ProType.ARRAY;
         }
         if(ArmsPro.filterRangeArr.indexOf(d0.name) >= 0)
         {
            return ProType.MIN_MAX;
         }
         return d0.type;
      }
      
      public function getProArrayMustB(d0:EditProDefine) : Boolean
      {
         if(d0.name == ArmsPro.godSkillArr)
         {
            return true;
         }
         return false;
      }
      
      public function linkClick(str0:String) : Boolean
      {
         var valueArr0:Array = null;
         var chooseB0:Boolean = false;
         var chooseMin0:int = 0;
         var chooseMax0:int = 0;
         var typeChangeB0:Boolean = false;
         var fleshB0:Boolean = false;
         var strArr0:Array = str0.split(":");
         var pro0:String = strArr0[0];
         var value0:String = strArr0[1];
         var d0:EditProDefine = this.getProDef(pro0);
         var type0:String = this.getProType(d0);
         if(type0 == ProType.ARRAY)
         {
            valueArr0 = getProValueArrAdd(d0.name);
            chooseB0 = valueArr0.indexOf(value0) >= 0;
            if(chooseB0)
            {
               chooseMin0 = this.getProChooseMin(pro0);
               if(valueArr0.length > chooseMin0)
               {
                  ArrayMethod.remove(valueArr0,value0);
                  fleshB0 = true;
               }
            }
            else
            {
               chooseMax0 = this.getProChooseMax(pro0);
               if(chooseMax0 > 0 && valueArr0.length >= chooseMax0)
               {
                  valueArr0.pop();
                  fleshB0 = true;
               }
               if(chooseMax0 == NO_NUM || valueArr0.length < chooseMax0)
               {
                  ArrayMethod.addNoRepeatInArr(valueArr0,value0);
                  fleshB0 = true;
               }
            }
         }
         if(fleshB0)
         {
            typeChangeB0 = d0.name == ArmsPro.armsType;
            this.afterClickDeal(typeChangeB0);
         }
         return fleshB0;
      }
      
      private function afterClickDeal(typeChangeB0:Boolean) : void
      {
         var valueArr0:Array = null;
         if(typeChangeB0)
         {
            o = ObjectMethod.removeArr(o,ArmsPro.filterRangeArr);
         }
         var skillMax0:int = this.getProChooseMax(ArmsPro.godSkillArr);
         if(skillMax0 >= 0)
         {
            valueArr0 = getProValueArrAdd(ArmsPro.godSkillArr);
            if(valueArr0.length > skillMax0)
            {
               valueArr0.length = skillMax0;
            }
         }
      }
   }
}

