package dataAll._app.food
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.cf.NumberEncodeObj;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.ClassProperty;
   
   public class FoodSave
   {
      
      public static var pro_arr:Array = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var eatName:String = "";
      
      public var eatNameArr:Array = [];
      
      public var rawObj:NumberEncodeObj = new NumberEncodeObj();
      
      public var bookObj:NumberEncodeObj = new NumberEncodeObj();
      
      public function FoodSave()
      {
         super();
      }
      
      public function get dropNum() : Number
      {
         return this.CF.getAttribute("dropNum");
      }
      
      public function set dropNum(v0:Number) : void
      {
         this.CF.setAttribute("dropNum",v0);
      }
      
      public function get dropAll() : Number
      {
         return this.CF.getAttribute("dropAll");
      }
      
      public function set dropAll(v0:Number) : void
      {
         this.CF.setAttribute("dropAll",v0);
      }
      
      public function get eatNum() : Number
      {
         return this.CF.getAttribute("eatNum");
      }
      
      public function set eatNum(v0:Number) : void
      {
         this.CF.setAttribute("eatNum",v0);
      }
      
      public function get eatAll() : Number
      {
         return this.CF.getAttribute("eatAll");
      }
      
      public function set eatAll(v0:Number) : void
      {
         this.CF.setAttribute("eatAll",v0);
      }
      
      public function get eatTime() : Number
      {
         return this.CF.getAttribute("eatTime");
      }
      
      public function set eatTime(v0:Number) : void
      {
         this.CF.setAttribute("eatTime",v0);
      }
      
      public function get profi() : Number
      {
         return this.CF.getAttribute("profi");
      }
      
      public function set profi(v0:Number) : void
      {
         this.CF.setAttribute("profi",v0);
      }
      
      public function get profiAll() : Number
      {
         return this.CF.getAttribute("profiAll");
      }
      
      public function set profiAll(v0:Number) : void
      {
         this.CF.setAttribute("profiAll",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function newDayCtrl(timeDa0:StringDate) : void
      {
         this.eatNum = 0;
         this.profi = 0;
         this.dropNum = 0;
         this.eatNameArr.length = 0;
         this.eatName = "";
         this.eatTime = 0;
      }
   }
}

