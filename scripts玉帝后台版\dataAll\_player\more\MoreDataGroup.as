package dataAll._player.more
{
   import dataAll._app.love.LoveData;
   import dataAll._player.PlayerData;
   import dataAll._player.PlayerSave;
   import dataAll._player.more.creator.MoreAddData;
   import dataAll._player.more.save.MoreSave;
   import dataAll._player.more.save.MoreSaveGroup;
   import dataAll._player.role.RoleName;
   import dataAll._player.time.TimeData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.body.define.HeroDefine;
   import dataAll.equip.EquipData;
   import dataAll.equip.EquipDataGroup;
   import dataAll.items.IO_ItemsData;
   import dataAll.items.ItemsDataGroup;
   import dataAll.items.save.ItemsSave;
   import dataAll.items.save.ItemsSaveGroup;
   import gameAll.hero.HeroBody;
   import gameAll.level.data.LevelData;
   
   public class MoreDataGroup extends ItemsDataGroup
   {
      
      public var saveGroup:MoreSaveGroup;
      
      public var heroData:MoreData = null;
      
      public function MoreDataGroup()
      {
         super();
         dataType = ItemsDataGroup.TYPE_MORE;
      }
      
      override public function inData_bySaveGroup(sg0:ItemsSaveGroup) : void
      {
         var n:* = undefined;
         var s0:MoreSave = null;
         var da0:MoreData = null;
         clearData();
         this.saveGroup = sg0 as MoreSaveGroup;
         for(n in sg0.arr)
         {
            s0 = sg0.arr[n];
            da0 = new MoreData();
            da0.inData_bySave(s0,normalPlayerData);
            da0.setPlaceType(placeType);
            dataArr[n] = da0;
            _siteDataArr = null;
         }
      }
      
      public function newMainHeroData(pd0:PlayerData, ps0:PlayerSave) : MoreData
      {
         var d0:HeroDefine = null;
         var hero_da0:MoreData = new MoreData();
         hero_da0.setPlayerData(pd0);
         var hero_s0:MoreSave = new MoreSave();
         d0 = Gaming.defineGroup.body.getHeroDefine(ps0.main.role);
         hero_s0.inDataByHeroDefine(d0);
         hero_s0.SAVE = ps0;
         hero_da0.save = hero_s0;
         hero_da0.def = d0;
         hero_da0.DATA = pd0;
         pd0.heroData = hero_da0;
         this.heroData = hero_da0;
         return hero_da0;
      }
      
      public function addByAddData(addDa0:MoreAddData) : MoreData
      {
         var d0:HeroDefine = addDa0.bodyDef as HeroDefine;
         var s0:MoreSave = new MoreSave();
         s0.inDataByHeroDefine(d0);
         var da0:MoreData = new MoreData();
         da0.inData_bySave(s0 as MoreSave,normalPlayerData);
         da0.DATA.initSaveByMoreAddData(addDa0);
         da0.affterAddFirstArms();
         this.addData(da0,true);
         return da0;
      }
      
      public function addByUnitName(name0:String) : MoreData
      {
         var d0:HeroDefine = Gaming.defineGroup.body.getHeroDefine(name0);
         var s0:MoreSave = new MoreSave();
         s0.inDataByHeroDefine(d0);
         return this.addSave(s0) as MoreData;
      }
      
      override public function addSave(s0:ItemsSave, fleshSaveGroupB0:Boolean = true) : IO_ItemsData
      {
         var da0:MoreData = new MoreData();
         da0.inData_bySave(s0 as MoreSave,normalPlayerData);
         da0.DATA.initSave(da0,playerData.level);
         da0.affterAddFirstArms();
         this.addData(da0,fleshSaveGroupB0);
         return da0;
      }
      
      override public function addData(da0:IO_ItemsData, fleshSaveGroupB0:Boolean = true) : void
      {
         super.addData(da0,fleshSaveGroupB0);
      }
      
      override public function swap_addData(da1:IO_ItemsData) : void
      {
         var da0:MoreData = null;
         super.swap_addData(da1);
         if(placeType == PLACE_BAG)
         {
            da0 = da1 as MoreData;
            da0.DATA.setPlayerCtrlB(false);
         }
      }
      
      override public function newDayCtrl(timeStr0:String) : void
      {
         var da0:MoreData = null;
         for each(da0 in dataArr)
         {
            da0.newDayCtrl(timeStr0);
         }
      }
      
      public function startLevel() : void
      {
         var da0:MoreData = null;
         var pd0:MorePlayerData = null;
         for each(da0 in dataArr)
         {
            pd0 = da0.DATA as MorePlayerData;
            if(Boolean(pd0))
            {
               pd0.partner.startLevel();
            }
         }
      }
      
      public function getMainSkillArr() : Array
      {
         var da0:MoreData = null;
         var pd0:MorePlayerData = null;
         var arr0:Array = [];
         for each(da0 in dataArr)
         {
            pd0 = da0.DATA as MorePlayerData;
            if(Boolean(pd0))
            {
               arr0 = arr0.concat(pd0.love.getSkillLabelArr(RoleName.Striker));
            }
         }
         return arr0;
      }
      
      public function getDataByHeroName(name0:String) : MoreData
      {
         var da0:MoreData = null;
         for each(da0 in dataArr)
         {
            if(da0.def.name == name0)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getNameArr(limitArr0:Array) : Array
      {
         var n:* = undefined;
         var da0:MoreData = null;
         var arr0:Array = [];
         for(n in dataArr)
         {
            da0 = dataArr[n];
            if(arr0.indexOf(da0.def.name) == -1)
            {
               if(!(Boolean(limitArr0) && limitArr0.indexOf(da0.def.name) == -1))
               {
                  arr0.push(da0.def.name);
               }
            }
         }
         return arr0;
      }
      
      public function getAllPlayerGunImageNameArr() : Array
      {
         var n:* = undefined;
         var da0:MoreData = null;
         var arr0:Array = [];
         for(n in dataArr)
         {
            da0 = dataArr[n];
            arr0 = arr0.concat(da0.DATA.getGunImageNameArr());
         }
         return arr0;
      }
      
      public function getAllPlayerArmsDataArr() : Array
      {
         var n:* = undefined;
         var da0:MoreData = null;
         var arr0:Array = [];
         for(n in dataArr)
         {
            da0 = dataArr[n];
            arr0 = arr0.concat(da0.DATA.getArmsDataArr());
         }
         return arr0;
      }
      
      public function getAllPlayerEquipDataArr() : Array
      {
         var n:* = undefined;
         var da0:MoreData = null;
         var arr0:Array = [];
         for(n in dataArr)
         {
            da0 = dataArr[n];
            arr0 = arr0.concat(da0.DATA.getEquipDataArr());
         }
         return arr0;
      }
      
      public function getPlayerDataArr() : Array
      {
         var n:* = undefined;
         var da0:MoreData = null;
         var arr0:Array = [];
         for(n in dataArr)
         {
            da0 = dataArr[n];
            arr0.push(da0.DATA);
         }
         return arr0;
      }
      
      public function getHeroBodyArr() : Array
      {
         var n:* = undefined;
         var da0:MoreData = null;
         var arr0:Array = [];
         for(n in dataArr)
         {
            da0 = dataArr[n];
            if(Boolean(da0.DATA.hero))
            {
               arr0.push(da0.DATA.hero);
            }
         }
         return arr0;
      }
      
      public function getDieNum() : Number
      {
         var n:* = undefined;
         var da0:MoreData = null;
         var hero0:HeroBody = null;
         var num0:int = 0;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            hero0 = da0.DATA.hero;
            if(hero0 is HeroBody)
            {
               if(hero0.dieCtrl.dieState == "stru")
               {
                  num0++;
               }
            }
         }
         return num0;
      }
      
      public function haveMustAddLifeB() : Boolean
      {
         var n:* = undefined;
         var da0:MoreData = null;
         var hero0:HeroBody = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            hero0 = da0.DATA.hero;
            if(hero0 is HeroBody)
            {
               if(hero0.dat.getLifePer() < 1)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function getAllDps() : Number
      {
         var n:* = undefined;
         var da0:MoreData = null;
         var v0:Number = 0;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            v0 += da0.DATA.getDps();
         }
         return v0;
      }
      
      public function getAllLife() : Number
      {
         var n:* = undefined;
         var da0:MoreData = null;
         var v0:Number = 0;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            v0 += da0.DATA.base.getMaxLife();
         }
         return v0;
      }
      
      public function getLiveOne() : HeroBody
      {
         var n:* = undefined;
         var da0:MoreData = null;
         var hero0:HeroBody = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            hero0 = da0.DATA.hero;
            if(hero0 is HeroBody)
            {
               if(hero0.getDie() == 0)
               {
                  return hero0;
               }
            }
         }
         return null;
      }
      
      public function event_AffterLoadSave() : void
      {
         var n:* = undefined;
         var da0:MoreData = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            da0.DATA.fleshAllByEquip();
            da0.DATA.fillAllData();
         }
      }
      
      public function findArmsData(da0:ArmsData, houseB0:Boolean = false) : ArmsDataGroup
      {
         var n:* = undefined;
         var m_da0:MoreData = null;
         var dg0:ArmsDataGroup = null;
         for(n in dataArr)
         {
            m_da0 = dataArr[n];
            dg0 = m_da0.DATA.findArmsData(da0);
            if(dg0 is ArmsDataGroup)
            {
               return dg0;
            }
         }
         return null;
      }
      
      public function findEquipData(da0:EquipData, houseB0:Boolean = false) : EquipDataGroup
      {
         var n:* = undefined;
         var m_da0:MoreData = null;
         var dg0:EquipDataGroup = null;
         for(n in dataArr)
         {
            m_da0 = dataArr[n];
            dg0 = m_da0.DATA.findEquipData(da0);
            if(dg0 is EquipDataGroup)
            {
               return dg0;
            }
         }
         return null;
      }
      
      public function findEquipDataByName(name0:String) : EquipData
      {
         var n:* = undefined;
         var m_da0:MoreData = null;
         var da0:EquipData = null;
         for(n in dataArr)
         {
            m_da0 = dataArr[n];
            for each(da0 in m_da0.DATA.equip.dataArr)
            {
               if(da0.save.name == name0)
               {
                  return da0;
               }
            }
         }
         return null;
      }
      
      override public function getAllMustArenaStampNum() : Number
      {
         var da0:MoreData = null;
         var pd0:NormalPlayerData = null;
         var num0:Number = 0;
         for each(da0 in dataArr)
         {
            pd0 = da0.DATA;
            num0 += pd0.getAllMustArenaStampNum();
         }
         return num0;
      }
      
      public function getAllItemsDataGroupArr() : Array
      {
         var da0:MoreData = null;
         var pd0:NormalPlayerData = null;
         var arr0:Array = [];
         for each(da0 in dataArr)
         {
            pd0 = da0.DATA;
            arr0 = arr0.concat(pd0.getAllItemsDataGroupArr());
         }
         return arr0;
      }
      
      override public function findArenaGiftItemsData(str0:String) : IO_ItemsData
      {
         var md0:MoreData = null;
         var pd0:NormalPlayerData = null;
         var da0:IO_ItemsData = null;
         for each(md0 in dataArr)
         {
            pd0 = md0.DATA;
            da0 = pd0.findArenaGiftItemsData(str0);
            if(da0 is IO_ItemsData)
            {
               return da0;
            }
         }
         return null;
      }
      
      public function getLoveData(roleName0:String) : LoveData
      {
         var pd0:MorePlayerData = null;
         var md0:MoreData = this.getDataByHeroName(roleName0);
         if(Boolean(md0))
         {
            pd0 = md0.DATA as MorePlayerData;
            if(Boolean(pd0))
            {
               return pd0.love;
            }
         }
         return null;
      }
      
      public function overLevel(levelDat0:LevelData) : void
      {
         var da0:MoreData = null;
         var pd0:MorePlayerData = null;
         for each(da0 in dataArr)
         {
            pd0 = da0.DATA as MorePlayerData;
            if(Boolean(pd0))
            {
               pd0.love.overLevel(levelDat0);
            }
         }
      }
      
      public function setReadSaveTime(timeData0:TimeData) : void
      {
         var da0:MoreData = null;
         var pd0:MorePlayerData = null;
         for each(da0 in dataArr)
         {
            pd0 = da0.DATA as MorePlayerData;
            if(Boolean(pd0))
            {
               pd0.love.setReadSaveTime(timeData0);
            }
         }
      }
      
      public function setMainPlayerType(type0:String) : void
      {
         var n:* = undefined;
         var da0:MoreData = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            da0.DATA.setMainPlayerType(type0);
         }
      }
      
      public function clearAllPlayerCtrl() : void
      {
         var da0:MoreData = null;
         for each(da0 in dataArr)
         {
            da0.DATA.setPlayerCtrlB(false);
         }
      }
      
      public function addExp(v0:Number) : void
      {
         var n:* = undefined;
         var da0:MoreData = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            da0.DATA.addExp(v0);
         }
      }
      
      public function addLifeMul(v0:Number) : void
      {
         var n:* = undefined;
         var da0:MoreData = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            if(Boolean(da0.DATA.hero))
            {
               da0.DATA.hero.dat.addLife(v0,true);
            }
         }
      }
      
      override public function getSaveGroup() : ItemsSaveGroup
      {
         return this.saveGroup;
      }
      
      override public function getGripType() : String
      {
         return "equipGrip";
      }
      
      public function zuobiPan() : String
      {
         var n:* = undefined;
         var da0:MoreData = null;
         var str0:String = null;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            str0 = da0.DATA.zuobiPan();
            if(str0 != "")
            {
               return str0;
            }
         }
         return "";
      }
      
      override public function checkNormalPlayerData(pd0:NormalPlayerData) : void
      {
         var n:* = undefined;
         var da0:MoreData = null;
         super.checkNormalPlayerData(pd0);
         for(n in dataArr)
         {
            da0 = dataArr[n];
            da0.DATA.checkNormalPlayerData();
         }
      }
      
      public function getPlayerCtrlNum() : int
      {
         var n:* = undefined;
         var da0:MoreData = null;
         var num0:int = 0;
         for(n in dataArr)
         {
            da0 = dataArr[n];
            if(da0.DATA.isPlayerCtrlB())
            {
               num0++;
            }
         }
         return num0;
      }
   }
}

