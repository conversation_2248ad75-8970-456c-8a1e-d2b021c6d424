package dataAll.arms
{
   import dataAll.arms.define.ArmsChargerDefine;
   import dataAll.arms.define.ArmsChargerDefineGroup;
   import dataAll.equip.EquipPropertyData;
   
   public class ArmsChargerDataGroup
   {
      
      public var nowData:ArmsChargerData = null;
      
      public var obj:Object = {};
      
      public function ArmsChargerDataGroup()
      {
         super();
      }
      
      public function initData() : void
      {
         var n:* = undefined;
         var type0:String = null;
         var d0:ArmsChargerDefine = null;
         var da0:ArmsChargerData = null;
         var armsChargerGroup:ArmsChargerDefineGroup = Gaming.defineGroup.armsCharger;
         var typeArr0:Array = armsChargerGroup.typeArr;
         for(n in typeArr0)
         {
            type0 = typeArr0[n];
            d0 = armsChargerGroup.getDefine(type0);
            da0 = new ArmsChargerData();
            da0.init(d0);
            this.obj[d0.name] = da0;
         }
      }
      
      public function flesh_byEquip(mergeData0:EquipPropertyData) : void
      {
         var n:* = undefined;
         var da0:ArmsChargerData = null;
         for(n in this.obj)
         {
            da0 = this.obj[n];
            da0.flesh_byEquip(mergeData0);
         }
      }
      
      public function fillAllData() : void
      {
         var n:* = undefined;
         var da0:ArmsChargerData = null;
         for(n in this.obj)
         {
            da0 = this.obj[n];
            da0.fillAllData();
         }
      }
      
      public function getData(name0:String) : ArmsChargerData
      {
         return this.obj[name0];
      }
      
      public function getCharger(name0:String) : int
      {
         var da0:ArmsChargerData = this.getData(name0);
         if(Boolean(da0))
         {
            return da0.now;
         }
         return 0;
      }
      
      public function addChargerNum(value0:Number, mulB0:Boolean = false, target0:String = "now", addType0:String = "add") : *
      {
         var n:* = undefined;
         var da0:ArmsChargerData = null;
         if(target0 == "now")
         {
            if(Boolean(this.nowData))
            {
               this.nowData.addChargerNum(value0,mulB0,addType0);
            }
         }
         else if(target0 == "all")
         {
            for(n in this.obj)
            {
               da0 = this.obj[n];
               da0.addChargerNum(value0,mulB0,addType0);
            }
         }
      }
      
      public function setAllMaxMul(mul0:Number, fillB0:Boolean) : void
      {
         var da0:ArmsChargerData = null;
         for each(da0 in this.obj)
         {
            da0.setMaxMul(mul0);
         }
         if(fillB0)
         {
            this.fillAllData();
         }
      }
      
      public function clear(mul0:Number) : void
      {
         var da0:ArmsChargerData = null;
         for each(da0 in this.obj)
         {
            da0.now -= da0.getMax() * mul0;
            if(da0.now < 0)
            {
               da0.now = 0;
            }
         }
      }
      
      public function isAllFillB() : Boolean
      {
         var n:* = undefined;
         var da0:ArmsChargerData = null;
         for(n in this.obj)
         {
            da0 = this.obj[n];
            if(da0.now < da0.getMax())
            {
               return false;
            }
         }
         return true;
      }
      
      public function getText() : String
      {
         var n:* = undefined;
         var da0:ArmsChargerData = null;
         var str1:String = null;
         var str0:String = "";
         for(n in this.obj)
         {
            da0 = this.obj[n];
            str1 = da0.def.cnName + "  " + da0.getMax();
            str0 += "\n" + str1;
         }
         return str0.substr(1);
      }
   }
}

