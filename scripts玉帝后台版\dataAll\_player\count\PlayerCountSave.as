package dataAll._player.count
{
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.task.TaskData;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.gift.define.GiftBase;
   import dataAll.items.IO_ItemsData;
   
   public class PlayerCountSave extends NormalPlayerCountSave
   {
      
      public static var pro_arr:Array = [];
      
      private var _maxFallValue:Number = 0;
      
      public var task5NameArr:Array = [];
      
      public var killKingNameArr:Array = [];
      
      public var onlineTime:Number = 0;
      
      public var onlineTime20:Number = 0;
      
      public var levelOnlineTime:Number = 0;
      
      public var doubleOnlineTime:Number = 0;
      
      public var todayKillEnemyNum:int = 0;
      
      public var todaySkillStoneNum:int = 0;
      
      public var hurtB:Boolean = false;
      
      public var oneHB:Boolean = false;
      
      public function PlayerCountSave()
      {
         super();
         this.dropArmsNum = 0;
         this.dropEquipNum = 0;
         this.bossNoOrangeNum = 0;
         this.maxFallValue = 0;
         this.maxKingLevel = 0;
         this.charmMaxNum = 0;
         this.rollNum = 0;
         this.myopiaMaxMul = 0;
         this.hyperopiaMaxMul = 0;
         this.moreMissileMaxNum = 0;
         this.skillGiftNum = 0;
         this.maxFallHigh = 0;
         this.maxFlyHigh = 0;
         this.glidingTime = 0;
         this.blackMarketMaxNum = 0;
         this.lotteryCoin = 0;
         this.lotteryAllOrangeNum = 0;
         this.normalLevelNum = 0;
         this.todayUseMoney = 0;
         this.todayExploitCards = 0;
         this.todayArenaStamp = 0;
         this.vehicleKillNum = 0;
         this.weaponKillNum = 0;
         this.todayStrengthNum = 0;
      }
      
      public function set vehicleKillNum(v0:Number) : void
      {
         iCF.setAttribute("vehicleKillNum",v0);
      }
      
      public function get vehicleKillNum() : Number
      {
         return Number(iCF.getAttribute("vehicleKillNum"));
      }
      
      public function set weaponKillNum(v0:Number) : void
      {
         iCF.setAttribute("weaponKillNum",v0);
      }
      
      public function get weaponKillNum() : Number
      {
         return Number(iCF.getAttribute("weaponKillNum"));
      }
      
      public function set dropArmsNum(v0:Number) : void
      {
         iCF.setAttribute("dropArmsNum",v0);
      }
      
      public function get dropArmsNum() : Number
      {
         return Number(iCF.getAttribute("dropArmsNum"));
      }
      
      public function set dropEquipNum(v0:Number) : void
      {
         iCF.setAttribute("dropEquipNum",v0);
      }
      
      public function get dropEquipNum() : Number
      {
         return Number(iCF.getAttribute("dropEquipNum"));
      }
      
      public function set bossNoOrangeNum(v0:Number) : void
      {
         iCF.setAttribute("bossNoOrangeNum",v0);
      }
      
      public function get bossNoOrangeNum() : Number
      {
         return Number(iCF.getAttribute("bossNoOrangeNum"));
      }
      
      public function set maxFallValue(v0:Number) : void
      {
         this._maxFallValue = v0 / V;
      }
      
      public function get maxFallValue() : Number
      {
         return Math.round(this._maxFallValue * V);
      }
      
      public function set charmMaxNum(v0:Number) : void
      {
         iCF.setAttribute("charmMaxNum",v0);
      }
      
      public function get charmMaxNum() : Number
      {
         return Number(iCF.getAttribute("charmMaxNum"));
      }
      
      public function set myopiaMaxMul(v0:Number) : void
      {
         nCF.setAttribute("myopiaMaxMul",v0);
      }
      
      public function get myopiaMaxMul() : Number
      {
         return Number(nCF.getAttribute("myopiaMaxMul"));
      }
      
      public function set hyperopiaMaxMul(v0:Number) : void
      {
         nCF.setAttribute("hyperopiaMaxMul",v0);
      }
      
      public function get hyperopiaMaxMul() : Number
      {
         return Number(nCF.getAttribute("hyperopiaMaxMul"));
      }
      
      public function set moreMissileMaxNum(v0:Number) : void
      {
         iCF.setAttribute("moreMissileMaxNum",v0);
      }
      
      public function get moreMissileMaxNum() : Number
      {
         return Number(iCF.getAttribute("moreMissileMaxNum"));
      }
      
      public function set skillGiftNum(v0:Number) : void
      {
         iCF.setAttribute("skillGiftNum",v0);
      }
      
      public function get skillGiftNum() : Number
      {
         return Number(iCF.getAttribute("skillGiftNum"));
      }
      
      public function set maxFlyHigh(v0:Number) : void
      {
         iCF.setAttribute("maxFlyHigh",v0);
      }
      
      public function get maxFlyHigh() : Number
      {
         return Number(iCF.getAttribute("maxFlyHigh"));
      }
      
      public function set maxFallHigh(v0:Number) : void
      {
         iCF.setAttribute("maxFallHigh",v0);
      }
      
      public function get maxFallHigh() : Number
      {
         return Number(iCF.getAttribute("maxFallHigh"));
      }
      
      public function set glidingTime(v0:Number) : void
      {
         nCF.setAttribute("glidingTime",v0);
      }
      
      public function get glidingTime() : Number
      {
         return Number(nCF.getAttribute("glidingTime"));
      }
      
      public function set rollNum(v0:Number) : void
      {
         iCF.setAttribute("rollNum",v0);
      }
      
      public function get rollNum() : Number
      {
         return Number(iCF.getAttribute("rollNum"));
      }
      
      public function set maxKingLevel(v0:Number) : void
      {
         CF.setAttribute("maxKingLevel",v0);
      }
      
      public function get maxKingLevel() : Number
      {
         return Number(CF.getAttribute("maxKingLevel"));
      }
      
      public function set blackMarketMaxNum(v0:Number) : void
      {
         CF.setAttribute("blackMarketMaxNum",v0);
      }
      
      public function get blackMarketMaxNum() : Number
      {
         return Number(CF.getAttribute("blackMarketMaxNum"));
      }
      
      public function set lotteryCoin(v0:Number) : void
      {
         CF.setAttribute("lotteryCoin",v0);
      }
      
      public function get lotteryCoin() : Number
      {
         return Number(CF.getAttribute("lotteryCoin"));
      }
      
      public function set lotteryAllOrangeNum(v0:Number) : void
      {
         CF.setAttribute("lotteryAllOrangeNum",v0);
      }
      
      public function get lotteryAllOrangeNum() : Number
      {
         return Number(CF.getAttribute("lotteryAllOrangeNum"));
      }
      
      public function get todayArenaNum() : Number
      {
         return CF.getAttribute("todayArenaNum");
      }
      
      public function set todayArenaNum(v0:Number) : void
      {
         CF.setAttribute("todayArenaNum",v0);
      }
      
      public function get normalLevelNum() : Number
      {
         return CF.getAttribute("normalLevelNum");
      }
      
      public function set normalLevelNum(v0:Number) : void
      {
         CF.setAttribute("normalLevelNum",v0);
      }
      
      public function get todayUseMoney() : Number
      {
         return CF.getAttribute("todayUseMoney");
      }
      
      public function set todayUseMoney(v0:Number) : void
      {
         CF.setAttribute("todayUseMoney",v0);
      }
      
      public function get todayExploitCards() : Number
      {
         return CF.getAttribute("todayExploitCards");
      }
      
      public function set todayExploitCards(v0:Number) : void
      {
         CF.setAttribute("todayExploitCards",v0);
      }
      
      public function get todayArenaStamp() : Number
      {
         return CF.getAttribute("todayArenaStamp");
      }
      
      public function set todayArenaStamp(v0:Number) : void
      {
         CF.setAttribute("todayArenaStamp",v0);
      }
      
      public function get todayStrengthNum() : Number
      {
         return CF.getAttribute("todayStrengthNum");
      }
      
      public function set todayStrengthNum(v0:Number) : void
      {
         CF.setAttribute("todayStrengthNum",v0);
      }
      
      public function get todayUnionContribution() : Number
      {
         return CF.getAttribute("todayUnionContribution");
      }
      
      public function set todayUnionContribution(v0:Number) : void
      {
         CF.setAttribute("todayUnionContribution",v0);
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         super.inData_byObj(obj0);
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
         if(this.maxKingLevel >= 999999)
         {
            this.maxKingLevel = 999999;
         }
      }
      
      public function newDayCtrl() : void
      {
         this.todayKillEnemyNum = 0;
         this.todaySkillStoneNum = 0;
         this.onlineTime = 0;
         this.levelOnlineTime = 0;
         this.blackMarketMaxNum = 0;
         this.doubleOnlineTime = 0;
         this.normalLevelNum = 0;
         this.todayUseMoney = 0;
         this.todayArenaNum = 0;
         this.todayUnionContribution = 0;
         this.todayStrengthNum = 0;
         this.todayArenaStamp = 0;
         this.todayExploitCards = 0;
      }
      
      public function addDropArmsNum(num0:int) : void
      {
         this.dropArmsNum += num0;
      }
      
      public function addDropEquipNum(num0:int) : void
      {
         this.dropEquipNum += num0;
      }
      
      public function inputHyperopiaMaxMul(v0:Number) : void
      {
         var now0:Number = this.hyperopiaMaxMul;
         if(now0 < v0)
         {
            this.hyperopiaMaxMul = v0;
         }
      }
      
      public function inputMyopiaMaxMul(v0:Number) : void
      {
         var now0:Number = this.myopiaMaxMul;
         if(now0 < v0)
         {
            this.myopiaMaxMul = v0;
         }
      }
      
      public function inputCharmMaxNum(v0:Number) : void
      {
         var now0:Number = this.charmMaxNum;
         if(now0 < v0)
         {
            this.charmMaxNum = v0;
         }
      }
      
      public function inputMoreMissileMaxNum(v0:Number) : void
      {
         var now0:Number = this.moreMissileMaxNum;
         if(now0 < v0)
         {
            this.moreMissileMaxNum = v0;
         }
      }
      
      public function inputSkillGiftNum(v0:Number) : void
      {
         var now0:Number = this.skillGiftNum;
         if(now0 < v0)
         {
            this.skillGiftNum = v0;
         }
      }
      
      public function addKillKing(name0:String, lv0:int) : void
      {
         if(this.killKingNameArr.indexOf(name0) == -1)
         {
            this.killKingNameArr.push(name0);
         }
         var now0:Number = this.maxKingLevel;
         if(now0 < lv0)
         {
            this.maxKingLevel = lv0;
         }
      }
      
      public function addBossNoOrangeNum(num0:Number) : void
      {
         var num2:Number = this.bossNoOrangeNum;
         num2 += num0;
         if(num2 < 0)
         {
            num2 = 0;
         }
         this.bossNoOrangeNum = num2;
      }
      
      public function lotteryOne(g0:GiftAddDefineGroup) : void
      {
         var d0:GiftAddDefine = null;
         for each(d0 in g0.arr)
         {
            if(d0.name == GiftBase.coin)
            {
               ++this.lotteryCoin;
            }
            if(d0.name == "allBlackEquipCash")
            {
               ++this.lotteryAllOrangeNum;
            }
         }
      }
      
      public function strengthEvent(da0:IO_ItemsData) : void
      {
         ++this.todayStrengthNum;
      }
      
      public function taskComplete(da0:TaskData) : void
      {
         var name0:String = null;
         if(da0.getDiff() >= 4)
         {
            name0 = da0.def.name;
            if(this.task5NameArr.length < 7)
            {
               ArrayMethod.addNoRepeatInArr(this.task5NameArr,name0);
            }
         }
      }
      
      public function getHurtTestB() : Boolean
      {
         return this.oneHB;
      }
      
      public function setHurtTestB(bb0:Boolean) : void
      {
         this.oneHB = bb0;
      }
      
      public function getHurtCumB() : Boolean
      {
         return this.hurtB;
      }
      
      public function setHurtCumB(bb0:Boolean) : void
      {
         this.hurtB = bb0;
      }
   }
}

