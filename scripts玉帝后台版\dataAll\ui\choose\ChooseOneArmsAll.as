package dataAll.ui.choose
{
   import dataAll.arms.creator.ArmsSpecial;
   import dataAll.arms.creator.ArmsSpecialAndSkill;
   import dataAll.arms.define.ArmsChargerDefine;
   import dataAll.skill.define.SkillDefine;
   
   public class ChooseOneArmsAll
   {
      
      public var arr:Array = [];
      
      public function ChooseOneArmsAll()
      {
         super();
      }
      
      public function init() : void
      {
         this.arr.push(this.armsType());
         this.arr.push(this.hurt());
         this.arr.push(this.special());
         this.arr.push(this.skill());
         this.arr.push(this.godSkill());
      }
      
      private function armsType() : ChooseOneDefineGroup
      {
         var d0:ChooseOneDefineGroup = null;
         var name0:String = null;
         var chargerD0:ArmsChargerDefine = null;
         d0 = new ChooseOneDefineGroup();
         var arr0:Array = Gaming.defineGroup.armsCharger.typeArr;
         for each(name0 in arr0)
         {
            chargerD0 = Gaming.defineGroup.armsCharger.getDefine(name0);
            d0.addBy(name0,chargerD0.cnName);
         }
         d0.name = "armsType";
         d0.cnName = "武器类型";
         d0.max = 1;
         return d0;
      }
      
      private function hurt() : ChooseOneDefineGroup
      {
         var d0:ChooseOneDefineGroup = new ChooseOneDefineGroup();
         d0.addBy("min","高伤害低射速");
         d0.addBy("mid","中伤害中射速");
         d0.addBy("max","低伤害高射速");
         d0.name = "hurt";
         d0.cnName = "伤害与射速";
         d0.max = 1;
         return d0;
      }
      
      private function special() : ChooseOneDefineGroup
      {
         var name0:String = null;
         var cnName0:String = null;
         var d0:ChooseOneDefineGroup = new ChooseOneDefineGroup();
         var arr0:Array = ArmsSpecial.specialArr;
         for each(name0 in arr0)
         {
            cnName0 = ArmsSpecial.getCn(name0);
            d0.addBy(name0,cnName0);
         }
         d0.name = "special";
         d0.cnName = "特殊属性";
         d0.max = 4;
         return d0;
      }
      
      private function skill() : ChooseOneDefineGroup
      {
         var name0:String = null;
         var skillD0:SkillDefine = null;
         var d0:ChooseOneDefineGroup = new ChooseOneDefineGroup();
         var arr0:Array = ArmsSpecialAndSkill.skillArr;
         for each(name0 in arr0)
         {
            skillD0 = Gaming.defineGroup.skill.getDefine(name0);
            d0.addBy(name0,skillD0.cnName);
         }
         d0.name = "skill";
         d0.cnName = "普通技能";
         d0.max = 3;
         return d0;
      }
      
      private function godSkill() : ChooseOneDefineGroup
      {
         var name0:String = null;
         var skillD0:SkillDefine = null;
         var d0:ChooseOneDefineGroup = new ChooseOneDefineGroup();
         var arr0:Array = ArmsSpecialAndSkill.godSkillArr;
         for each(name0 in arr0)
         {
            skillD0 = Gaming.defineGroup.skill.getDefine(name0);
            d0.addBy(name0,skillD0.cnName);
         }
         d0.name = "godSkill";
         d0.cnName = "神级技能";
         d0.max = 1;
         return d0;
      }
   }
}

