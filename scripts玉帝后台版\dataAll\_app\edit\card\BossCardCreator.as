package dataAll._app.edit.card
{
   import com.adobe.crypto.MD5;
   import com.sounto.cf.NumberEncodeObj;
   import com.sounto.utils.ArrayMethod;
   import com.sounto.utils.NumberMethod;
   import com.sounto.utils.ObjectMethod;
   import dataAll._app.edit.boss.BossEditData;
   import dataAll._app.edit.boss.BossEditPro;
   import dataAll.body.define.BodyFather;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.pro.dataList.DataListDefine;
   import dataAll.skill.define.SkillDefine;
   
   public class BossCardCreator
   {
      
      private static const highRanArr:Array = [0,0,0,0,0.77,0.2,0.03];
      
      private static const ranArr:Array = [0.4,0.36,0.2,0.03,0.0078,0.002,0.0002];
      
      private static const ranArr2000:Array = [0,0,0.2,0.03,0.0078,0.002,0.0002];
      
      private static const rareArr:Array = [0,0,0,0.3,0.8,0.8,0.8];
      
      private static const dpsArr:Array = [[1,3],[3,9],[5,10],[10,20],[20,30],[30,35],[35,40],[40,50]];
      
      private static const lifeArr:Array = [[10,30],[30,180],[70,200],[200,500],[500,1000],[1000,1200],[1200,1500],[1500,2000]];
      
      private static const skillArr:Array = [4,5,6,6,6,6,6,6];
      
      private static const rareSkillArr:Array = [0,0,0,1,2,2,3,3];
      
      private static const magicSkillArr:Array = [0,0,0,0,0,0,0,1];
      
      private static const allArr:Array = [ranArr,ranArr2000,rareArr,dpsArr,lifeArr,skillArr,rareSkillArr,magicSkillArr];
      
      private static const allCode:String = "a5deadf9f33e2db9fd9450cad443c45b";
      
      private static const sO:Object = {};
      
      private static const idSO:Object = {};
      
      private static const idCnSO:Object = {};
      
      private static const sA:Array = [];
      
      private static const sRareA:Array = [];
      
      private static const sRareIdObj:Object = {};
      
      private static const sMagicA:Array = [];
      
      private static const sMareIdObj:Object = {};
      
      private static const bossNameArr:Array = [];
      
      private static const rareBossNameArr:Array = ["Sentry"];
      
      public static const niuBossNameArr:Array = ["Madboss","Watchdog","Salamander","IronDog"];
      
      private static const ranProObj:NumberEncodeObj = new NumberEncodeObj();
      
      private static const proArr6:Array = ["dpsAllBlack"];
      
      private static const proArr7:Array = ["dpsAllBlack","hurtAll"];
      
      private static const proArrOther:Array = [];
      
      public function BossCardCreator()
      {
         super();
      }
      
      public static function init() : void
      {
         inSkillListBy("bcardSkill",false,false);
         inSkillListBy("bcardSkillRare",true,false);
         inSkillListBy("bcardSkillMagic",false,true);
         initRanPro();
         inBossArr();
      }
      
      public static function panCodeB() : Boolean
      {
         var c0:String = MD5.hash(String(allArr));
         if(c0 == allCode)
         {
            return true;
         }
         return false;
      }
      
      private static function initRanPro() : void
      {
         ranProObj.addAttribute("chargerMul",0.3);
         ranProObj.addAttribute("lifeAll",0.24);
         ranProObj.addAttribute("weaponDropPro",0.3);
         ranProObj.addAttribute("blackArmsDropPro",0.2);
         ranProObj.addAttribute("blackEquipDropPro",0.2);
         ranProObj.addAttribute("gemDropPro",0.15);
         ranProObj.addAttribute("specialPartsDropPro",0.15);
         ranProObj.addAttribute("rareGeneDropPro",0.2);
         ranProObj.addAttribute("petBookDropPro",0.12);
         ranProObj.addAttribute("dodge",0.08);
         ranProObj.addAttribute("fightDedut",0.12);
         ranProObj.addAttribute("cdMul",0.1);
         ranProObj.addAttribute("moveMul",0.12);
         ranProObj.addAttribute("vehicleDpsMul",0.15);
         ranProObj.addAttribute("damageMul",0.1);
      }
      
      public static function getRanProMax(name0:String) : Number
      {
         if(ranProObj.haveAttribute(name0))
         {
            return ranProObj.getAttribute(name0);
         }
         return -1;
      }
      
      private static function inSkillListBy(listName0:String, rareB0:Boolean, magicB0:Boolean) : void
      {
         var str0:String = null;
         var sO1:* = undefined;
         var idSO1:* = undefined;
         var idCnSO1:* = undefined;
         var sRareA0:* = undefined;
         var sA0:* = undefined;
         var sMagicA0:* = undefined;
         var f0:int = 0;
         var id0:String = null;
         var name0:String = null;
         var d0:SkillDefine = null;
         var dataList0:DataListDefine = Gaming.defineGroup.dataList.getDefine(listName0);
         var strArr0:Array = dataList0.getTrueValueArr();
         for each(str0 in strArr0)
         {
            if(str0 != "")
            {
               f0 = int(str0.indexOf(":"));
               id0 = str0.substring(0,f0);
               name0 = str0.substring(f0 + 1);
               if(id0 != "" && name0 != "")
               {
                  d0 = Gaming.defineGroup.skill.getDefine(name0);
                  if(Boolean(d0))
                  {
                     if(sO.hasOwnProperty(id0) == false)
                     {
                        sO[id0] = name0;
                        idSO[name0] = id0;
                        idCnSO[d0.cnName] = id0;
                     }
                     if(rareB0)
                     {
                        sRareA.push(id0);
                        sRareIdObj[name0] = id0;
                     }
                     else if(magicB0)
                     {
                        sMagicA.push(id0);
                        sMareIdObj[name0] = id0;
                     }
                     else
                     {
                        sA.push(id0);
                     }
                  }
                  else
                  {
                     INIT.showError("bcardSkill不存在技能：" + name0);
                  }
               }
               else
               {
                  INIT.showError("bcardSkill不存在技能和id：" + str0);
               }
            }
         }
         sO1 = sO;
         idSO1 = idSO;
         idCnSO1 = idCnSO;
         sRareA0 = sRareA;
         sA0 = sA;
         sMagicA0 = sMagicA;
      }
      
      private static function inBossArr() : void
      {
         var father0:String = null;
         var bossNameArr0:* = undefined;
         var rareBossNameArr0:* = undefined;
         var arr0:Array = null;
         var d0:NormalBodyDefine = null;
         var noCn0:String = null;
         var name0:String = null;
         var fatherArr0:Array = BodyFather.sumArr;
         for each(father0 in fatherArr0)
         {
            arr0 = Gaming.defineGroup.body.getArrByFather(father0);
            for each(d0 in arr0)
            {
               noCn0 = d0.cnName;
               if(d0.showLevel < 9999 && d0.canBossB)
               {
                  name0 = d0.name;
                  if(BodyFather.noSumNameArr.indexOf(name0) == -1)
                  {
                     if(father0 == BodyFather.wilder || d0.showLevel >= 999)
                     {
                        rareBossNameArr.push(name0);
                        noCn0 = "";
                     }
                     else if(rareBossNameArr.indexOf(name0) == -1)
                     {
                        bossNameArr.push(name0);
                        noCn0 = "";
                     }
                  }
               }
               if(noCn0 != "")
               {
                  INIT.tempTrace("     ++++++++ " + noCn0);
               }
            }
         }
         bossNameArr0 = bossNameArr;
         rareBossNameArr0 = rareBossNameArr;
      }
      
      private static function panBossNiubi(d0:NormalBodyDefine) : void
      {
         var name0:String = null;
         var sd0:SkillDefine = null;
         var arr0:Array = d0.skillArr.concat(d0.getBossSkillArr(false,false));
         var s0:String = "";
         for each(name0 in arr0)
         {
            if(sRareIdObj.hasOwnProperty(name0))
            {
               sd0 = Gaming.defineGroup.skill.getDefine(name0);
               s0 += "、" + sd0.cnName;
            }
         }
         if(s0 != "")
         {
            INIT.tempTrace(d0.cnName + "【" + s0 + "】");
         }
      }
      
      public static function getStarMax() : int
      {
         return skillArr.length;
      }
      
      public static function getSave(cardNum0:int, highB0:Boolean, hsvMul0:Number) : BossCardSave
      {
         var ranArr0:Array = ranArr;
         if(highB0)
         {
            ranArr0 = highRanArr;
         }
         else if(cardNum0 >= 2000)
         {
            ranArr0 = ranArr2000;
         }
         var index0:int = ArrayMethod.getPro_byArrSum(ranArr0);
         var star0:int = index0 + 1;
         return getSaveStar(star0);
      }
      
      public static function getCardPro(star0:int, highB0:Boolean) : Number
      {
         var ranArr0:Array = ranArr;
         if(highB0)
         {
            ranArr0 = highRanArr;
         }
         return ArrayMethod.getElementLimit(ranArr0,star0 - 1);
      }
      
      public static function getSaveStar(star0:int, excludeArr0:Array = null, setBodyName0:String = "") : BossCardSave
      {
         var maxStar0:int = getStarMax();
         if(star0 > maxStar0)
         {
            star0 = maxStar0;
         }
         var i:int = star0 - 1;
         var dps0:Number = getDps(star0);
         var life0:Number = getLife(star0);
         var skill0:Number = Number(skillArr[i]);
         var rareSkill0:Number = Number(rareSkillArr[i]);
         var magicSkill0:Number = Number(magicSkillArr[i]);
         var s0:BossCardSave = new BossCardSave();
         if(setBodyName0 == "")
         {
            setBodyName0 = getBodyName(star0,excludeArr0);
         }
         var bodyD0:NormalBodyDefine = Gaming.defineGroup.body.getDefine(setBodyName0);
         var baseSkillArr0:Array = bodyD0.getEditSkillArr();
         var baseSkillIDArr0:Array = getIdArrBySkillNameArr(baseSkillArr0);
         s0.n = bodyD0.name;
         s0.li = life0;
         s0.dp = dps0;
         s0.s = star0;
         s0.sr = getSkillIdArr(skill0,rareSkill0,magicSkill0,baseSkillIDArr0);
         s0.o = getAddObj(star0);
         s0.lk = star0 >= 6 ? 1 : 0;
         if(star0 >= 8)
         {
            addRanPro(s0.o,star0 - 7,star0);
         }
         return s0;
      }
      
      public static function getSaveStar7() : BossCardSave
      {
         var star0:int = 7;
         var s0:BossCardSave = getSaveStar(star0);
         s0.o = ObjectMethod.remove(s0.o,"hurtAll");
         addRanPro(s0.o,1,star0);
         return s0;
      }
      
      public static function getEvoAgent(da0:BossCardData) : BcardEvoAgent
      {
         var a0:BcardEvoAgent = new BcardEvoAgent();
         var s0:BossCardSave = da0.getCardSave();
         var beforeStar0:int = da0.getStar();
         var star0:int = beforeStar0 + 1;
         var before_i:int = beforeStar0 - 1;
         var i:int = star0 - 1;
         var lifeA0:Array = lifeArr[i];
         var dpsA0:Array = dpsArr[i];
         a0.lifeMin = 2 * lifeA0[0];
         if(a0.lifeMin < s0.li)
         {
            a0.lifeMin = s0.li;
         }
         a0.lifeMax = 2 * lifeA0[1];
         a0.dpsMin = dpsA0[0];
         if(a0.dpsMin < s0.dp)
         {
            a0.dpsMin = s0.dp;
         }
         a0.dpsMax = dpsA0[1];
         a0.skill = skillArr[i] - skillArr[before_i];
         a0.rareSkill = rareSkillArr[i] - rareSkillArr[before_i];
         a0.magicSkill = magicSkillArr[i] - magicSkillArr[before_i];
         if(star0 >= 7)
         {
            a0.ranProNum = star0 - beforeStar0;
         }
         return a0;
      }
      
      public static function evo(da0:BossCardData) : void
      {
         var a0:BcardEvoAgent = getEvoAgent(da0);
         var s0:BossCardSave = da0.getCardSave();
         var star0:int = s0.s + 1;
         s0.s = star0;
         s0.li = a0.getLife();
         s0.dp = a0.getDps();
         var haveSkillIdArr0:Array = da0.getHaveSkillIdArr();
         var newSkillIdArr0:Array = getSkillIdArr(a0.skill,a0.rareSkill,a0.magicSkill,haveSkillIdArr0);
         s0.sr = s0.sr.concat(newSkillIdArr0);
         if(a0.ranProNum > 0)
         {
            addRanPro(s0.o,a0.ranProNum,star0);
         }
         da0.evoEvent();
      }
      
      public static function addRanPro(obj0:Object, num0:int, star0:int) : void
      {
         var ranArr0:Array = null;
         var name0:String = null;
         var v0:Number = NaN;
         var xx0:int = 0;
         var allArr0:Array = ranProObj.getNameArr();
         var nowArr0:Array = ObjectMethod.getNameArr(obj0);
         var newArr0:Array = ArrayMethod.deductArr(allArr0,nowArr0);
         if(newArr0.length > 0)
         {
            ranArr0 = ArrayMethod.getRandomArray(newArr0,num0);
            for each(name0 in ranArr0)
            {
               if(obj0.hasOwnProperty(name0) == false)
               {
                  v0 = getRanProValue(name0,star0);
                  obj0[name0] = v0;
               }
               else
               {
                  xx0 = 0;
               }
            }
         }
         else
         {
            xx0 = 0;
         }
      }
      
      private static function getRanProValue(name0:String, star0:int) : Number
      {
         var max0:Number = ranProObj.getAttribute(name0);
         var min0:Number = max0 / 2;
         if(star0 < 8)
         {
            min0 = max0 / 5;
            max0 /= 2;
         }
         var v0:Number = NumberMethod.getRandom(min0,max0);
         if(v0 < 0.01)
         {
            v0 = 0.01;
         }
         return NumberMethod.toFixed(v0,2);
      }
      
      private static function getLife(star0:int) : Number
      {
         var i:int = star0 - 1;
         var lifeA0:Array = lifeArr[i];
         return Math.ceil(2 * NumberMethod.randomInRange(lifeA0[0],lifeA0[1]));
      }
      
      private static function getDps(star0:int) : Number
      {
         var i:int = star0 - 1;
         var dpsA0:Array = dpsArr[i];
         return Math.ceil(NumberMethod.randomInRange(dpsA0[0],dpsA0[1]));
      }
      
      private static function getAddProArr(star0:int) : Array
      {
         if(star0 == 6)
         {
            return proArr6;
         }
         if(star0 == 7 || star0 == 8)
         {
            return proArr7;
         }
         return proArrOther;
      }
      
      private static function getAddProValue(name0:String) : Number
      {
         if(name0 == "dpsAllBlack")
         {
            return NumberMethod.toFixed(NumberMethod.getRandom(0.05,0.1),2);
         }
         if(name0 == "hurtAll")
         {
            return NumberMethod.toFixed(NumberMethod.getRandom(0.05,0.1),2);
         }
         return 0;
      }
      
      private static function getAddObj(star0:int) : Object
      {
         var pro0:String = null;
         var obj0:Object = {};
         var proArr0:Array = getAddProArr(star0);
         for each(pro0 in proArr0)
         {
            obj0[pro0] = getAddProValue(pro0);
         }
         return obj0;
      }
      
      private static function getBodyName(star0:int, excludeArr0:Array = null) : String
      {
         var rarePro0:Number = Number(rareArr[star0]);
         var bossArr0:Array = bossNameArr;
         if(rarePro0 > 0 && Math.random() < rarePro0)
         {
            bossArr0 = rareBossNameArr;
         }
         if(Boolean(excludeArr0) && excludeArr0.length > 0)
         {
            bossArr0 = ArrayMethod.deductArr(bossArr0,excludeArr0);
         }
         return ArrayMethod.getRandomOne(bossArr0);
      }
      
      public static function getSkillId(name0:String) : String
      {
         if(idSO.hasOwnProperty(name0))
         {
            return idSO[name0];
         }
         return name0;
      }
      
      public static function getSkillName(id0:String) : String
      {
         if(sO.hasOwnProperty(id0))
         {
            return sO[id0];
         }
         return id0;
      }
      
      public static function isRareSkill(name0:String) : Boolean
      {
         var id0:String = getSkillId(name0);
         return sRareA.indexOf(id0) >= 0;
      }
      
      public static function isMagicSkill(name0:String) : Boolean
      {
         var id0:String = getSkillId(name0);
         return sMagicA.indexOf(id0) >= 0;
      }
      
      public static function getSkillNameArrByIdArr(idArr0:Array) : Array
      {
         var id0:String = null;
         var name0:String = null;
         var arr0:Array = [];
         for each(id0 in idArr0)
         {
            name0 = getSkillName(id0);
            if(name0 != null)
            {
               arr0.push(name0);
            }
         }
         return arr0;
      }
      
      public static function getIdArrBySkillNameArr(nameArr0:Array) : Array
      {
         var name0:String = null;
         var id0:String = null;
         var arr0:Array = [];
         for each(name0 in nameArr0)
         {
            id0 = getSkillId(name0);
            if(id0 != null)
            {
               arr0.push(id0);
            }
         }
         return arr0;
      }
      
      public static function getIdArrBySkillCnArr(cnArr0:Array) : Array
      {
         var cn0:String = null;
         var id0:String = null;
         var arr0:Array = [];
         for each(cn0 in cnArr0)
         {
            id0 = idCnSO[cn0];
            if(id0 != null)
            {
               arr0.push(id0);
            }
            else
            {
               id0 = cn0;
            }
         }
         return arr0;
      }
      
      public static function getSkillIdArr(num0:int, rare0:int, magic0:int, excludeArr0:Array = null) : Array
      {
         if(excludeArr0 == null)
         {
            excludeArr0 = [];
         }
         var arr0:Array = [];
         if(num0 > 0)
         {
            arr0 = getRanSkillArr(sA,num0,excludeArr0).concat(arr0);
         }
         if(rare0 > 0)
         {
            arr0 = getRanSkillArr(sRareA,rare0,excludeArr0).concat(arr0);
         }
         if(magic0 > 0)
         {
            arr0 = getRanSkillArr(sMagicA,magic0,excludeArr0.concat(arr0)).concat(arr0);
         }
         return arr0;
      }
      
      private static function getRanSkillArr(skillArr0:Array, num0:int, excludeArr0:Array = null) : Array
      {
         var sA0:Array = skillArr0;
         if(Boolean(excludeArr0) && excludeArr0.length > 0)
         {
            sA0 = ArrayMethod.deductArr(sA0,excludeArr0);
         }
         return ArrayMethod.getRandomArray(sA0,num0);
      }
      
      public static function editToCardSave(e0:BossEditData) : BossCardSave
      {
         var s0:BossCardSave = new BossCardSave();
         s0.n = e0.name;
         s0.s = e0.getStar();
         s0.sr = getIdArrBySkillNameArr(e0.getSkillNameArr());
         s0.li = e0.getValueByDefName(BossEditPro.li);
         s0.dp = e0.getValueByDefName(BossEditPro.dp);
         return s0;
      }
      
      public static function editToCardXML(e0:BossEditData) : String
      {
         var s0:BossCardSave = editToCardSave(e0);
         return s0.getXMLStr();
      }
      
      public static function repairSkill(s0:BossCardSave) : void
      {
         if(s0.n == "RifleHornetShooter")
         {
            ArrayMethod.replace(s0.sr,"meltFlamerPurgold","21_14");
         }
         else if(s0.n == "TriceratopsEgg")
         {
            ArrayMethod.replace(s0.sr,"blackHoleDemon","23_13");
         }
      }
   }
}

