package dataAll._player.supple
{
   import UI.UIOrder;
   import UI.test.SaveTestBox;
   import com.sounto.utils.StringMethod;
   import dataAll._app.city.CitySave;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.goods.define.PriceType;
   import dataAll._app.task.TaskData;
   import dataAll._app.task.define.TaskDefineGroup;
   import dataAll._app.task.define.TaskType;
   import dataAll._app.worldMap.save.WorldMapSave;
   import dataAll._data.ConstantDefine;
   import dataAll._player.PlayerData;
   import dataAll._player.base.PlayerMainSave;
   import dataAll._player.more.NormalPlayerData;
   import dataAll._player.role.RoleName;
   import dataAll.arms.ArmsData;
   import dataAll.arms.creator.ArmsEvoCtrl;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.body.define.BodySex;
   import dataAll.equip.EquipData;
   import dataAll.equip.creator.EquipEvoCtrl;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.device.DeviceData;
   import dataAll.equip.vehicle.VehicleData;
   import dataAll.equip.vehicle.VehicleDataCreator;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import dataAll.things.ThingsData;
   import dataAll.things.define.ThingsDefine;
   
   public class PlayerDataSupple
   {
      
      public static var versionTooHighB:Boolean = false;
      
      public function PlayerDataSupple()
      {
         super();
      }
      
      public static function supple(P:PlayerData) : void
      {
         openPan(P);
         morePan(P);
         taskOpen(P);
         dealArms(P);
         darkgoldEquip(P);
         newVersion(P);
      }
      
      private static function newVersion(P:PlayerData) : void
      {
         versionTooHighB = false;
         var versionNumber0:Number = ConstantDefine.getMainVersionNumber(Number(ConstantDefine.versionNumber));
         var s_v0:Number = ConstantDefine.getMainVersionNumber(P.main.save.versionNumber);
         var trueVersionNumber0:Number = Number(ConstantDefine.versionNumber);
         var trueS_v0:Number = P.main.save.versionNumber;
         SaveTestBox.addText("【" + Gaming.getUid() + "_" + Gaming.getSaveIndex() + "】" + "存档版本:" + trueS_v0 + " → " + trueVersionNumber0);
         if(trueS_v0 < trueVersionNumber0)
         {
            autoUseThings(P);
            if(trueS_v0 == 34.6)
            {
               moreGirlSkill(P);
            }
            if(trueS_v0 == 34.6 || trueS_v0 == 34.61)
            {
               equip346(P);
            }
            if(trueS_v0 < 34.5)
            {
               timeCapsuleDeal(P);
            }
            if(trueS_v0 < 34.01)
            {
               blackArms3401(P);
            }
            if(trueS_v0 < 33.67)
            {
               mainAddGift(P);
            }
            if(trueS_v0 == 29)
            {
               thingsToParts(P);
            }
            if(s_v0 < 27.72)
            {
               P.getSave().setting.partsSaB = false;
            }
            if(s_v0 == 28.3)
            {
               reloadGap(P);
            }
            if(trueS_v0 < 28.72)
            {
               phage(P);
            }
            if(s_v0 < 23.2)
            {
               loveShell(P);
            }
         }
         if(s_v0 < versionNumber0)
         {
            P.getSave().guide.arenaSeasonInfo = false;
            if(s_v0 < 30)
            {
               P.pet.dealRed496();
            }
            if(s_v0 < 17.9)
            {
               equipSite(s_v0,P);
            }
            if(s_v0 < 18)
            {
               armsSite(s_v0,P);
            }
            if(s_v0 < 20.9)
            {
               thingsParts(P);
            }
            if(s_v0 < 21.1)
            {
               P.goods.save.clearBuyObj(["zongzi","guiCake"]);
            }
            if(s_v0 < 27.3)
            {
               armsEvoBack(P);
            }
            if(s_v0 < 30.6)
            {
               lastLevelName(P);
            }
            if(s_v0 < 32.3)
            {
               gift18(P);
            }
            if(s_v0 < 33.7)
            {
               addSmeltPlan(P);
            }
            if(s_v0 < 34.6)
            {
               backGirlSkill(P);
            }
            if(s_v0 < PriceType.activeVer)
            {
               P.goods.save.clearBuyObj([PriceType.PUMPKIN]);
            }
            P.thingsBag.removeName("zongzi");
            wearNoArmsTip(P);
         }
         if(trueS_v0 > trueVersionNumber0)
         {
            versionTooHighB = true;
         }
         if(!versionTooHighB)
         {
            P.main.save.versionNumber = trueVersionNumber0;
         }
      }
      
      private static function equip346(P:PlayerData) : void
      {
         var da0:EquipData = null;
         SaveTestBox.addText("---- 执行equip346");
         P.main.save.maxDp = 0;
         var arr0:Array = P.getEquipDataArr(true,true,true,false,true);
         for each(da0 in arr0)
         {
            da0.save.deal346();
         }
      }
      
      private static function backGirlSkill(P:PlayerData) : void
      {
         var trueNum0:int = 0;
         var num0:int = getGirlSkillNum(BodySex.FEMALE,P);
         if(num0 > 0)
         {
            if(num0 > 1500)
            {
               num0 = 1500;
            }
            trueNum0 = num0;
            P.thingsBag.addDataByName("nuclearStone",trueNum0);
            P.main.save.girlSkillNum += trueNum0;
            SaveTestBox.addText("backGirlSkill返还核能石：" + trueNum0);
            if(Gaming.uiGroup.alertBox.visible == false)
            {
               Gaming.uiGroup.alertBox.showSuccess("女性技能位价格已调整为和小白一致，现补偿" + trueNum0 + "个核能石。");
            }
         }
      }
      
      private static function getGirlSkillNum(sex0:String, P:PlayerData) : Number
      {
         var pd0:NormalPlayerData = null;
         var i:int = 0;
         var unlockB0:Boolean = false;
         var gold0:Number = NaN;
         var arr0:Array = P.getPlayerDataArr();
         var num0:int = 0;
         for each(pd0 in arr0)
         {
            if(pd0.heroData.def.sex == sex0)
            {
               for(i = 5; i <= 9; i++)
               {
                  unlockB0 = pd0.skill.saveGroup.getUnlockBySite(i - 1);
                  if(unlockB0)
                  {
                     gold0 = Gaming.defineGroup.must.getSkillGoldGap(i);
                     num0 += gold0;
                  }
               }
            }
         }
         return num0;
      }
      
      private static function moreGirlSkill(P:PlayerData) : void
      {
         var bagNum0:Number = NaN;
         var useNum0:Number = NaN;
         var body0:int = getGirlSkillNum(BodySex.MALE,P);
         var girl0:int = getGirlSkillNum(BodySex.FEMALE,P);
         var num0:int = body0 - girl0;
         var main0:PlayerMainSave = P.main.save;
         SaveTestBox.addText("moreGirlSkilll 男性：" + body0 + "  女性：" + girl0);
         if(num0 > 0)
         {
            bagNum0 = P.thingsBag.getThingsNum("nuclearStone");
            useNum0 = 0;
            if(bagNum0 < num0)
            {
               useNum0 = bagNum0;
               P.thingsBag.removeName("nuclearStone");
               num0 -= bagNum0;
            }
            else
            {
               useNum0 = num0;
               P.thingsBag.useThings("nuclearStone",num0);
               num0 = 0;
            }
            main0.delSS = useNum0;
            SaveTestBox.addText("moreGirlSkilll 扣除：" + useNum0 + "  缺口：" + (body0 - girl0 - useNum0));
         }
         main0.SkillNum1 = girl0;
         main0.SkillNum2 = body0;
      }
      
      public static function oweNuclearStone() : void
      {
         var bagNum0:Number = NaN;
         var useNum0:Number = NaN;
         var P:PlayerData = Gaming.PG.da;
         var main0:PlayerMainSave = P.main.save;
         var owe0:Number = main0.getOweNuclearStone();
         if(owe0 > 0)
         {
            bagNum0 = P.thingsBag.getThingsNum("nuclearStone");
            if(bagNum0 >= owe0)
            {
               useNum0 = owe0;
               P.thingsBag.useThings("nuclearStone",useNum0);
               main0.delSS += useNum0;
               SaveTestBox.addText("moweNuclearStone 回收核能石：" + useNum0);
            }
            else
            {
               SaveTestBox.addText("moweNuclearStone 核能石不足：" + bagNum0 + "《" + owe0);
            }
         }
      }
      
      private static function timeCapsuleDeal(P:PlayerData) : void
      {
         var taskName0:String = null;
         var nowNum0:Number = NaN;
         var daArr0:Array = null;
         var da0:EquipData = null;
         var giftB0:Boolean = false;
         var dda0:DeviceData = null;
         var dlv0:int = 0;
         var getNum0:Number = 0;
         var taskArr:Array = ["timeCapsule11","timeCapsule2","timeCapsule3"];
         for each(taskName0 in taskArr)
         {
            giftB0 = P.task.isOverB(taskName0);
            if(giftB0)
            {
               getNum0 += 3;
            }
         }
         getNum0 += P.drop.save.dayAll.getAttribute("timeCapsule_1");
         nowNum0 = 0;
         daArr0 = P.getEquipDataArr(true,true,true,false,true);
         for each(da0 in daArr0)
         {
            dda0 = da0 as DeviceData;
            if(Boolean(dda0))
            {
               if(dda0.deviceDefine.baseLabel == "timeCapsule")
               {
                  dlv0 = dda0.deviceDefine.lv;
                  if(dlv0 == 1)
                  {
                     nowNum0 += 80;
                  }
                  else if(dlv0 == 2)
                  {
                     nowNum0 += 80 + 80;
                  }
                  else if(dlv0 >= 3)
                  {
                     nowNum0 += 80 + 80 + 120;
                  }
               }
            }
         }
         nowNum0 += P.thingsBag.getThingsNum("timeCapsule_1");
         if(nowNum0 > getNum0 + 50)
         {
            P.drop.save.timeCMore = nowNum0 - getNum0;
         }
      }
      
      private static function blackArms3401(P:PlayerData) : void
      {
         var da0:ArmsData = null;
         var lv0:int = 0;
         var max0:Number = NaN;
         var dps0:Number = NaN;
         var arr0:Array = P.getArmsDataArr(true,true,true,true);
         for each(da0 in arr0)
         {
            if(da0.isRandomB() && da0.save.addLevel > 0)
            {
               lv0 = da0.save.getTrueLevel();
               max0 = 5 + Gaming.defineGroup.normal.getArmsDps(lv0 + 1);
               dps0 = da0.getTestBaseShowDps();
               if(dps0 > max0)
               {
                  da0.save.bh = da0.save.hurtRatio;
                  da0.setTestBaseShowDps(max0);
                  da0.fleshOriginalData();
               }
            }
         }
      }
      
      private static function mainAddGift(P:PlayerData) : void
      {
         var arr0:Array = null;
         var nameArr0:Array = null;
         var da0:TaskData = null;
         if(P.main.save.t38 == false)
         {
            P.main.save.t38 == true;
            P.task.dayInitByFather(TaskType.DAY);
            P.task.saveGroup.setTodayCompleteNum(TaskType.DAY,0);
            P.task.saveGroup.setTodayCompleteNum(TaskType.TREASURE,0);
            P.task.saveGroup.setTodayCompleteNum(TaskType.EXTRA,0);
            arr0 = P.task.getDataArrByType(TaskType.MAIN);
            nameArr0 = TaskDefineGroup.mainAddGiftArr;
            for each(da0 in arr0)
            {
               if(da0.isOver())
               {
                  if(nameArr0.indexOf(da0.def.name) >= 0)
                  {
                     P.thingsBag.addDataByName("allBlackCash",20);
                     P.thingsBag.addDataByName("allBlackEquipCash",20);
                     P.thingsBag.addDataByName("armsRadium",20);
                     P.thingsBag.addDataByName("armsTitanium",20);
                  }
               }
            }
         }
      }
      
      private static function addSmeltPlan(P:PlayerData) : void
      {
         var arr0:Array = null;
         var S0:CitySave = P.city.save;
         if(S0.allNum < 10 && S0.planArr.length == 0)
         {
            arr0 = ["镭晶钛晶：周年碎片、78级零件箱"];
            arr0.push("镭晶钛晶2：周年碎片、生命药瓶、78级零件箱、72级零件箱、69级零件箱、队友生命药瓶");
            arr0.push("强化剂：强化剂、78级零件箱");
            arr0.push("精石光能石：血石、生命药瓶、队友生命药瓶、弹药箱、78级零件箱");
            arr0.push("强化石神能石：幻想钥匙、勇气钥匙、能量钥匙、胜利钥匙、强化石、转化石");
            arr0.push("远古传奇宝箱：72级零件箱、78级零件箱、稀有宝箱、生命药瓶、队友生命药瓶、弹药箱");
            arr0.push("万能载具碎片：先知碎片、制裁者碎片、泰坦碎片、生命药瓶");
            arr0.push("泰坦碎片：制裁者碎片、先知碎片、万能载具碎片、78级零件箱、75级零件箱");
            arr0.push("副手箱：装置箱、生命药瓶、队友生命药瓶、弹药箱、75级零件箱");
            S0.planArr = arr0;
         }
      }
      
      private static function gift18(P:PlayerData) : void
      {
         if(P.main.save.gift18 == false)
         {
            GiftAddit.add(Gaming.defineGroup.gift.getOne("realName"));
         }
      }
      
      private static function lastLevelName(P:PlayerData) : void
      {
         var sobj0:Object = null;
         var n:* = undefined;
         var name0:String = null;
         var s0:WorldMapSave = null;
         var lastName0:String = null;
         if(P.task.isCompleteB("WoTu_1M"))
         {
            sobj0 = P.worldMap.saveGroup.obj;
            for(n in sobj0)
            {
               name0 = n as String;
               s0 = sobj0[n];
               lastName0 = Gaming.defineGroup.worldMap.getLastLevel(name0);
               if(lastName0 != "" && lastName0 != s0.levelName)
               {
                  s0.levelName = lastName0;
               }
            }
         }
      }
      
      private static function autoUseThings(P:PlayerData) : void
      {
         var da0:ThingsData = null;
         var d0:ThingsDefine = null;
         var darr0:Array = P.thingsBag.dataArr.concat();
         for each(da0 in darr0)
         {
            d0 = da0.save.getDefine();
            if(Boolean(d0))
            {
               if(d0.isShopAutoUseB())
               {
                  P.thingsBag.useThings(d0.name,da0.getNowNum(),true);
               }
            }
            else
            {
               P.thingsBag.removeData(da0);
            }
         }
      }
      
      private static function thingsToParts(P:PlayerData) : void
      {
         var name0:String = null;
         var v0:Number = NaN;
         var nameArr0:Array = ["huntParts_1","acidicParts_1"];
         for each(name0 in nameArr0)
         {
            v0 = P.thingsBag.getThingsNum(name0);
            if(v0 > 0)
            {
               P.thingsBag.useThings(name0,v0,false,false);
               P.partsBag.addDataByName(name0,v0);
            }
         }
      }
      
      private static function phage(P:PlayerData) : void
      {
         var da0:EquipData = P.findEquipDataByName("phage",true);
         if(Boolean(da0))
         {
            P.thingsBag.addDataByName("disEver22_10");
            Gaming.uiGroup.alertBox.showSuccess("获得噬原补偿礼包，在10月的第一次版本更新后可使用。");
         }
      }
      
      private static function reloadGap(P:PlayerData) : void
      {
         var dealNum0:Number = NaN;
         var arr0:Array = null;
         var da0:ArmsData = null;
         var rd0:ArmsRangeDefine = null;
         var rangeArr0:Array = null;
         var min0:Number = NaN;
         var max0:Number = NaN;
         var now0:Number = NaN;
         var i:int = 0;
         var taskDa0:TaskData = P.task.getTaskDataByName("madSame");
         if(Boolean(taskDa0))
         {
            dealNum0 = 0;
            arr0 = P.getArmsDataArr(true,true,true,true);
            for each(da0 in arr0)
            {
               rd0 = Gaming.defineGroup.bullet.getArmsRangeDefine(da0.def.name);
               rangeArr0 = rd0.getRangeArr("reloadGap");
               if(Boolean(rangeArr0))
               {
                  min0 = Number(rangeArr0[0]);
                  max0 = Number(rangeArr0[1]);
                  now0 = da0.save.reloadGap;
                  if(now0 < min0 - 1e-7)
                  {
                     if(min0 == max0)
                     {
                        now0 = min0;
                     }
                     else if(now0 <= 0)
                     {
                        now0 = max0;
                     }
                     else
                     {
                        for(i = 0; i < 100; i++)
                        {
                           now0 *= 3;
                           if(now0 >= min0 - 1e-7)
                           {
                              if(now0 <= max0 + 1e-7)
                              {
                                 break;
                              }
                              now0 = max0;
                              break;
                           }
                        }
                        if(now0 < min0 - 1e-7)
                        {
                           now0 = max0;
                        }
                     }
                     da0.save.reloadGap = now0;
                     dealNum0++;
                  }
               }
            }
            if(dealNum0 > 0)
            {
               if(P.main.save.isZuobiB)
               {
                  if(P.main.save.zuobiReason.indexOf("28.3：") >= 0)
                  {
                     P.main.save.isZuobiB = false;
                  }
               }
            }
         }
      }
      
      private static function wearNoArmsTip(P:PlayerData) : void
      {
         var arr0:Array = null;
         var cnArr0:Array = null;
         var pd0:NormalPlayerData = null;
         var cn0:String = null;
         var tip0:String = null;
         if(P.level > 10)
         {
            arr0 = P.getPlayerDataArr();
            cnArr0 = [];
            for each(pd0 in arr0)
            {
               if(pd0.arms.dataArr.length == 0)
               {
                  cn0 = pd0.getSayCn();
                  cnArr0.push(cn0);
               }
            }
            if(cnArr0.length > 0)
            {
               tip0 = StringMethod.concatStringArr(cnArr0,99) + "没有装载武器，\n进入关卡后将会出现错误。";
               UIOrder.alertError(tip0);
            }
         }
      }
      
      private static function armsEvoBack(P:PlayerData) : void
      {
         var garr0:Array = null;
         var all0:GiftAddDefineGroup = null;
         var da0:ArmsData = null;
         var g0:GiftAddDefineGroup = null;
         if(P.main.save.ab27 == "")
         {
            garr0 = P.getArmsDataArr(true,true,true,true);
            all0 = new GiftAddDefineGroup();
            for each(da0 in garr0)
            {
               g0 = ArmsEvoCtrl.countEvoMore(da0);
               if(Boolean(g0))
               {
                  all0.merge(g0);
               }
            }
            if(all0.arr.length > 0)
            {
               GiftAddit.addAndAutoBagSpacePan(all0,"返还武器进阶材料：\n" + all0.getDescription(3),"");
               P.main.save.ab27 = all0.getDescription(999,false);
            }
            else
            {
               P.main.save.ab27 = "无";
            }
         }
      }
      
      private static function weekTaskMap(P:PlayerData) : void
      {
         var da0:TaskData = null;
         var mapNameArr0:Array = ["HospitalUnder","MainUpland"];
         var arr0:Array = P.task.getDataArrByType(TaskType.WEEK);
         var gift0:GiftAddDefineGroup = new GiftAddDefineGroup();
         for each(da0 in arr0)
         {
            if(mapNameArr0.indexOf(da0.save.map) >= 0)
            {
               gift0 = da0.getGift();
               da0.rebirth(true);
            }
         }
         if(gift0.arr.length > 0 && !P.main.save.wt27B)
         {
            gift0 = gift0.clone();
            gift0.addNumMul(3);
            GiftAddit.addAndAutoBagSpacePan(gift0,"领取每周任务补偿礼包：\n" + gift0.getDescription(),"");
            P.main.save.wt27B = true;
         }
      }
      
      private static function loveShell(P:PlayerData) : void
      {
         P.equip.saveGroup.replaceItemsName("loveShell","loveShell_1");
         P.equipBag.saveGroup.replaceItemsName("loveShell","loveShell_1");
      }
      
      private static function equipSite(saveVer0:Number, P:PlayerData) : void
      {
         if(saveVer0 > 0)
         {
            P.equipBag.saveGroup.unlockNextSiteNum(24);
         }
      }
      
      private static function armsSite(saveVer0:Number, P:PlayerData) : void
      {
         if(saveVer0 > 0)
         {
            P.armsBag.saveGroup.unlockNextSiteNum(8);
            P.partsBag.saveGroup.unlockNextSiteNum(12);
         }
      }
      
      private static function dealArms(P:PlayerData) : void
      {
         var pd0:NormalPlayerData = null;
         var pdArr0:Array = P.getPlayerDataArr();
         for each(pd0 in pdArr0)
         {
            pd0.arms.delNoPositionItems();
         }
      }
      
      private static function taskOpen(P:PlayerData) : void
      {
         if(P.worldMap.saveGroup.getWinB("NanTang"))
         {
            P.task.unlockOneByType(TaskType.MAIN);
         }
      }
      
      private static function morePan(P:PlayerData) : void
      {
         if(P.more.saveGroup.lockLen < 2)
         {
            P.more.saveGroup.unlockTo(2 - 1);
         }
         P.addMoreByUnitName(RoleName.WenJie,false);
         var task_da0:TaskData = P.task.getTaskDataByName("BaiLu_ZangShi");
         if(task_da0 is TaskData)
         {
            if(task_da0.state == "complete" || task_da0.state == "over")
            {
               P.addMoreByUnitName(RoleName.ZangShi,false);
            }
         }
      }
      
      private static function openPan(P:PlayerData) : void
      {
         if(P.worldMap.saveGroup.getWinB("BaiLu"))
         {
            P.worldMap.saveGroup.superNum = 3;
            Gaming.PG.save.guide.task = true;
         }
         else if(P.worldMap.saveGroup.getWinB("YangMei"))
         {
            P.worldMap.saveGroup.superNum = 2;
         }
         else if(P.worldMap.saveGroup.getWinB("WoTu"))
         {
            P.worldMap.saveGroup.superNum = 1;
         }
         if(P.worldMap.saveGroup.getWinB("ShuiSheng"))
         {
            P.worldMap.saveGroup.dropRocketB = true;
         }
         armsSitePan(P);
      }
      
      public static function armsSitePan(P:PlayerData) : void
      {
         if(P.worldMap.saveGroup.getWinB("BeiDou"))
         {
            P.arms.saveGroup.unlockSite(2);
         }
      }
      
      private static function darkgoldEquip(P:PlayerData) : void
      {
         var garr0:Array = null;
         var da0:EquipData = null;
         var d0:EquipDefine = null;
         var name0:String = null;
         var obj0:Object = null;
         var v0:Number = NaN;
         var newV0:Number = NaN;
         if(!P.gift.save.darkgoldSuppleB)
         {
            P.gift.save.darkgoldSuppleB = true;
            garr0 = P.getEquipDataArr(true,true,true,false,true);
            for each(da0 in garr0)
            {
               if(da0.getColor() == EquipColor.DARKGOLD)
               {
                  d0 = da0.save.getDefine();
                  if(d0.name == "madamor_head")
                  {
                     name0 = "dpsAllBlack";
                     obj0 = da0.save.obj;
                     v0 = Number(obj0[name0]);
                     newV0 = (v0 - 0.1) / 3 * 2 + 0.05;
                     obj0[name0] = newV0;
                     da0.save.obj = obj0;
                     da0.save.evoLv = 9;
                  }
               }
            }
         }
      }
      
      private static function delNoThings(P:PlayerData) : void
      {
         var da0:ThingsData = null;
         var d0:ThingsDefine = null;
         var arr0:Array = P.thingsBag.dataArr;
         var delArr0:Array = [];
         for each(da0 in arr0)
         {
            d0 = da0.save.getDefine();
            if(!d0)
            {
               delArr0.push(da0);
            }
         }
         if(delArr0.length < 5)
         {
            P.thingsBag.removeDataArr(delArr0);
         }
      }
      
      private static function thingsParts(P:PlayerData) : void
      {
         var da0:ThingsData = null;
         var d0:ThingsDefine = null;
         var name0:String = null;
         var arr0:Array = P.thingsBag.dataArr;
         var delArr0:Array = [];
         for each(da0 in arr0)
         {
            d0 = da0.save.getDefine();
            if(d0.isPartsB())
            {
               delArr0.push(da0);
            }
         }
         P.thingsBag.removeDataArr(delArr0);
         for each(da0 in delArr0)
         {
            name0 = da0.save.name;
            if(P.partsBag.getSpaceSiteNum() > 0 || Boolean(P.partsBag.getDataBySaveName(name0)))
            {
               P.partsBag.addDataByName(name0,da0.getNowNum());
            }
         }
      }
      
      public static function dealSummer(P:PlayerData) : void
      {
      }
      
      private static function dealSmelt() : void
      {
      }
      
      public static function smeltArms() : String
      {
         var now0:Number = getArmsChipNum();
         var drop0:Number = PD.drop.save.allNintyArmsBlackNum;
         var s0:String = "smeltArms：" + now0 + "-" + drop0 + "=" + (now0 - drop0);
         tra(s0);
         if(now0 > drop0 + 2800)
         {
            return s0;
         }
         return "";
      }
      
      public static function getArmsGap() : int
      {
         var now0:Number = getArmsChipNum();
         var drop0:Number = PD.drop.save.allNintyArmsBlackNum;
         return now0 - drop0;
      }
      
      private static function getArmsChipNum() : Number
      {
         var da0:ArmsData = null;
         var chipNum0:int = 0;
         var mustNum0:Number = NaN;
         var daChipNum0:int = 0;
         var arr0:Array = PD.getArmsDataArr(true,true,true,true);
         for each(da0 in arr0)
         {
            if(da0.def.composeLv >= 86 && da0.getColor() == "black")
            {
               if(da0.def.isCanEvoB())
               {
                  mustNum0 = ArmsEvoCtrl.getAllChipMustNum(da0.save.evoLv);
                  daChipNum0 += mustNum0;
                  tra(da0.def.cnName + "：evoLv：" + da0.save.evoLv + "    " + mustNum0);
               }
            }
         }
         chipNum0 = PD.thingsBag.getBlackArmsNum86();
         return daChipNum0 + chipNum0;
      }
      
      public static function smeltEquip() : String
      {
         var now0:Number = getEquipChipNum();
         var drop0:Number = PD.drop.save.allNintyEquipBlackNum;
         var s0:String = "smeltEquip：" + now0 + "-" + drop0 + "=" + (now0 - drop0);
         tra(s0);
         if(now0 > drop0 + 2500)
         {
            return s0;
         }
         return "";
      }
      
      public static function getEquipGap() : int
      {
         var now0:Number = getEquipChipNum();
         var drop0:Number = PD.drop.save.allNintyEquipBlackNum;
         return now0 - drop0;
      }
      
      private static function getEquipChipNum() : Number
      {
         var da0:EquipData = null;
         var chipNum0:int = 0;
         var equipD0:EquipDefine = null;
         var mustNum0:Number = NaN;
         var daChipNum0:int = 0;
         var arr0:Array = PD.getEquipDataArr(true,true,true,true,true);
         for each(da0 in arr0)
         {
            equipD0 = da0.save.getDefine();
            if(equipD0.isBlack90() && equipD0.isCanEvoB() && da0.getColor() == "black")
            {
               mustNum0 = EquipEvoCtrl.getAllChipMustNum(da0.save.evoLv);
               daChipNum0 += mustNum0;
               tra(equipD0.cnName + "：evoLv：" + da0.save.evoLv + "    " + mustNum0);
            }
         }
         chipNum0 = PD.thingsBag.getBlackEquipNum86();
         return daChipNum0 + chipNum0;
      }
      
      public static function smeltRadium() : String
      {
         var arms0:Number = getRadiumArmsNum();
         var equip0:Number = getRadiumEquipNum();
         var things0:Number = PD.thingsBag.getThingsNum("armsRadium");
         var num0:Number = arms0 + equip0 + things0;
         var goodD0:GoodsDefine = Gaming.defineGroup.goods.getDefine("armsRadium");
         var shop0:Number = PD.getSave().pay.getNum(goodD0.propId);
         var max0:Number = 25000 + shop0;
         var s0:String = "镭晶num:" + num0 + "等于" + arms0 + "+" + equip0 + "+" + things0 + " 》max:" + max0;
         tra(s0);
         if(num0 > max0)
         {
            return "镭晶数量太多：" + s0;
         }
         return "";
      }
      
      private static function getRadiumArmsNum() : Number
      {
         var da0:ArmsData = null;
         var mustNum0:Number = NaN;
         var daChipNum0:int = 0;
         var arr0:Array = PD.getArmsDataArr(true,true,true,true);
         for each(da0 in arr0)
         {
            if(da0.def.composeLv >= 86 && da0.getColor() == "black")
            {
               if(da0.def.isCanEvoB())
               {
                  mustNum0 = ArmsEvoCtrl.getAllRadiumMustNum(da0.save.evoLv);
                  daChipNum0 += mustNum0;
                  tra(da0.def.cnName + "：evoLv：" + da0.save.evoLv + "    镭晶：" + mustNum0);
               }
            }
         }
         return daChipNum0;
      }
      
      private static function getRadiumEquipNum() : Number
      {
         var da0:EquipData = null;
         var equipD0:EquipDefine = null;
         var mustNum0:Number = NaN;
         var daChipNum0:int = 0;
         var arr0:Array = PD.getEquipDataArr(true,true,true,true,true);
         for each(da0 in arr0)
         {
            equipD0 = da0.save.getDefine();
            if(equipD0.isBlack90() && equipD0.isCanEvoB() && da0.getColor() == "black")
            {
               mustNum0 = ArmsEvoCtrl.getAllRadiumMustNum(da0.save.evoLv);
               daChipNum0 += mustNum0;
               tra(equipD0.cnName + "：evoLv：" + da0.save.evoLv + "    镭晶：" + mustNum0);
            }
         }
         return daChipNum0;
      }
      
      public static function vehicleSkillBack() : void
      {
         var daArr0:Array = null;
         var g0:GiftAddDefineGroup = null;
         var da000:EquipData = null;
         var da0:VehicleData = null;
         var skillNameArr0:Array = null;
         var newNameArr0:Array = null;
         var skillName0:String = null;
         var ng0:GiftAddDefineGroup = null;
         var tip0:String = null;
         if(!PD.main.save.vehicleBackB)
         {
            daArr0 = Gaming.PG.da.getEquipDataArr(true,true,true,false,true);
            g0 = new GiftAddDefineGroup();
            for each(da000 in daArr0)
            {
               da0 = da000 as VehicleData;
               if(Boolean(da0))
               {
                  skillNameArr0 = da0.skill.saveGroup.delNameArr;
                  if(skillNameArr0.length > 0)
                  {
                     newNameArr0 = [];
                     for each(skillName0 in skillNameArr0)
                     {
                        ng0 = VehicleDataCreator.getSkillBackGift(skillName0);
                        if(Boolean(ng0))
                        {
                           g0.merge(ng0);
                        }
                        else
                        {
                           newNameArr0.push(skillName0);
                        }
                     }
                     if(Boolean(newNameArr0))
                     {
                        da0.skill.saveGroup.delNameArr = newNameArr0;
                     }
                  }
               }
            }
            PD.main.save.vehicleBackB = true;
            if(g0.arr.length > 0)
            {
               GiftAddit.add(g0);
               tip0 = "当前版本已经修复了载具技能学习混乱的Bug。";
               tip0 += "\n系统删除了你之前误学的技能，同时将返还以下消耗品：";
               tip0 += "\n" + g0.getDescription(10);
               Gaming.uiGroup.alertBox.showInfo(tip0);
            }
         }
      }
      
      private static function get PD() : PlayerData
      {
         return Gaming.PG.da;
      }
      
      private static function tra(obj0:*) : void
      {
         trace(obj0);
         SaveTestBox.addText(obj0 as String);
      }
   }
}

