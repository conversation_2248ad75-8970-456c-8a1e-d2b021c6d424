package dataAll.pet
{
   import com.sounto.oldUtils.OldNiuBiCF;
   
   public class PetCount
   {
      
      private static var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public function PetCount()
      {
         super();
      }
      
      public static function get openLevel() : Number
      {
         return CF.getAttribute("openLevel");
      }
      
      public static function set openLevel(v0:Number) : void
      {
         CF.setAttribute("openLevel",v0);
      }
      
      public static function get cLevel() : Number
      {
         return CF.getAttribute("cLevel");
      }
      
      public static function set cLevel(v0:Number) : void
      {
         CF.setAttribute("cLevel",v0);
      }
      
      public static function get skillFirstLevel() : Number
      {
         return CF.getAttribute("skillFirstLevel");
      }
      
      public static function set skillFirstLevel(v0:Number) : void
      {
         CF.setAttribute("skillFirstLevel",v0);
      }
      
      private static function get skillStoneMul() : Number
      {
         return CF.getAttribute("skillStoneMul");
      }
      
      private static function set skillStoneMul(v0:Number) : void
      {
         CF.setAttribute("skillStoneMul",v0);
      }
      
      private static function get skillCoinMul() : Number
      {
         return CF.getAttribute("skillCoinMul");
      }
      
      private static function set skillCoinMul(v0:Number) : void
      {
         CF.setAttribute("skillCoinMul",v0);
      }
      
      public static function init() : void
      {
         openLevel = 30;
         cLevel = 15;
         skillFirstLevel = 25;
         skillStoneMul = 20;
         skillCoinMul = 30;
      }
      
      public static function getSkillStoneMul() : Number
      {
         return skillStoneMul / 100;
      }
      
      public static function getSkillCoinMul() : Number
      {
         return skillCoinMul / 100;
      }
      
      public static function levelCanB() : Boolean
      {
         return Gaming.PG.da.level >= openLevel;
      }
      
      public static function getFirstLevel() : int
      {
         return openLevel - cLevel;
      }
   }
}

