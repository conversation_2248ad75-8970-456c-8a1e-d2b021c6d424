package dataAll._player.state
{
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll._player.state.define.PlayerStateDefine;
   import dataAll.ui.StateIconData;
   import gameAll.body.skill.IO_LifeStateExtraObj;
   
   public class PlayerOneStateSave implements IO_LifeStateExtraObj
   {
      
      public static var pro_arr:Array = null;
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var name:String = "";
      
      private var tempStateData:StateIconData = new StateIconData();
      
      public function PlayerOneStateSave()
      {
         super();
      }
      
      public function get surplusTime() : Number
      {
         return this.CF.getAttribute("surplusTime");
      }
      
      public function set surplusTime(v0:Number) : void
      {
         this.CF.setAttribute("surplusTime",v0);
      }
      
      public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function inDataByDefine(d0:PlayerStateDefine) : void
      {
         this.name = d0.name;
         this.surplusTime = d0.time;
      }
      
      public function getDefine() : PlayerStateDefine
      {
         return Gaming.defineGroup.playerState.getDefine(this.name);
      }
      
      public function isDieB() : Boolean
      {
         return this.surplusTime <= 0;
      }
      
      public function getStateIconData() : StateIconData
      {
         var da0:StateIconData = this.tempStateData;
         var d0:PlayerStateDefine = this.getDefine();
         da0.name = this.name;
         da0.iconUrl = d0.iconUrl;
         da0.time = this.surplusTime;
         return da0;
      }
   }
}

