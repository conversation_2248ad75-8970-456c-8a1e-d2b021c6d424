package com.sounto.utils
{
   import com.sounto.math.Lines;
   
   public class BlockMethod
   {
      
      private static var tempLines:Lines = new Lines();
      
      public static const ZMUL:Number = 0.7;
      
      public function BlockMethod()
      {
         super();
      }
      
      public static function yImgWorldToMotion(imgWorldY0:Number, z0:Number) : Number
      {
         return imgWorldY0 - z0 * ZMUL;
      }
      
      public static function getMotionZ(imgWorldY0:Number, y0:Number) : Number
      {
         return (imgWorldY0 - y0) / ZMUL;
      }
      
      public static function getMotionDeep(height0:Number) : Number
      {
         return height0 / ZMUL;
      }
      
      public static function yMotionToImgWorld(y0:Number, z0:Number) : Number
      {
         return y0 + z0 * ZMUL;
      }
      
      public static function imgToMotionLines(imgX0:Number, imgY0:Number, blockB0:Boolean) : Lines
      {
         var l0:Lines = null;
         var zmul0:Number = BlockMethod.ZMUL;
         l0 = tempLines;
         l0.x = imgX0;
         if(blockB0)
         {
            l0.y = 0;
            l0.z = imgY0 / zmul0;
         }
         else
         {
            l0.y = imgY0;
            l0.z = 0;
         }
         return l0;
      }
   }
}

