package dataAll.skill.define
{
   public class SkillEffect
   {
      
      public static const no:String = "no";
      
      public static const rebirth:String = "rebirth";
      
      public static const changeHurt:String = "changeHurt";
      
      public static const summonedUnits:String = "summonedUnits";
      
      public static const addTempDevice:String = "addTempDevice";
      
      public static const armsSenArr:Array = ["armsSensitive","otherSensitive","redArmsSensitive"];
      
      public function SkillEffect()
      {
         super();
      }
   }
}

