package gameAll.arms
{
   import com.common.data.Line2;
   import com.sounto.image.BmpEffectData;
   import com.sounto.math.Maths;
   import com.sounto.utils.ArrayMethod;
   import dataAll._app.task.TaskData;
   import dataAll.arms.ArmsChargerData;
   import dataAll.arms.ArmsData;
   import dataAll.arms.creator.ArmsPiano;
   import dataAll.arms.creator.GunImage;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsName;
   import dataAll.arms.define.ArmsType;
   import dataAll.body.attack.HurtData;
   import dataAll.bullet.BulletDefine;
   import dataAll.bullet.position.BulletGatlinCount;
   import dataAll.skill.define.SkillDefine;
   import dataAll.skill.define.SkillEvent;
   import flash.geom.Point;
   import gameAll.body.data.NormalBodyStateData;
   import gameAll.body.skill.SkillData;
   import gameAll.bullet.BulletBody;
   import gameAll.bullet.BulletLauncher;
   import gameAll.bullet.IO_BulletLauncher;
   import gameAll.hero.HeroBody;
   import gameAll.hero.data.HeroData;
   import gameAll.hero.image.HeroShootImage;
   
   public class ArmsLauncher implements IO_BulletLauncher
   {
      
      public static var attackGapMul_w:Number = 1;
      
      public var enabled:Boolean = true;
      
      public var BB:HeroBody;
      
      protected var heroImg:HeroShootImage;
      
      public var heroData:HeroData;
      
      public var nowArmsData:ArmsData;
      
      private var nowArmsSkillArr:Array = null;
      
      public var nowChargerData:ArmsChargerData;
      
      protected var shootSkillObj:Object = null;
      
      public var state:String = "stoping";
      
      private var now_t:Number = 0;
      
      private var true_t:Number = 0;
      
      private var timeGap:Number = 0;
      
      private var shootNum:int = 0;
      
      private var attackNum:Number = 0;
      
      private var allShootNum:int = 0;
      
      private var loopB:Boolean = false;
      
      private var before_t:Number = 100;
      
      private var twoFireAttackGap:Number = -1;
      
      private var tempAttackDelay:Number = 0;
      
      private var mouseLoopB:Boolean = false;
      
      private var loopObj:Object = {};
      
      private var loop_t:Number = 0;
      
      private var tempLoopSkillData:SkillData = null;
      
      private var heroShootEventB:Boolean = true;
      
      private var tempFireImg:BmpEffectData = null;
      
      private var tempBulletArr:Array = null;
      
      private var tempBulletPoint:Point = null;
      
      public var out_ShootPointTween:Number = 0;
      
      public var noReduceB:Boolean = true;
      
      public var noReduceCapacityB:Boolean = false;
      
      protected var attackGapMul:Number = 1;
      
      protected var reloadGapMul:Number = 1;
      
      public var speedNoReduceB:Boolean = false;
      
      private var d_speedNoReduceB:Boolean = false;
      
      public var noShootB:Boolean = false;
      
      public var noShoot_s:Boolean = false;
      
      private var memoryCanB:Boolean = true;
      
      public var armsTypeLimit:Array = null;
      
      private var swapNameLimit:Array = null;
      
      public var focoImgMul:Number = -1;
      
      public var bulletNumMul:Number = 1;
      
      public var shootAngleMul:Number = 1;
      
      public var bulletNumWeekB:Boolean = false;
      
      public var shootRange_s:Number = -1;
      
      private var resonateGodIndex:int = 0;
      
      private var nowCanBNB:int = -1;
      
      public function ArmsLauncher(_BB:HeroBody)
      {
         super();
         this.BB = _BB;
         this.heroData = this.BB.dat;
         this.heroImg = this.BB.img;
      }
      
      public static function canShootB(mx:int, my:int, x0:int, y0:int, armsD:BulletDefine, clong0:int = -1, setPenetrationGap0:int = -1, precisionB0:Boolean = true) : Boolean
      {
         var p0:Point = null;
         var range1:Number = NaN;
         if(clong0 == -1)
         {
            clong0 = Math.sqrt((x0 - mx) * (x0 - mx) + (y0 - my) * (y0 - my));
         }
         if(clong0 < armsD.getAIShootRange(precisionB0))
         {
            if(setPenetrationGap0 == -1)
            {
               setPenetrationGap0 = armsD.penetrationGap;
            }
            p0 = Gaming.sceneGroup.map.hitLine(x0,y0,mx,my,setPenetrationGap0);
            if(!(p0 is Point))
            {
               return true;
            }
            range1 = (x0 - p0.x) * (x0 - p0.x) + (y0 - p0.y) * (y0 - p0.y);
            if(clong0 * clong0 < range1)
            {
               return true;
            }
         }
         return false;
      }
      
      public function initState() : void
      {
         this.d_speedNoReduceB = this.speedNoReduceB;
         this.speedNoReduceB = false;
         this.noReduceCapacityB = false;
         this.attackGapMul = 1;
         this.reloadGapMul = 1;
         this.focoImgMul = -1;
         this.bulletNumMul = 1;
         this.shootAngleMul = 1;
         this.bulletNumWeekB = false;
         this.noShoot_s = false;
         this.shootRange_s = -1;
         if(Boolean(this.nowArmsData))
         {
            this.nowArmsData.initState();
         }
      }
      
      public function getShootEnabled() : Boolean
      {
         var labelType0:String = this.BB.img.nowLabelType;
         if(labelType0 != "Attack" && labelType0 != "die" && labelType0 != "fill" && this.state != BulletLauncher.RELOADING)
         {
            return true;
         }
         return false;
      }
      
      public function getState() : String
      {
         return this.state;
      }
      
      public function getNowBulletDefine() : BulletDefine
      {
         return this.nowArmsData;
      }
      
      public function getNowArmsName() : String
      {
         if(Boolean(this.nowArmsData))
         {
            return this.nowArmsData.name;
         }
         return "";
      }
      
      public function setShootSpeedMul(mul0:Number) : void
      {
         if(mul0 < 1)
         {
         }
         if(this.d_speedNoReduceB && mul0 < 1)
         {
            return;
         }
         this.attackGapMul /= mul0;
      }
      
      public function setReloadSpeedMul(mul0:Number) : void
      {
         this.reloadGapMul /= mul0;
      }
      
      public function getAttackGapMul() : Number
      {
         return this.attackGapMul;
      }
      
      public function setArmsBySite(site0:int) : ArmsData
      {
         var data0:ArmsData = this.heroData.armsData.getDataBySite(site0) as ArmsData;
         if(!data0)
         {
            INIT.showError("不存在武器数据site：" + site0);
         }
         return this.setArms(data0);
      }
      
      public function setArmsByName(name0:String) : ArmsData
      {
         var da0:ArmsData = this.heroData.armsData.getDataByBaseName(name0);
         if(Boolean(da0))
         {
            this.setArms(da0);
         }
         return da0;
      }
      
      public function setArms(data0:ArmsData) : ArmsData
      {
         var d0:ArmsDefine = null;
         var gun1:GunImage = null;
         var chargerData0:ArmsChargerData = null;
         var skillArr0:Array = null;
         var gun2:GunImage = null;
         if(Boolean(data0) && data0 != this.nowArmsData)
         {
            d0 = data0;
            this.BB.img.setGunDefine(data0);
            gun1 = Gaming.gunImageManager.getImageCopy(data0.save.getImgLabel());
            if(d0.gunNum == 2)
            {
               gun2 = gun1.clone();
            }
            this.BB.img.setGun(gun1,gun2);
            this.twoFireAttackGap = -1;
            this.before_t = 0;
            this.timeGap = 0;
            this.attackNum = 0;
            this.allShootNum = 0;
            if(Boolean(this.nowArmsData))
            {
               this.nowArmsData.now_t = this.now_t;
            }
            this.now_t = 0;
            this.true_t = 0;
            this.loopObj = {};
            this.loop_t = 0;
            this.BB.img.tryPlayLaunch();
            this.clearTempFireImg();
            this.clearTempBullet();
            this.state = BulletLauncher.OVERING;
            chargerData0 = this.heroData.chargerData.getData(d0.armsType);
            if(!chargerData0)
            {
               INIT.showError("不存在武器类型指定的携弹类数据：" + d0.armsType);
            }
            this.nowArmsData = data0;
            this.nowChargerData = chargerData0;
            this.nowArmsSkillArr = ArrayMethod.clearRepeat(data0.getAllSkillArr());
            this.heroData.armsData.nowData = data0;
            this.heroData.chargerData.nowData = chargerData0;
            this.heroShootEventB = !data0.lineD.editB;
            this.nowCanBNB = -1;
            this.resonateGodIndex = 0;
            skillArr0 = this.nowArmsSkillArr;
            if(data0.haveColorPartsB() == false)
            {
               if(skillArr0.indexOf("resonateGodSuper") >= 0)
               {
                  this.resonateGodIndex = 2;
               }
               else if(skillArr0.indexOf("resonateGod") >= 0)
               {
                  this.resonateGodIndex = 1;
               }
            }
         }
         this.fleshMemory();
         return data0;
      }
      
      private function fleshMemory() : void
      {
         var da0:ArmsData = null;
         var tda0:TaskData = null;
         var lv0:int = 0;
         var armsLv0:int = 0;
         this.memoryCanB = true;
         if(this.heroData.isWeSaveDataPlayerB())
         {
            da0 = this.nowArmsData;
            if(Boolean(da0))
            {
               if(Gaming.LG.isMemoryTaskB())
               {
                  tda0 = Gaming.LG.getTaskData();
                  lv0 = tda0.getLv();
                  armsLv0 = da0.save.getTrueLevel();
                  if(armsLv0 > lv0)
                  {
                     this.memoryCanB = false;
                  }
               }
            }
         }
      }
      
      public function nextArms() : void
      {
         var data0:ArmsData = this.heroData.armsData.getNextSiteArmsData(this.firstChoiceB(),this.armsTypeLimit,this.swapNameLimit);
         if(!data0)
         {
            INIT.showError("获得下一个武器失败");
         }
         if(Boolean(data0))
         {
            if(Boolean(this.nowArmsData))
            {
               if(this.heroData.armsData.dataArr.length >= 2)
               {
                  this.setArms(data0);
               }
               else if(!this.heroData.armsData.nowArmsDataInArrB())
               {
                  this.setArms(data0);
               }
            }
            else
            {
               this.setArms(data0);
            }
         }
      }
      
      public function randomArms() : void
      {
         var arr0:Array = this.heroData.armsData.dataArr;
         this.setArms(arr0[int(Math.random() * arr0.length)]);
      }
      
      public function onlySwapArmsName(name0:String) : void
      {
         var limitArr0:Array = this.swapNameLimit;
         if(Boolean(limitArr0))
         {
            if(limitArr0.length == 1)
            {
               if(limitArr0[0] == name0)
               {
                  return;
               }
            }
         }
         limitArr0 = [name0];
         this.setArmsByName(name0);
      }
      
      public function setSwapNameLimit(arr0:Array) : void
      {
         this.swapNameLimit = arr0;
      }
      
      public function getSwapNameLimit() : Array
      {
         return this.swapNameLimit;
      }
      
      private function firstChoiceB() : Boolean
      {
         var firstB:Boolean = false;
         if(Boolean(this.heroData.normalPD))
         {
            if(this.heroData.isMainPlayerB())
            {
               if(Gaming.PG.save.setting.firstChooseB)
               {
                  firstB = true;
               }
            }
            else
            {
               firstB = true;
            }
         }
         return firstB;
      }
      
      public function getNoShootB() : Boolean
      {
         var state0:NormalBodyStateData = this.heroData.stateD;
         if(this.noShootB)
         {
            return true;
         }
         if(this.noShoot_s)
         {
            return true;
         }
         if(this.memoryCanB == false)
         {
            return true;
         }
         if(this.heroData.stateD.lostCtrlB)
         {
            return true;
         }
         if(this.heroData.existB == false)
         {
            return true;
         }
         if(!this.armsTypePan())
         {
            return true;
         }
         return false;
      }
      
      private function armsTypePan() : Boolean
      {
         var type0:String = null;
         if(Boolean(this.nowArmsData))
         {
            type0 = this.nowArmsData.armsType;
            if(type0 == ArmsType.props)
            {
               return false;
            }
            if(Boolean(this.armsTypeLimit))
            {
               if(this.armsTypeLimit.indexOf(type0) == -1)
               {
                  return false;
               }
            }
         }
         return true;
      }
      
      public function setShootPoint(x0:int, y0:int, tweenMul0:Number = 0, fleshImgB0:Boolean = false) : void
      {
         if(this.out_ShootPointTween > 0)
         {
            tweenMul0 = this.out_ShootPointTween;
         }
         if(tweenMul0 > 0)
         {
            this.heroImg.setTweenShootPoint(x0,y0,tweenMul0);
         }
         else
         {
            this.heroImg.setShootPoint(x0,y0);
         }
         if(fleshImgB0)
         {
            this.heroImg.inGunXY(false);
         }
      }
      
      private function focoPan() : void
      {
         var mul0:Number = -1;
         if(this.focoImgMul >= 0)
         {
            mul0 = this.focoImgMul;
         }
         else if(this.nowArmsData.crossbowD.focoB)
         {
            if(this.state != BulletLauncher.DELAYING)
            {
               mul0 = 0;
            }
            mul0 = this.getNowFocoMul();
         }
         else if(this.nowArmsData.recordD.moveGap > 0)
         {
            mul0 = this.nowArmsData.moveGap / this.nowArmsData.recordD.moveGap;
         }
         if(mul0 >= 0)
         {
            this.heroImg.setFocoMul(mul0);
         }
      }
      
      public function getLoopB() : Boolean
      {
         return this.loopB;
      }
      
      public function startLoop() : Boolean
      {
         if(this.getNoShootB())
         {
            return true;
         }
         var labelType0:String = this.BB.img.nowLabelType;
         if(labelType0 != "Attack")
         {
            this.loopB = true;
         }
         if(this.getShootEnabled())
         {
            if(this.state == BulletLauncher.STOPING)
            {
               if(this.haveCapacityB())
               {
                  this.state = BulletLauncher.START;
                  this.now_t = 0;
                  this.true_t = 0;
                  this.shootNum = 0;
               }
               else if(this.haveChargerB())
               {
                  this.state = BulletLauncher.STOP;
               }
            }
            return this.haveCapacityB() || this.haveChargerB();
         }
         return true;
      }
      
      public function stopLoop() : void
      {
         this.loopB = false;
      }
      
      public function stopBreak() : void
      {
         this.loopB = false;
         this.now_t = 0;
         this.true_t = 0;
         this.shootNum = 0;
         if(this.state == BulletLauncher.STOPING || this.state == BulletLauncher.STOP)
         {
            this.state = BulletLauncher.STOPING;
         }
         else
         {
            this.state = BulletLauncher.OVERING;
         }
         this.before_t = 0;
         this.mouseLoopB = false;
      }
      
      public function toStoping() : void
      {
         this.state = BulletLauncher.STOPING;
      }
      
      public function changeCharger() : void
      {
         if(this.state != BulletLauncher.RELOADING)
         {
            if(this.nowArmsData.nowCapacityNum < this.nowArmsData.capacity && this.haveChargerB())
            {
               this.state = BulletLauncher.RELOADING;
               this.swapEvent();
            }
         }
      }
      
      public function haveChargerB() : Boolean
      {
         return this.nowChargerData.now > 0 || this.noReduceB || this.noReduceCapacityB;
      }
      
      public function haveCapacityB() : Boolean
      {
         return this.nowArmsData.nowCapacityNum > 0 || this.noReduceCapacityB || this.nowArmsData.capacity <= -1000;
      }
      
      public function isEverChargerB() : Boolean
      {
         return this.nowArmsData.capacity == -1000;
      }
      
      public function aiWantCaissonB(changeArmsB0:Boolean) : Boolean
      {
         var armsArr0:Array = null;
         if(this.haveCapacityB() == false && this.haveChargerB() == false && this.isEverChargerB() == false)
         {
            if(changeArmsB0)
            {
               armsArr0 = this.heroData.armsData.getCanUseArr(this.firstChoiceB(),this.armsTypeLimit,this.swapNameLimit,this.heroData.chargerData);
               if(armsArr0.length > 0)
               {
                  return false;
               }
               return true;
            }
            if(Boolean(this.nowArmsData))
            {
               if(this.nowArmsData.getNowCapacityAndCharger(this.heroData.chargerData) == 0)
               {
                  return true;
               }
               return false;
            }
            return false;
         }
         return false;
      }
      
      public function aiSwapArmsPan() : Boolean
      {
         var armsArr0:Array = null;
         if(this.state == BulletLauncher.RELOADING)
         {
            return false;
         }
         if(!this.haveCapacityB())
         {
            return true;
         }
         if(Boolean(this.armsTypeLimit) && Boolean(this.nowArmsData))
         {
            if(this.armsTypeLimit.indexOf(this.nowArmsData.armsType) == -1)
            {
               armsArr0 = this.heroData.armsData.getSiteDataArrMust(this.firstChoiceB(),this.armsTypeLimit,this.swapNameLimit);
               return true;
            }
         }
         return false;
      }
      
      public function mouseDown() : Boolean
      {
         this.mouseLoopB = true;
         return this.startLoop();
      }
      
      public function mouseUp() : void
      {
         var min0:Number = NaN;
         var d0:ArmsDefine = this.nowArmsData;
         var delay0:Number = this.getAttackDelay();
         if(d0.crossbowD.focoB)
         {
            if(this.state == BulletLauncher.DELAYING)
            {
               min0 = delay0 * d0.crossbowD.minDelayMul;
               if(this.now_t < min0)
               {
                  if(d0.crossbowD.delayBreakB())
                  {
                     this.stopBreak();
                     this.state = BulletLauncher.STOPING;
                     return;
                  }
                  this.now_t = delay0 - (min0 - this.now_t);
               }
               else
               {
                  this.now_t = delay0;
               }
            }
         }
         this.mouseLoopB = false;
         this.stopLoop();
      }
      
      public function startEvent() : void
      {
      }
      
      public function swapEvent() : void
      {
         this.BB.img.playReloadBullet(this.getReloadGap());
         this.twoFireAttackGap = -1;
      }
      
      public function shootOverEvent() : void
      {
         var d:ArmsDefine = null;
         this.state = BulletLauncher.OVERING;
         var twoShootPro0:Number = this.nowArmsData.twoShootPro;
         if(twoShootPro0 > 0.25)
         {
            twoShootPro0 = 0.25;
         }
         if(this.twoFireAttackGap == -1 && twoShootPro0 > 0)
         {
            if(Math.random() < twoShootPro0)
            {
               d = this.nowArmsData;
               if(d.attackGap < 0.2)
               {
                  this.twoFireAttackGap = 0;
               }
               else
               {
                  this.twoFireAttackGap = 0.1;
               }
            }
         }
         else
         {
            this.twoFireAttackGap = -1;
         }
      }
      
      public function shootEvent() : void
      {
         var firebmp0:BmpEffectData = null;
         var piano_s0:String = null;
         var extraMul0:Number = NaN;
         var bulletNumMul0:Number = NaN;
         var followPointB0:Boolean = false;
         var x0:int = 0;
         var y0:int = 0;
         var base_ra0:Number = NaN;
         var shake_ra0:Number = NaN;
         var magnetic_mul0:Number = NaN;
         var magnetic_ra0:Number = NaN;
         var bulletRa0:Number = NaN;
         var ra0:Number = NaN;
         var h0:HurtData = null;
         var bu0:BulletBody = null;
         var gap0:Number = NaN;
         var p2:Array = null;
         var pra2:Number = NaN;
         var pl2:Number = NaN;
         if(this.getNoShootB())
         {
            return;
         }
         Gaming.TG.skill.eventTrigger(SkillEvent.beforeAttack,this.BB);
         this.triggerNowArmsSkill(SkillEvent.beforeAttack);
         var d:ArmsDefine = this.nowArmsData;
         var focoHurtMul0:Number = 1;
         var focoMul0:Number = 1;
         var vMul0:Number = 1;
         if(d.crossbowD.focoB)
         {
            focoMul0 = this.getNowFocoMul();
            focoHurtMul0 = focoMul0;
            if(d.crossbowD.vAtt > 0)
            {
               vMul0 = focoMul0 * d.crossbowD.vAtt;
            }
            else
            {
               vMul0 = focoMul0 * 0.3 + 0.7;
            }
         }
         if(!this.noReduceCapacityB && this.twoFireAttackGap == -1)
         {
            --this.nowArmsData.nowCapacityNum;
         }
         var al0:String = d.armsImgLabel;
         var sl0:String = this.nowArmsData.getShootSoundUrl();
         var img0:HeroShootImage = this.BB.img;
         var l0:Line2 = img0.getShootLines();
         img0.playShoot(focoMul0);
         BulletGatlinCount.count(d,l0,this.allShootNum);
         if(d.fireImg.url != "")
         {
            firebmp0 = Gaming.EG.addBmpEffectFull(d.fireImg.getRandomUrl(),l0.x,l0.y,l0.ra,d.fireImg.con);
            if(Boolean(firebmp0))
            {
               if(d.fireImg.cacheB)
               {
                  firebmp0.setDieType(d.fireImg.imgDieType);
                  this.setTempFireImg(firebmp0);
               }
            }
         }
         else if(d.fireImgType == "")
         {
            Gaming.EG.addBmpEffect("gunFire","f" + int(1 + Math.random() * 2),l0.x,l0.y,l0.ra,"add");
         }
         if(Boolean(this.tempBulletPoint))
         {
            l0.x = this.tempBulletPoint.x;
            l0.y = this.tempBulletPoint.y;
         }
         var pianoArr0:Array = this.nowArmsData.pianoArr;
         var pianoIndex0:int = this.nowArmsData.pianoIndex;
         if(Boolean(pianoArr0))
         {
            piano_s0 = "";
            if(pianoArr0.length > 0)
            {
               piano_s0 = ArrayMethod.getElementLimit(pianoArr0,pianoIndex0) as String;
               if(pianoIndex0 >= pianoArr0.length - 1)
               {
                  this.nowArmsData.pianoIndex = 0;
               }
               else
               {
                  ++this.nowArmsData.pianoIndex;
               }
            }
            else
            {
               piano_s0 = ArrayMethod.getRandomOne(ArmsPiano.STR_ARR);
            }
            this.BB.soundCtrl.playSoundFull(ArmsPiano.getSoundUrl(piano_s0),1);
         }
         else
         {
            this.BB.soundCtrl.playSoundFull(sl0,ArmsType.getVolume(this.heroData.haveShakeAndSoundB(),this.nowArmsData.armsType,this.nowArmsData));
         }
         var loopNum0:int = d.bulletNum;
         var shootAngle0:Number = d.shootAngle;
         var canBulletNumB0:Boolean = true;
         if(this.bulletNumWeekB)
         {
            canBulletNumB0 = this.bulletNumPan();
         }
         if(canBulletNumB0)
         {
            extraMul0 = 1;
            if(this.bulletNumWeekB)
            {
               if(loopNum0 == 1 && d.attackGap >= 0.3)
               {
                  extraMul0 = 2;
               }
            }
            bulletNumMul0 = this.bulletNumMul;
            if(bulletNumMul0 > 5)
            {
               bulletNumMul0 = 5;
            }
            loopNum0 = Math.ceil(loopNum0 * bulletNumMul0 * extraMul0);
            shootAngle0 *= this.shootAngleMul * extraMul0;
            if(this.shootAngleMul > 1)
            {
               if(shootAngle0 > 180)
               {
                  shootAngle0 = 180;
               }
               if(loopNum0 > 1)
               {
                  if(shootAngle0 < 3 * loopNum0)
                  {
                     shootAngle0 = 1 * loopNum0;
                  }
               }
            }
         }
         if(this.nowArmsData.shootAngleCtrlB && shootAngle0 > 0 && loopNum0 > 1)
         {
            shootAngle0 *= ArmsType.getShootAngleMul(l0.len);
         }
         var bulletArr0:Array = null;
         if(Gaming.targetInput.timeStopB == false)
         {
            followPointB0 = d.bulletFollowShootPointB();
            if(followPointB0)
            {
               bulletArr0 = [];
            }
         }
         for(var i:int = 0; i < loopNum0; i++)
         {
            x0 = l0.x;
            y0 = l0.y;
            if(this.tempBulletPoint && this.shootAngleMul > 1 && bulletNumMul0 > 1 && d.name == "consVirgo")
            {
               gap0 = 10 + 8 * loopNum0;
               if(gap0 > 70)
               {
                  gap0 = 70;
               }
               x0 = l0.x + (1 - 2 * Math.random()) * gap0;
               y0 = l0.y + (1 - 2 * Math.random()) * gap0;
            }
            base_ra0 = 0;
            if(loopNum0 > 1)
            {
               base_ra0 = shootAngle0 / 180 * Math.PI * (-1 + i * 2 / (loopNum0 - 1));
            }
            shake_ra0 = d.shakeAngle / 180 * Math.PI * (2 * Math.random() - 1);
            if(d.noShakeTime > 0)
            {
               if(this.before_t >= d.noShakeTime)
               {
                  shake_ra0 *= 0.2;
               }
            }
            magnetic_mul0 = this.heroData.stateD.getMagneticCtrlMul();
            magnetic_ra0 = 0;
            if(magnetic_mul0 > 0 && d.noMagneticB == false)
            {
               magnetic_ra0 = magnetic_mul0 * 60 / 180 * Math.PI;
            }
            if(d.getPathType() == ArmsType.POINT)
            {
               p2 = d.getPathPoint2(i);
               if(Boolean(p2))
               {
                  pra2 = Math.atan2(p2[1],p2[0]) + l0.ra;
                  pl2 = Maths.Long(p2[0],p2[1]);
                  x0 += pl2 * Math.cos(pra2);
                  y0 += pl2 * Math.sin(pra2);
                  base_ra0 = 0;
               }
            }
            bulletRa0 = d.bulletAngle > -100 ? d.bulletAngle / 180 * Math.PI : 0;
            ra0 = l0.ra + base_ra0 + shake_ra0 + magnetic_ra0 + bulletRa0;
            h0 = this.nowArmsData.getHurtData();
            if(this.resonateGodIndex > 0)
            {
               this.dealResonateGod(this.BB,this.nowArmsData,h0,this.resonateGodIndex);
            }
            if(this.heroData.stateD.armsDpsMul != 1)
            {
               h0.hurtRatio *= this.heroData.stateD.armsDpsMul;
            }
            h0.hurtRatio *= focoHurtMul0;
            h0.focoMul = this.getNowFocoMul();
            bu0 = Gaming.bulletGroup.addBullet(d,x0,y0,ra0,this.BB,this.heroData.camp,h0,false,vMul0,i);
            if(this.shootRange_s > 0)
            {
               bu0.limitBulletLen(this.shootRange_s);
            }
            if(Boolean(pianoArr0))
            {
               bu0.pianoIndex = pianoIndex0;
            }
            if(Boolean(bulletArr0))
            {
               bu0.mot.setRaDvalue(base_ra0 + shake_ra0 + bulletRa0);
               bulletArr0.push(bu0);
            }
            this.dealBullet(d,bu0);
            this.heroData.countD.addBulletNum(1);
            Gaming.TG.hurt.dealArmsSkill(this.BB,this.BB,h0,"heroOneShoot");
         }
         this.before_t = 0;
         if(Boolean(bulletArr0))
         {
            this.addTempBulletArr(bulletArr0);
         }
         if(this.BB.getData().haveShakeAndSoundB())
         {
            Gaming.sceneGroup.targetShake.startBoom(1,0.2,ra0 + Math.PI,d.screenShakeValue * this.heroData.getShootShakeValue() * focoMul0);
         }
         if(this.heroShootEventB)
         {
            Gaming.TG.skill.eventTrigger(SkillEvent.heroShoot,this.BB);
         }
         this.triggerNowArmsSkill(SkillEvent.heroShoot);
         this.tempBulletPoint = null;
      }
      
      private function bulletNumPan() : Boolean
      {
         var canB0:Boolean = false;
         var name0:String = this.nowArmsData.def.name;
         if(this.nowCanBNB == -1)
         {
            canB0 = Gaming.PG.da.main.save.weekArmsArr.indexOf(name0) == -1;
            this.nowCanBNB = canB0 ? 0 : 1;
         }
         if(this.nowCanBNB == 0)
         {
            Gaming.LG.nowLevel.dat.temp.addSuperBulletArms(name0);
            return true;
         }
         return false;
      }
      
      private function dealResonateGod(hero0:HeroBody, now0:ArmsData, h0:HurtData, index0:int) : void
      {
         var daArr0:Array = null;
         var mustSameB0:Boolean = false;
         var ranArr0:Array = null;
         var da0:ArmsData = null;
         var m0:ArmsData = null;
         var newGodArr0:Array = null;
         var sarr0:Array = h0.skillArr;
         if(Boolean(sarr0))
         {
            daArr0 = hero0.dat.armsData.dataArr;
            if(daArr0.length > 1)
            {
               mustSameB0 = true;
               if(index0 == 2)
               {
                  mustSameB0 = ArmsType.highSpeedArr.indexOf(now0.armsType) >= 0;
               }
               ranArr0 = [];
               for each(da0 in daArr0)
               {
                  if(now0 != da0)
                  {
                     if(da0.godSkillArr.length > 0)
                     {
                        if(mustSameB0 == false || da0.armsType == now0.armsType)
                        {
                           ranArr0.push(da0);
                        }
                     }
                  }
               }
               if(ranArr0.length > 0)
               {
                  m0 = ArrayMethod.getRandomOne(ranArr0);
                  if(m0.godSkillArr.length > 0)
                  {
                     newGodArr0 = ArrayMethod.getRandomArray(m0.godSkillArr,2);
                     h0.skillArr = h0.skillArr.concat();
                     ArrayMethod.addNoRepeatArrInArr(h0.skillArr,newGodArr0);
                  }
               }
            }
         }
      }
      
      private function dealBullet(d:ArmsDefine, bu0:BulletBody) : void
      {
         var hitB0:Boolean = false;
         var name0:String = d.name;
         if(name0 == ArmsName.yearDog)
         {
            if(d.bounceD.hurtNumAdd > 0)
            {
               hitB0 = Gaming.sceneGroup.map.hitPoint(bu0.mot.x,bu0.mot.y);
               if(hitB0)
               {
                  bu0.toDie();
               }
            }
         }
      }
      
      public function setTempBulletPoint(p0:Point) : void
      {
         this.tempBulletPoint = p0;
      }
      
      public function getAttackGap() : Number
      {
         var d:ArmsDefine = this.nowArmsData;
         var attackGap0:Number = d.attackGap;
         var maxAttackGap0:Number = ArmsType.getMaxAttackGap(d.armsType,d);
         if(attackGap0 < maxAttackGap0)
         {
            attackGap0 = maxAttackGap0;
         }
         attackGap0 *= this.attackGapMul;
         attackGap0 *= attackGapMul_w;
         if(this.twoFireAttackGap >= 0)
         {
            attackGap0 = this.twoFireAttackGap;
         }
         return attackGap0;
      }
      
      private function getReloadGap() : Number
      {
         var d:ArmsDefine = this.nowArmsData;
         var v0:Number = d.getTrueReloadGap();
         return v0 * this.reloadGapMul;
      }
      
      private function getAttackDelay(newGap0:Number = -1) : Number
      {
         if(newGap0 == -1)
         {
            newGap0 = this.getAttackGap();
         }
         var d:ArmsDefine = this.nowArmsData;
         var attackGap0:Number = d.attackGap;
         var delay0:Number = d.attackDelay;
         return BulletLauncher.getDelay(delay0,attackGap0,newGap0);
      }
      
      private function getNowFocoMul() : Number
      {
         var mul0:Number = this.true_t / this.tempAttackDelay;
         if(mul0 > 1)
         {
            mul0 = 1;
         }
         return mul0;
      }
      
      public function attackPan() : void
      {
         var shootb2:Boolean = false;
         var d:ArmsDefine = this.nowArmsData;
         var attackGap0:Number = this.getAttackGap();
         var attackDelay0:Number = this.getAttackDelay(attackGap0);
         this.tempAttackDelay = attackDelay0;
         if(d.noShakeTime > 0)
         {
            if(this.before_t < 0.5)
            {
               this.before_t += 1 / 30;
            }
         }
         if(this.state != BulletLauncher.STOPING)
         {
            this.now_t += 1 / 30;
            this.true_t += 1 / 30;
            if(this.state == BulletLauncher.START)
            {
               if(this.armsTypePan())
               {
                  if(attackDelay0 == 0)
                  {
                     this.state = BulletLauncher.SHOOT;
                  }
                  else
                  {
                     this.state = BulletLauncher.DELAYING;
                  }
                  ++this.attackNum;
                  this.startEvent();
               }
               else
               {
                  this.stopBreak();
               }
            }
            if(this.state == BulletLauncher.DELAYING)
            {
               if(this.now_t >= attackDelay0)
               {
                  this.state = BulletLauncher.SHOOT;
               }
            }
            if(this.state == BulletLauncher.SHOOT)
            {
               ++this.shootNum;
               ++this.allShootNum;
               this.shootEvent();
               shootb2 = true;
               if(d.shootNum > 1)
               {
                  if(this.shootNum >= d.shootNum)
                  {
                     this.state = BulletLauncher.OVERING;
                     this.shootOverEvent();
                  }
                  else if(d.shootGap == 0)
                  {
                     this.state = BulletLauncher.SHOOT;
                  }
                  else
                  {
                     this.state = BulletLauncher.SHOOT_DELAYING;
                  }
               }
               else
               {
                  this.state = BulletLauncher.OVERING;
                  this.shootOverEvent();
               }
            }
            if(this.state == BulletLauncher.SHOOT_DELAYING)
            {
               if(this.now_t >= attackDelay0 + this.shootNum * d.shootGap)
               {
                  this.state = BulletLauncher.SHOOT;
               }
            }
            if(this.state == BulletLauncher.OVERING)
            {
               if(!this.haveCapacityB())
               {
                  if(this.now_t >= attackGap0 / 2)
                  {
                     this.state = BulletLauncher.STOP;
                  }
               }
               else if(this.now_t >= attackGap0 - this.timeGap)
               {
                  this.timeGap = this.now_t - (attackGap0 - this.timeGap);
                  this.state = BulletLauncher.STOP;
               }
            }
            if(this.state == BulletLauncher.RELOADING)
            {
               if(this.now_t >= this.getReloadGap())
               {
                  this.nowArmsData.swapCharger(this.nowChargerData,this.noReduceB);
                  this.state = BulletLauncher.STOP;
               }
            }
            if(this.state == BulletLauncher.STOP)
            {
               this.now_t = 0;
               this.true_t = 0;
               this.shootNum = 0;
               if(!this.haveCapacityB())
               {
                  if(!this.haveChargerB())
                  {
                     this.state = BulletLauncher.STOPING;
                     if(this.heroData.playerCtrlB)
                     {
                        Gaming.soundGroup.playSound("sound","noCharger");
                     }
                  }
                  else
                  {
                     this.state = BulletLauncher.RELOADING;
                     this.swapEvent();
                  }
               }
               else if(this.loopB)
               {
                  this.state = BulletLauncher.START;
               }
               else
               {
                  this.state = BulletLauncher.STOPING;
               }
            }
         }
         if(this.mouseLoopB)
         {
            this.loop_t += 1 / 30;
            this.loopClickPan();
         }
         else
         {
            this.loop_t = 0;
            this.loopObj = null;
         }
      }
      
      public function triggerNowArmsSkill(event0:String) : Boolean
      {
         var name0:String = null;
         var d0:SkillDefine = null;
         var bb0:Boolean = false;
         var skillArr0:Array = this.nowArmsSkillArr;
         for each(name0 in skillArr0)
         {
            d0 = Gaming.defineGroup.skill.getDefine(name0);
            if(d0 is SkillDefine)
            {
               if(d0.condition == event0)
               {
                  Gaming.TG.skill.bodyDoSkillAndCheckCondition(d0,this.BB,this.getTempLoopSkillData(d0));
                  bb0 = true;
               }
            }
         }
         return bb0;
      }
      
      private function loopClickPan() : void
      {
         var name0:String = null;
         var d0:SkillDefine = null;
         var num0:int = 0;
         var skillArr0:Array = this.nowArmsSkillArr;
         for each(name0 in skillArr0)
         {
            d0 = Gaming.defineGroup.skill.getDefine(name0);
            if(d0 is SkillDefine)
            {
               if(d0.condition == SkillEvent.loopClick)
               {
                  num0 = this.getLoopObjNum(name0) + 1;
                  if(this.loop_t >= d0.duration * num0)
                  {
                     this.addLoopObjNum(name0,1);
                     if(this.getNoShootB() == false)
                     {
                        Gaming.TG.skill.bodyDoSkillAndCheckCondition(d0,this.BB,this.getTempLoopSkillData(d0));
                     }
                  }
               }
            }
         }
      }
      
      protected function doSkillObj(obj0:Object) : void
      {
         var d0:SkillDefine = null;
         for each(d0 in obj0)
         {
            Gaming.TG.skill.bodyDoSkillAndCheckCondition(d0,this.BB,this.getTempLoopSkillData(d0));
         }
      }
      
      private function getTempLoopSkillData(d0:SkillDefine) : SkillData
      {
         var sk0:SkillData = this.tempLoopSkillData;
         if(!sk0)
         {
            sk0 = new SkillData();
            this.tempLoopSkillData = sk0;
         }
         sk0.clearSimpleDefine();
         sk0.inSimpleDefine(d0);
         return sk0;
      }
      
      private function getLoopObjNum(name0:String) : int
      {
         if(!this.loopObj)
         {
            this.loopObj = {};
         }
         if(this.loopObj.hasOwnProperty(name0))
         {
            return this.loopObj[name0];
         }
         return 0;
      }
      
      private function addLoopObjNum(name0:String, v0:int) : void
      {
         if(!this.loopObj)
         {
            this.loopObj = {};
         }
         if(!this.loopObj.hasOwnProperty(name0))
         {
            this.loopObj[name0] = 0;
         }
         this.loopObj[name0] += v0;
      }
      
      private function otherPan() : void
      {
         var da0:ArmsData = this.nowArmsData;
         if(da0.recordD.moveGap > 0)
         {
            if(da0.moveGap < da0.recordD.moveGap)
            {
               da0.moveGap += Math.abs(this.BB.mot.vx);
            }
         }
      }
      
      private function setTempFireImg(bmp0:BmpEffectData) : void
      {
         this.clearTempFireImg();
         this.tempFireImg = bmp0;
      }
      
      private function clearTempFireImg() : void
      {
         if(Boolean(this.tempFireImg))
         {
            this.tempFireImg.toDie();
            this.tempFireImg = null;
         }
      }
      
      private function addTempBulletArr(arr0:Array) : void
      {
         if(!this.tempBulletArr)
         {
            this.tempBulletArr = arr0;
         }
         else
         {
            this.tempBulletArr = arr0.concat(this.tempBulletArr);
         }
      }
      
      private function clearTempBullet() : void
      {
         this.tempBulletArr = null;
      }
      
      public function toDie() : void
      {
         this.stopLoop();
         this.enabled = false;
         this.clearTempFireImg();
         this.clearTempBullet();
      }
      
      public function FTimer() : void
      {
         if(this.enabled)
         {
            if(Boolean(this.nowArmsData))
            {
               this.otherPan();
               this.attackPan();
               this.focoPan();
            }
         }
      }
      
      public function imageTimer() : void
      {
         var l0:Line2 = null;
         var newArr0:Array = null;
         var bu0:BulletBody = null;
         if(this.enabled)
         {
            if(Boolean(this.nowArmsData))
            {
               if(Boolean(this.tempFireImg))
               {
                  l0 = this.BB.img.getShootLines();
                  this.tempFireImg.x = l0.x;
                  this.tempFireImg.y = l0.y;
                  this.tempFireImg.setRa(l0.ra);
                  if(this.state == BulletLauncher.STOPING)
                  {
                     this.tempFireImg.toDie();
                  }
               }
               if(Boolean(this.tempBulletArr))
               {
                  newArr0 = [];
                  for each(bu0 in this.tempBulletArr)
                  {
                     bu0.outImageTimer();
                     if(bu0.getDie() == 0)
                     {
                        newArr0.push(bu0);
                     }
                     this.tempBulletArr = newArr0;
                  }
               }
            }
         }
      }
   }
}

