package dataAll._app.ask.define
{
   import com.sounto.oldUtils.ComMethod;
   import dataAll.skill.define.HeroSkillDefine;
   import dataAll.skill.define.SkillDefine;
   import dataAll.ui.GatherColor;
   
   public class AskDefine
   {
      
      public var father:String = "";
      
      public var index:int = 0;
      
      public var title:String = "";
      
      public var correctArr:Array = [];
      
      public var errorArr:Array = [];
      
      public var au:String = "";
      
      public function AskDefine()
      {
         super();
      }
      
      public function inData_byXML(xml0:XML, father0:String) : void
      {
         var c0:XML = null;
         var e0:XML = null;
         this.father = father0;
         this.title = xml0.title;
         this.au = String(xml0.@author);
         if(xml0.correct.length() == 0)
         {
            INIT.showError("没有正确答案，AskDefine：" + this.title);
         }
         if(xml0.error.length() == 0)
         {
            INIT.showError("没有错误答案，AskDefine：" + this.title);
         }
         for each(c0 in xml0.correct)
         {
            this.correctArr.push(String(c0));
         }
         for each(e0 in xml0.error)
         {
            this.errorArr.push(String(e0));
         }
      }
      
      public function getAnswerArr() : Array
      {
         var correct0:String = this.correctArr[int(Math.random() * this.correctArr.length)];
         var errorArr0:Array = ComMethod.getRandomArray(this.errorArr,3);
         return [correct0].concat(errorArr0);
      }
      
      public function getId() : String
      {
         return this.father + "_" + this.index;
      }
      
      public function panAnswer(str0:String) : Boolean
      {
         return this.correctArr.indexOf(str0) >= 0;
      }
      
      public function getTitleAndAuthor() : String
      {
         var s0:String = this.title;
         if(this.au != "")
         {
            s0 += "\n" + ComMethod.color("（出题人：" + this.au + "）",GatherColor.graydarkColor,12);
         }
         return s0;
      }
      
      public function inSkillEffect(effect0:String, fatherCnName0:String, yesB0:Boolean) : void
      {
         this.title = "";
         this.title += "以下哪个" + fatherCnName0 + "";
         if(effect0.indexOf("瞬间释放") >= 0)
         {
            this.title += ComMethod.color((yesB0 ? "有概率" : "不会") + effect0,"#00FFFF");
         }
         else if(effect0.indexOf("伤害输出" || "弹药消耗") >= 0)
         {
            this.title += ComMethod.color((yesB0 ? "可" : "不可") + effect0,"#00FFFF");
         }
         else
         {
            this.title += ComMethod.color((yesB0 ? "具有" : "不具有") + effect0,"#00FFFF");
            this.title += effect0 == "群体" ? "效果" : "的效果";
         }
      }
      
      public function inSkillCd(d0:SkillDefine, fatherCnName0:String) : void
      {
         var name0:String = d0.cnName;
         if(d0 is HeroSkillDefine)
         {
            name0 += "(第" + (d0 as HeroSkillDefine).lv + "级)";
         }
         this.title = fatherCnName0 + ComMethod.color(name0,"#00FFFF") + "的技能冷却时间为";
         this.inAnswerByCd(d0.cd);
      }
      
      private function inAnswerByCd(cd0:int) : void
      {
         var v0:int = 0;
         this.correctArr.push(cd0 + "秒");
         var c0:int = Boolean(Math.random()) ? 1 : 2;
         for(var i:int = 0; i < 20; i++)
         {
            v0 = 0;
            if(i % 2 == 0)
            {
               v0 = cd0 - int(i / 2) * c0;
            }
            else
            {
               v0 = cd0 + int(i / 2) * c0;
            }
            if(v0 > 10 && v0 != cd0)
            {
               this.errorArr.push(v0 + "秒");
            }
         }
      }
      
      public function showText() : String
      {
         var str2:String = null;
         var n0:int = 0;
         var str3:String = null;
         var str0:String = "";
         str0 += this.title + "\n";
         for each(str2 in this.correctArr)
         {
            str0 += "【正确答案】：" + str2 + "\n";
         }
         n0 = 0;
         for each(str3 in this.errorArr)
         {
            n0++;
            str0 += "【" + n0 + "】：" + str3 + "\n";
         }
         return str0;
      }
   }
}

