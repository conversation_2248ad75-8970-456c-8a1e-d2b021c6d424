package dataAll._player.more
{
   import dataAll._app.partner.IO_PartnerEnemy;
   import dataAll._player.PlayerData;
   import dataAll.equip.add.IO_EquipAddGetter;
   import dataAll.equip.define.EquipType;
   
   public class MorePlayerData extends NormalPlayerData
   {
      
      public function MorePlayerData()
      {
         super();
      }
      
      override public function fleshReference(moreData0:MoreData) : void
      {
         super.fleshReference(moreData0);
      }
      
      override public function newDayCtrl(timeStr0:String) : void
      {
         super.newDayCtrl(timeStr0);
      }
      
      override public function inData_bySave(s0:NormalPlayerSave) : void
      {
         var save0:MorePlayerSave = s0 as MorePlayerSave;
         super.inData_bySave(save0);
         this.initSaveBySave(save0);
      }
      
      protected function initSaveBySave(save0:MorePlayerSave) : void
      {
         var n:* = undefined;
         var pro0:String = null;
         var obj0:Object = null;
         var funName0:String = null;
         var arr0:Array = save0.getInputNoDataNameArr();
         for(n in arr0)
         {
            pro0 = arr0[n];
            if(this.hasOwnProperty(pro0))
            {
               obj0 = this[pro0];
               funName0 = pro0.indexOf("Bag") > 0 ? "initBagSave" : "initSave";
               if(obj0.hasOwnProperty(funName0))
               {
                  obj0[funName0]();
               }
            }
         }
      }
      
      override public function initSave(heroData0:MoreData, firstLv0:int = 0) : void
      {
         base.save.level = firstLv0 > 0 ? firstLv0 : heroData0.def.moreD.firstLv;
         super.initSave(heroData0);
      }
      
      override public function getDpsWholeAdd() : Number
      {
         var mainPd0:PlayerData = getMainPlayerData();
         if(Boolean(mainPd0))
         {
            return mainPd0.getHeroMerge().dpsWhole;
         }
         return 0;
      }
      
      override public function getRoleDpsMul() : Number
      {
         return partner.getDpsMul();
      }
      
      override public function getRoleUnderHurtMul() : Number
      {
         return partner.getUnderHurtMul();
      }
      
      override public function getAllSkillLabelArr(equipB0:Boolean = true, skillB0:Boolean = true, mapMode0:String = "regular", skillCnRange0:Array = null, skillNumLimit0:int = -1) : Array
      {
         var arr0:Array = super.getAllSkillLabelArr(equipB0,skillB0,mapMode0,skillCnRange0,skillNumLimit0);
         if(equipB0)
         {
            arr0 = arr0.concat(love.getSkillLabelArr());
         }
         return arr0;
      }
      
      override public function getVehicleExtraSkillArr() : Array
      {
         var arr0:Array = super.getVehicleExtraSkillArr();
         return arr0.concat(love.getSkillLabelArr(EquipType.VEHICLE));
      }
      
      override public function getEquipAddGetter(name0:String) : IO_EquipAddGetter
      {
         if(this.hasOwnProperty(name0))
         {
            return this[name0];
         }
         return null;
      }
      
      public function killEnemy(b0:IO_PartnerEnemy) : void
      {
         partner.killEnemy(b0);
      }
      
      public function underHurt(hurt0:Number) : void
      {
         partner.underHurt(hurt0);
      }
   }
}

