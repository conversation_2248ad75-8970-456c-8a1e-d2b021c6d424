package dataAll._app.space.craft
{
   import com.sounto.cf.NiuBiCF;
   import com.sounto.cf.NumberEncodeObj;
   import com.sounto.utils.ClassProperty;
   import dataAll._base.OneSave;
   
   public class CraftSave extends OneSave
   {
      
      public static var pro_arr:Array = null;
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public var n:String = "";
      
      public var p0:NumberEncodeObj = new NumberEncodeObj();
      
      public var now:String = "0";
      
      public function CraftSave()
      {
         super();
         this.lv = 1;
      }
      
      public function get lv() : Number
      {
         return this.CF.getAttribute("lv");
      }
      
      public function set lv(v0:Number) : void
      {
         this.CF.setAttribute("lv",v0);
      }
      
      public function get exp() : Number
      {
         return this.CF.getAttribute("exp");
      }
      
      public function set exp(v0:Number) : void
      {
         this.CF.setAttribute("exp",v0);
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      public function getNow() : NumberEncodeObj
      {
         return this["p" + this.now];
      }
      
      public function getSkillIndexLv(name0:String) : int
      {
         return int(this.getNow().getAttribute(name0));
      }
   }
}

