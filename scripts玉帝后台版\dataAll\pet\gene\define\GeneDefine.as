package dataAll.pet.gene.define
{
   import com.common.text.TextWay;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.OldNiuBiCF;
   import com.sounto.utils.ClassProperty;
   import dataAll.body.define.NormalBodyDefine;
   import dataAll.gift.define.IO_GiftDefine;
   import dataAll.level.define.unit.UnitType;
   
   public class GeneDefine implements IO_GiftDefine
   {
      
      public static var pro_arr:Array = [];
      
      public static var catalystNameArr:Array = ["life","energy","variation"];
      
      public static var mulNameArr:Array = ["dps","life"];
      
      private var CF:OldNiuBiCF = new OldNiuBiCF();
      
      public var index:int = 0;
      
      public var father:String = "";
      
      public var name:String = "";
      
      public var cnName:String = "";
      
      public var type:String = "normal";
      
      public var bodyName:String = "";
      
      public var targetBodyName:String = "";
      
      public var secBodyName:String = "";
      
      public var iconUrl:String = "";
      
      public var lv:* = 1;
      
      public var dropNum:int = 120;
      
      public var dpsMul:Number = 1;
      
      public var lifeMul:Number = 1;
      
      public var defenceMul:Number = 1;
      
      public var talentSkillArr:Array = [];
      
      public var laterSkillArr:Array = [];
      
      public var talentProArr:Array = [];
      
      public var lowDispaArr:Array = [];
      
      public var highDispaArr:Array = [];
      
      public var superB:Boolean = false;
      
      public var evolutionLabel:String = "";
      
      private var _evolutionMustMul:String = "";
      
      public var beforeDefine:GeneDefine = null;
      
      public var firstDefine:GeneDefine = null;
      
      public function GeneDefine()
      {
         super();
         this.evolutionMustMul = "1,1,1";
         this.growMust = 0;
      }
      
      public function get growMust() : Number
      {
         return this.CF.getAttribute("growMust");
      }
      
      public function set growMust(v0:Number) : void
      {
         this.CF.setAttribute("growMust",v0);
      }
      
      public function get evolutionMustMul() : String
      {
         return TextWay.getText32(this._evolutionMustMul);
      }
      
      public function set evolutionMustMul(v0:String) : void
      {
         this._evolutionMustMul = TextWay.toCode32(v0);
      }
      
      public function inData_byXML(xml0:XML, father0:String) : void
      {
         ClassProperty.inData_byXMLAt(this,xml0,pro_arr);
         ClassProperty.inData_byXML(this,xml0,pro_arr);
         this.father = father0;
         if(this.bodyName == "")
         {
            this.bodyName = this.name;
         }
         if(this.iconUrl == "")
         {
            this.iconUrl = "ThingsIcon/" + this.name;
         }
         if(this.type == UnitType.BOSS)
         {
            this.dropNum = 4000;
         }
      }
      
      public function getBodyImgUrl() : String
      {
         var bodyD0:NormalBodyDefine = this.getBodyDefine();
         if(Boolean(bodyD0))
         {
            return bodyD0.bmpUrl;
         }
         return "";
      }
      
      public function isShowInBookB() : Boolean
      {
         return this.lv == 1;
      }
      
      public function getBodyDefine() : NormalBodyDefine
      {
         return Gaming.defineGroup.body.getDefine(this.bodyName);
      }
      
      public function getFirstName() : String
      {
         if(Boolean(this.firstDefine))
         {
            return this.firstDefine.name;
         }
         return "";
      }
      
      public function getBaseName() : String
      {
         if(Boolean(this.firstDefine))
         {
            return this.firstDefine.name;
         }
         return this.name;
      }
      
      public function getTrueCnName() : String
      {
         return this.cnName + "基因体";
      }
      
      public function getBarColorName() : String
      {
         if(this.type == UnitType.BOSS)
         {
            return "orange";
         }
         return "green";
      }
      
      public function getMul(name0:String) : Number
      {
         return this[name0 + "Mul"];
      }
      
      public function getMulUIPer(name0:String) : Number
      {
         var mul0:Number = this.getMul(name0) / 2;
         if(name0 == "life")
         {
            mul0 /= 4;
         }
         var v0:Number = 0;
         if(mul0 > 1)
         {
            v0 = (mul0 - 1) / 4 + 0.5;
         }
         else
         {
            v0 = mul0 / 2;
         }
         if(v0 > 1)
         {
            v0 = 1;
         }
         return v0;
      }
      
      public function isLastB() : Boolean
      {
         var first0:String = this.getFirstName();
         if(first0 == "PetLake")
         {
            return this.lv >= 3;
         }
         return this.lv >= 4;
      }
      
      public function canEvoB() : Boolean
      {
         return this.evolutionLabel != "";
      }
      
      public function getEvoGeneDefine() : GeneDefine
      {
         return Gaming.defineGroup.gene.getDefine(this.evolutionLabel);
      }
      
      public function getBookThingsName() : String
      {
         return this.firstDefine.name + "Book";
      }
      
      public function getEvoInfo() : String
      {
         var evoD0:GeneDefine = null;
         var evoBodyD0:NormalBodyDefine = null;
         var str0:String = "";
         str0 += ComMethod.color("下一阶进化：","#00FF00");
         if(this.evolutionLabel == "")
         {
            str0 += "\n无";
         }
         else
         {
            evoD0 = this.getEvoGeneDefine();
            evoBodyD0 = evoD0.getBodyDefine();
            str0 += "\n" + ComMethod.color(evoBodyD0.cnName,"#FFFF00");
         }
         return str0;
      }
      
      public function getMustStringArr(baseNum0:Number) : Array
      {
         var n:* = undefined;
         var thingsName0:String = null;
         var thingsStr0:String = null;
         var arr0:Array = [];
         var str0:String = this.evolutionMustMul;
         var strArr0:Array = this.evolutionMustMul.split(",");
         for(n in catalystNameArr)
         {
            thingsName0 = catalystNameArr[n] + "Catalyst";
            thingsStr0 = thingsName0 + ";" + Math.ceil(baseNum0 * Number(strArr0[n]));
            arr0.push(thingsStr0);
         }
         return arr0;
      }
      
      public function getGiftCn() : String
      {
         return this.cnName;
      }
      
      public function getGiftTip() : String
      {
         return "";
      }
      
      public function getGiftIconUrl() : String
      {
         return this.iconUrl;
      }
   }
}

